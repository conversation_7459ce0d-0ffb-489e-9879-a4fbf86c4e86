import { ApiResponse, ErrorCode } from '@cloud-storage/shared';
import { authService } from './authService';

const API_BASE_URL = (() => {
  try {
    return (import.meta as any).env?.VITE_API_URL || 'http://localhost:3001/api';
  } catch {
    return 'http://localhost:3001/api';
  }
})();

export class ApiService {
  private static instance: ApiService;
  private baseURL: string;

  private constructor() {
    this.baseURL = API_BASE_URL;
  }

  public static getInstance(): ApiService {
    if (!ApiService.instance) {
      ApiService.instance = new ApiService();
    }
    return ApiService.instance;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
    isFormData: boolean = false
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;

    const defaultHeaders: HeadersInit = {};
    
    // Only set Content-Type for non-FormData requests
    if (!isFormData) {
      defaultHeaders['Content-Type'] = 'application/json';
    }

    // Add auth token if available
    const token = localStorage.getItem('authToken');
    if (token) {
      defaultHeaders.Authorization = `Bearer ${token}`;
    }

    // Add device ID if available
    const deviceId = localStorage.getItem('deviceId');
    if (deviceId) {
      defaultHeaders['X-Device-Id'] = deviceId;
    }

    const config: RequestInit = {
      ...options,
      credentials: 'include',
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    };

    try {
      let response = await fetch(url, config);
      
      // Handle token expiration
      if (response.status === 401 && token && endpoint !== '/auth/refresh-token') {
        try {
          // Try to refresh the token
          const refreshResult = await authService.refreshToken();
          
          // Update the Authorization header with the new token
          const newConfig = {
            ...config,
            headers: {
              ...config.headers,
              Authorization: `Bearer ${refreshResult.accessToken}`
            }
          };
          
          // Retry the original request with the new token
          response = await fetch(url, newConfig);
        } catch (refreshError) {
          // If refresh fails, throw the original 401 error
          console.error('Token refresh failed:', refreshError);
          const errorData = await response.json().catch(() => ({}));
          throw new ApiError(
            response.status,
            errorData.error?.message || 'Authentication failed',
            'UNAUTHORIZED',
            errorData.error?.details
          );
        }
      }
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new ApiError(
          response.status,
          errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`,
          errorData.error?.code || 'HTTP_ERROR',
          errorData.error?.details
        );
      }

      const responseData = await response.json();
      
      // If the response is wrapped in an ApiResponse format, extract the data
      if (responseData && 'success' in responseData && 'data' in responseData) {
        const apiResponse = responseData as ApiResponse<T>;
        return apiResponse.data as T;
      }
      
      return responseData;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      
      // Network or other errors
      throw new ApiError(
        0,
        error instanceof Error ? error.message : 'Network error occurred',
        'NETWORK_ERROR'
      );
    }
  }

  public async get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  public async post<T>(endpoint: string, data?: any): Promise<T> {
    const isFormData = data instanceof FormData;
    return this.request<T>(endpoint, {
      method: 'POST',
      body: isFormData ? data : (data ? JSON.stringify(data) : undefined),
    }, isFormData);
  }

  public async put<T>(endpoint: string, data?: any): Promise<T> {
    const isFormData = data instanceof FormData;
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: isFormData ? data : (data ? JSON.stringify(data) : undefined),
    }, isFormData);
  }

  public async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }
}

export class ApiError extends Error {
  public readonly status: number;
  public readonly code: string;
  public readonly details?: any;

  constructor(status: number, message: string, code: string, details?: any) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.code = code;
    this.details = details;
  }

  public isValidationError(): boolean {
    return this.code === 'VALIDATION_ERROR' || this.status === 400;
  }

  public isAuthError(): boolean {
    return this.status === 401 || this.code === 'UNAUTHORIZED';
  }

  public isNetworkError(): boolean {
    return this.code === 'NETWORK_ERROR' || this.status === 0;
  }
}

export const apiService = ApiService.getInstance();