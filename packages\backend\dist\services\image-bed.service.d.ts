export interface ImageBedFolder {
    id: string;
    name: string;
    isImageBed: boolean;
    userId: string;
    createdAt: Date;
    modifiedAt: Date;
}
export interface PublicImageInfo {
    id: string;
    originalName: string;
    publicUrl: string;
    cdnUrl?: string;
    thumbnails?: {
        small?: string;
        medium?: string;
        large?: string;
    };
    accessCount: number;
    createdAt: Date;
}
declare class ImageBedService {
    private readonly IMAGE_BED_FOLDER_NAME;
    /**
     * Get or create the image bed folder for a user
     * Ensures only one image bed folder exists per user
     */
    getOrCreateImageBedFolder(userId: string): Promise<ImageBedFolder>;
    /**
     * Find existing image bed folder (only in root directory)
     */
    private findImageBedFolder;
    /**
     * Clean up duplicate image bed folders in ROOT directory only
     * Note: "Image Bed" folders in subdirectories are allowed as regular folders
     */
    private cleanupDuplicateImageBedFolders;
    /**
     * Create image bed folder (strictly in root directory only)
     */
    private createImageBedFolder;
    /**
     * Upload image directly to image bed
     */
    uploadToImageBed(req: any, userId: string): Promise<PublicImageInfo>;
    /**
     * Move an image file to the image bed
     */
    moveImageToImageBed(fileId: string, userId: string): Promise<PublicImageInfo>;
    /**
     * Generate public URL for an image
     */
    private generatePublicUrl;
    /**
     * Get public image information
     */
    getPublicImageInfo(imageId: string): Promise<PublicImageInfo>;
    /**
     * List images in the image bed
     */
    listImageBedImages(userId: string, options?: {
        page?: number;
        limit?: number;
        sortBy?: 'name' | 'createdAt' | 'accessCount';
        sortOrder?: 'asc' | 'desc';
    }): Promise<{
        images: PublicImageInfo[];
        total: number;
        page: number;
        limit: number;
    }>;
    /**
     * Remove image from image bed
     */
    removeFromImageBed(imageId: string, userId: string, targetFolderId?: string): Promise<void>;
    /**
     * Validate that a folder is a valid image bed folder
     */
    private validateImageBedFolder;
    /**
     * Map folder to image bed folder with validation
     */
    private mapFolderToImageBedFolder;
    /**
     * Public method to clean up duplicate image bed folders
     */
    forceCleanupImageBedFolders(userId: string): Promise<void>;
}
export declare const imageBedService: ImageBedService;
export {};
//# sourceMappingURL=image-bed.service.d.ts.map