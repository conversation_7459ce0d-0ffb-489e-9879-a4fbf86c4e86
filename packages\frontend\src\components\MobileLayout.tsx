import React, { useState, useEffect, useRef } from 'react'
import { useSync } from '../contexts/SyncContext'
import { useMobile } from '../hooks/useMobile'
import { useGestures } from '../hooks/useGestures'
import { cn } from '../utils/cn'
import {
  Bars3Icon,
  XMarkIcon,
  MagnifyingGlassIcon,
  BellIcon,
  UserIcon,
  HomeIcon,
  FolderIcon,
  CloudArrowUpIcon,
  ShareIcon,
  TrashIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline'
import { SyncStatusIndicator } from './sync'
import ThemeLanguageToggle from './ThemeLanguageToggle'

interface MobileLayoutProps {
  children: React.ReactNode
}

const MobileLayout: React.FC<MobileLayoutProps> = ({ children }) => {
  const { conflicts, isOnline } = useSync()
  const { orientation, isMobile } = useMobile()
  const [showSidebar, setShowSidebar] = useState(false)
  const [activeTab, setActiveTab] = useState('home')
  const [showSearch, setShowSearch] = useState(false)
  const [sidebarSwipeOffset, setSidebarSwipeOffset] = useState(0)
  const sidebarRef = useRef<HTMLDivElement>(null)

  const navItems = [
    { icon: HomeIcon, label: 'Home', href: '/', id: 'home' },
    { icon: FolderIcon, label: 'Files', href: '/files', id: 'files' },
    { icon: CloudArrowUpIcon, label: 'Upload', href: '/upload', id: 'upload' },
    { icon: ShareIcon, label: 'Shared', href: '/shared', id: 'shared' },
    { icon: TrashIcon, label: 'Trash', href: '/trash', id: 'trash' },
    { icon: Cog6ToothIcon, label: 'Settings', href: '/settings', id: 'settings' }
  ]

  const bottomNavItems = navItems.slice(0, 5) // Show first 5 items in bottom nav

  // Enhanced gesture handling for sidebar
  const { attachGestures: attachSidebarGestures } = useGestures({
    onSwipeLeft: () => {
      if (showSidebar) {
        setShowSidebar(false)
        setSidebarSwipeOffset(0)
      }
    },
    onSwipeRight: () => {
      if (!showSidebar && sidebarSwipeOffset === 0) {
        setShowSidebar(true)
      }
    }
  }, {
    swipeThreshold: 30,
    preventScroll: false
  })

  // Edge swipe gesture for opening sidebar
  const { attachGestures: attachEdgeGestures } = useGestures({
    onSwipeRight: () => {
      if (!showSidebar) {
        setShowSidebar(true)
      }
    }
  }, {
    swipeThreshold: 20
  })

  // Close sidebar when orientation changes
  useEffect(() => {
    setShowSidebar(false)
    setSidebarSwipeOffset(0)
  }, [orientation])

  // Attach edge swipe gesture to main content
  useEffect(() => {
    if (isMobile && !showSidebar) {
      const edgeZone = document.createElement('div')
      edgeZone.style.position = 'fixed'
      edgeZone.style.left = '0'
      edgeZone.style.top = '0'
      edgeZone.style.width = '20px'
      edgeZone.style.height = '100vh'
      edgeZone.style.zIndex = '30'
      edgeZone.style.pointerEvents = 'auto'

      document.body.appendChild(edgeZone)
      const cleanup = attachEdgeGestures(edgeZone)

      return () => {
        cleanup?.()
        document.body.removeChild(edgeZone)
      }
    }
  }, [isMobile, showSidebar, attachEdgeGestures])

  // Attach sidebar gestures
  useEffect(() => {
    if (showSidebar && sidebarRef.current) {
      const cleanup = attachSidebarGestures(sidebarRef.current)
      return cleanup
    }
  }, [showSidebar, attachSidebarGestures])

  // Close sidebar when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: Event) => {
      if (showSidebar && !(event.target as Element).closest('.mobile-sidebar')) {
        setShowSidebar(false)
      }
    }

    if (showSidebar) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('touchstart', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('touchstart', handleClickOutside)
    }
  }, [showSidebar])

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId)
    // Handle navigation here
    window.location.href = navItems.find(item => item.id === tabId)?.href || '/'
  }

  return (
    <div className="h-full bg-dracula-bg flex flex-col">
      {/* Mobile Header */}
      <header className="bg-dracula-current-line border-b border-dracula-comment px-4 py-3 sticky top-0 z-40">
        <div className="flex items-center justify-between">
          {/* Left Section */}
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowSidebar(true)}
              className="p-2 hover:bg-dracula-bg rounded-lg transition-colors"
            >
              <Bars3Icon className="w-6 h-6 text-dracula-foreground" />
            </button>

            {/* <h1 className="text-lg font-bold text-dracula-foreground truncate">
              Cloud Storage
            </h1> */}
          </div>

          {/* Right Section */}
          <div className="flex items-center space-x-1">
            {/* Sync Status */}
            <div className="relative">
              <SyncStatusIndicator showDetails={false} />
              {(conflicts.length > 0 || !isOnline) && (
                <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
              )}
            </div>

            {/* Search */}
            <button
              onClick={() => setShowSearch(true)}
              className="p-2 hover:bg-dracula-bg rounded-lg transition-colors"
            >
              <MagnifyingGlassIcon className="w-5 h-5 text-dracula-foreground" />
            </button>

            {/* Notifications */}
            <button className="p-2 hover:bg-dracula-bg rounded-lg transition-colors relative">
              <BellIcon className="w-5 h-5 text-dracula-foreground" />
              <span className="absolute -top-1 -right-1 w-3 h-3 bg-dracula-red rounded-full"></span>
            </button>
            {/* 主题和语言切换按钮 */}
            <ThemeLanguageToggle />
          </div>
        </div>
      </header>

      {/* Search Overlay */}
      {showSearch && (
        <div className="fixed inset-0 bg-black/50 z-50 flex flex-col">
          <div className="bg-dracula-bg p-4">
            <div className="flex items-center space-x-3">
              <div className="flex-1 relative">
                <MagnifyingGlassIcon className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-dracula-comment" />
                <input
                  type="text"
                  placeholder="Search files and folders..."
                  className="w-full pl-10 pr-4 py-3 bg-dracula-current-line border border-dracula-comment rounded-lg text-dracula-foreground placeholder-dracula-comment focus:outline-none focus:border-dracula-purple"
                  autoFocus
                />
              </div>
              <button
                onClick={() => setShowSearch(false)}
                className="p-2 text-dracula-purple font-medium"
              >
                Cancel
              </button>
            </div>
          </div>

          {/* Search Results */}
          <div className="flex-1 bg-dracula-bg p-4">
            <div className="text-center text-dracula-comment mt-8">
              <MagnifyingGlassIcon className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>Start typing to search your files</p>
            </div>
          </div>
        </div>
      )}

      {/* Sidebar Overlay */}
      {showSidebar && (
        <div className="fixed inset-0 bg-black/50 z-50 flex">
          {/* Sidebar */}
          <div
            ref={sidebarRef}
            className="mobile-sidebar w-80 max-w-[85vw] bg-dracula-bg shadow-xl flex flex-col transform transition-transform duration-300 ease-out"
            style={{
              transform: `translateX(${sidebarSwipeOffset}px)`
            }}
          >
            {/* Sidebar Header */}
            <div className="flex items-center justify-between p-4 border-b border-dracula-current-line">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-dracula-purple rounded-full flex items-center justify-center">
                  <UserIcon className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-dracula-foreground font-medium">John Doe</h3>
                  <p className="text-sm text-dracula-comment"><EMAIL></p>
                </div>
              </div>
              <button
                onClick={() => setShowSidebar(false)}
                className="p-2 hover:bg-dracula-current-line rounded-lg transition-colors"
              >
                <XMarkIcon className="w-6 h-6 text-dracula-foreground" />
              </button>
            </div>

            {/* Navigation */}
            <nav className="flex-1 p-4 space-y-2">
              {navItems.map((item) => {
                const Icon = item.icon
                const isActive = activeTab === item.id

                return (
                  <a
                    key={item.id}
                    href={item.href}
                    onClick={(e) => {
                      e.preventDefault()
                      handleTabChange(item.id)
                      setShowSidebar(false)
                    }}
                    className={cn(
                      'flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors',
                      isActive
                        ? 'bg-dracula-purple text-white'
                        : 'text-dracula-foreground hover:bg-dracula-current-line'
                    )}
                  >
                    <Icon className="w-6 h-6" />
                    <span className="font-medium">{item.label}</span>
                  </a>
                )
              })}
            </nav>

            {/* Storage Info */}
            <div className="p-4 border-t border-dracula-current-line">
              <div className="text-sm text-dracula-comment mb-2">Storage Used</div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-dracula-foreground">2.4 GB</span>
                <span className="text-dracula-comment">of 15 GB</span>
              </div>
              <div className="w-full bg-dracula-current-line rounded-full h-2">
                <div className="bg-dracula-purple h-2 rounded-full" style={{ width: '16%' }}></div>
              </div>
            </div>
          </div>

          {/* Backdrop */}
          <div
            className="flex-1"
            onClick={() => setShowSidebar(false)}
          />
        </div>
      )}

      {/* Main Content */}
      <main className="flex-1 pb-20 overflow-auto">
        {children}
      </main>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-dracula-current-line border-t border-dracula-comment z-30">
        <div className="flex items-center justify-around py-2">
          {bottomNavItems.map((item) => {
            const Icon = item.icon
            const isActive = activeTab === item.id

            return (
              <button
                key={item.id}
                onClick={() => handleTabChange(item.id)}
                className={cn(
                  'flex flex-col items-center justify-center p-2 min-w-0 flex-1 transition-colors',
                  isActive
                    ? 'text-dracula-purple'
                    : 'text-dracula-comment hover:text-dracula-foreground'
                )}
              >
                <Icon className={cn('w-6 h-6 mb-1', isActive && 'text-dracula-purple')} />
                <span className="text-xs font-medium truncate">{item.label}</span>
              </button>
            )
          })}
        </div>
      </div>

      {/* Safe Area for devices with notches */}
      <div className="h-safe-area-inset-bottom bg-dracula-current-line"></div>
    </div>
  )
}

export default MobileLayout