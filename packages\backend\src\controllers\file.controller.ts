import { Request, Response } from 'express';
import { fileService } from '../services/file.service';
import { storageService } from '../services/storage.service';
import { FileUpload, SearchQuery, Pagination, FolderData, ShareOptions } from '@cloud-storage/shared';
import logger from '../utils/logger';
import multer from 'multer';
import * as crypto from 'crypto';

// File type validation configuration
const ALLOWED_FILE_TYPES = {
  'image/jpeg': ['.jpg', '.jpeg'],
  'image/png': ['.png'],
  'image/gif': ['.gif'],
  'image/webp': ['.webp'],
  'text/plain': ['.txt'],
  'application/pdf': ['.pdf'],
  'application/msword': ['.doc'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
  'application/vnd.ms-excel': ['.xls'],
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
  'application/zip': ['.zip'],
  'application/x-rar-compressed': ['.rar'],
  'video/mp4': ['.mp4'],
  'video/avi': ['.avi'],
  'video/quicktime': ['.mov'],
  'audio/mpeg': ['.mp3'],
  'audio/wav': ['.wav']
};

const MAX_FILE_SIZE = 500 * 1024 * 1024; // 500MB
const MAX_FILES_BATCH = 20;

// Enhanced file filter with type validation
const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  const isAllowed = Object.keys(ALLOWED_FILE_TYPES).includes(file.mimetype);
  
  if (!isAllowed) {
    const error = new Error(`File type ${file.mimetype} is not allowed`);
    error.name = 'INVALID_FILE_TYPE';
    return cb(error);
  }
  
  cb(null, true);
};

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: MAX_FILE_SIZE,
    files: MAX_FILES_BATCH
  },
  fileFilter
});

// Configure multer for chunked uploads
const uploadChunked = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB per chunk
    files: 1
  }
});

export class FileController {
  
  // Upload single file with enhanced validation and progress tracking
  async uploadFile(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      if (!req.file) {
        res.status(400).json({ error: 'No file provided' });
        return;
      }

      // Validate file size
      if (req.file.size > MAX_FILE_SIZE) {
        res.status(413).json({ 
          error: 'File too large',
          maxSize: MAX_FILE_SIZE,
          actualSize: req.file.size
        });
        return;
      }

      // Check user storage quota
      const storageStats = await fileService.getStorageStats(userId);
      if (storageStats.totalSize + req.file.size > storageStats.storageQuota) {
        res.status(413).json({ 
          error: 'Storage quota exceeded',
          available: storageStats.storageQuota - storageStats.totalSize,
          required: req.file.size
        });
        return;
      }

      // Check if uploading to image bed folder - only allow images
      if (req.body.folderId) {
        const folder = await fileService.getFolderById(req.body.folderId);
        if (folder && folder.name === 'Image Bed' && !folder.parentId) {
          // This is the image bed folder - only allow image files
          if (!req.file.mimetype.startsWith('image/')) {
            res.status(400).json({ 
              error: 'Image bed folder only accepts image files',
              allowedTypes: 'image/*'
            });
            return;
          }
        }
      }

      const fileUpload: FileUpload = {
        file: req.file.buffer,
        filename: req.file.originalname,
        mimeType: req.file.mimetype,
        folderId: req.body.folderId,
        tags: req.body.tags ? JSON.parse(req.body.tags) : []
      };

      const fileInfo = await fileService.uploadFile(userId, fileUpload, {
        isPublic: req.body.isPublic === 'true'
      });

      // Notify other clients via WebSocket
      if (req.wsService) {
        req.wsService.sendToUser(userId, {
          type: 'file_uploaded',
          payload: {
            fileId: fileInfo.id,
            fileName: fileInfo.filename,
            folderId: req.body.folderId,
            size: fileInfo.size
          },
          timestamp: new Date()
        });
      }

      res.status(201).json({
        success: true,
        data: fileInfo,
        message: 'File uploaded successfully'
      });
    } catch (error) {
      logger.error('File upload error:', error);
      
      if (error instanceof Error) {
        if (error.name === 'INVALID_FILE_TYPE') {
          res.status(400).json({ 
            error: 'Invalid file type',
            message: error.message,
            allowedTypes: Object.keys(ALLOWED_FILE_TYPES)
          });
          return;
        }
        
        if (error.message.includes('quota')) {
          res.status(413).json({ 
            error: 'Storage quota exceeded',
            message: error.message
          });
          return;
        }

        // 存储服务相关错误
        if (error.message.includes('storage node') || error.message.includes('MinIO') || error.message.includes('ECONNREFUSED')) {
          res.status(503).json({ 
            error: 'Storage service unavailable',
            message: 'File storage service is temporarily unavailable. Please try again later.',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
          });
          return;
        }

        // 数据库相关错误
        if (error.message.includes('database') || error.message.includes('connection')) {
          res.status(503).json({ 
            error: 'Database service unavailable',
            message: 'Database service is temporarily unavailable. Please try again later.',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
          });
          return;
        }
      }

      res.status(500).json({ 
        error: 'File upload failed',
        message: error instanceof Error ? error.message : 'Unknown error',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Upload multiple files with enhanced validation
  async uploadFiles(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
        res.status(400).json({ error: 'No files provided' });
        return;
      }

      if (req.files.length > MAX_FILES_BATCH) {
        res.status(400).json({ 
          error: 'Too many files',
          maxFiles: MAX_FILES_BATCH,
          provided: req.files.length
        });
        return;
      }

      // Calculate total size
      const totalSize = req.files.reduce((sum, file) => sum + file.size, 0);
      
      // Check storage quota
      const storageStats = await fileService.getStorageStats(userId);
      if (storageStats.totalSize + totalSize > storageStats.storageQuota) {
        res.status(413).json({ 
          error: 'Storage quota exceeded',
          available: storageStats.storageQuota - storageStats.totalSize,
          required: totalSize
        });
        return;
      }

      const fileUploads: FileUpload[] = req.files.map((file: Express.Multer.File) => ({
        file: file.buffer,
        filename: file.originalname,
        mimeType: file.mimetype,
        folderId: req.body.folderId,
        tags: req.body.tags ? JSON.parse(req.body.tags) : []
      }));

      const results = await fileService.uploadFiles(userId, fileUploads);

      res.status(201).json({
        success: true,
        data: results,
        uploaded: results.length,
        total: fileUploads.length,
        message: `Successfully uploaded ${results.length} of ${fileUploads.length} files`
      });
    } catch (error) {
      logger.error('Batch file upload error:', error);
      res.status(500).json({ 
        error: 'Batch file upload failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // Initialize chunked upload for large files
  async initChunkedUpload(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const { filename, fileSize, mimeType, totalChunks } = req.body;

      if (!filename || !fileSize || !mimeType || !totalChunks) {
        res.status(400).json({ error: 'Missing required parameters' });
        return;
      }

      // Validate file type
      if (!Object.keys(ALLOWED_FILE_TYPES).includes(mimeType)) {
        res.status(400).json({ 
          error: 'Invalid file type',
          allowedTypes: Object.keys(ALLOWED_FILE_TYPES)
        });
        return;
      }

      // Check storage quota
      const storageStats = await fileService.getStorageStats(userId);
      if (storageStats.totalSize + fileSize > storageStats.storageQuota) {
        res.status(413).json({ 
          error: 'Storage quota exceeded',
          available: storageStats.storageQuota - storageStats.totalSize,
          required: fileSize
        });
        return;
      }

      const uploadId = await fileService.initChunkedUpload(userId, {
        filename,
        fileSize,
        mimeType,
        totalChunks,
        folderId: req.body.folderId,
        tags: req.body.tags || []
      });

      res.status(201).json({
        success: true,
        uploadId,
        message: 'Chunked upload initialized'
      });
    } catch (error) {
      logger.error('Init chunked upload error:', error);
      res.status(500).json({ 
        error: 'Failed to initialize chunked upload',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // Upload file chunk
  async uploadChunk(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const { uploadId } = req.params;
      const { chunkIndex } = req.body;

      if (!req.file) {
        res.status(400).json({ error: 'No chunk data provided' });
        return;
      }

      if (chunkIndex === undefined || chunkIndex === null) {
        res.status(400).json({ error: 'Chunk index is required' });
        return;
      }

      const result = await fileService.uploadChunk(uploadId, {
        chunkIndex: parseInt(chunkIndex),
        chunkData: req.file.buffer,
        chunkSize: req.file.size
      });

      res.json({
        success: true,
        chunkIndex: result.chunkIndex,
        uploaded: result.uploaded,
        total: result.total,
        progress: result.progress,
        message: `Chunk ${result.chunkIndex + 1} uploaded successfully`
      });
    } catch (error) {
      logger.error('Upload chunk error:', error);
      res.status(500).json({ 
        error: 'Failed to upload chunk',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // Complete chunked upload
  async completeChunkedUpload(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const { uploadId } = req.params;
      const fileInfo = await fileService.completeChunkedUpload(uploadId, userId);

      res.json({
        success: true,
        data: fileInfo,
        message: 'File upload completed successfully'
      });
    } catch (error) {
      logger.error('Complete chunked upload error:', error);
      res.status(500).json({ 
        error: 'Failed to complete chunked upload',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // Get upload progress
  async getUploadProgress(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const { uploadId } = req.params;
      const progress = await fileService.getUploadProgress(uploadId, userId);

      res.json({
        success: true,
        data: progress
      });
    } catch (error) {
      logger.error('Get upload progress error:', error);
      res.status(500).json({ 
        error: 'Failed to get upload progress',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // Download file
  async downloadFile(req: Request, res: Response): Promise<void> {
    try {
      const { fileId } = req.params;
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const fileStream = await fileService.downloadFile(fileId, userId);

      // Set appropriate headers
      res.setHeader('Content-Type', fileStream.metadata.mimeType);
      res.setHeader('Content-Length', fileStream.metadata.size);
      res.setHeader('Content-Disposition', `attachment; filename="${fileStream.metadata.originalName}"`);

      // Pipe the stream to response
      fileStream.stream.pipe(res);
    } catch (error) {
      logger.error('File download error:', error);
      if (error instanceof Error && error.message === 'File not found') {
        res.status(404).json({ error: 'File not found' });
      } else if (error instanceof Error && error.message === 'Access denied') {
        res.status(403).json({ error: 'Access denied' });
      } else {
        res.status(500).json({ 
          error: 'File download failed',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  // Preview file (optimized for web display)
  async previewFile(req: Request, res: Response): Promise<void> {
    try {
      const { fileId } = req.params;
      const userId = req.user?.id;
      const { quality = 'medium', maxWidth, maxHeight } = req.query;

      logger.info(`Preview request - fileId: ${fileId}, userId: ${userId}`);

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      if (!fileId) {
        res.status(400).json({ error: 'File ID is required' });
        return;
      }

      // Validate UUID format
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(fileId)) {
        logger.error(`Invalid UUID format: ${fileId}`);
        res.status(400).json({ error: 'Invalid file ID format' });
        return;
      }

      const previewStream = await fileService.getFilePreview(fileId, userId, {
        quality: quality as string,
        maxWidth: maxWidth ? parseInt(maxWidth as string) : undefined,
        maxHeight: maxHeight ? parseInt(maxHeight as string) : undefined
      });

      // Set appropriate headers for preview (inline display)
      res.setHeader('Content-Type', previewStream.metadata.mimeType);
      res.setHeader('Content-Length', previewStream.metadata.size);
      res.setHeader('Content-Disposition', `inline; filename="${previewStream.metadata.originalName}"`);
      res.setHeader('Cache-Control', 'public, max-age=3600'); // Cache for 1 hour

      // Pipe the stream to response
      previewStream.stream.pipe(res);
    } catch (error) {
      logger.error('File preview error:', error);
      if (error instanceof Error && error.message === 'File not found') {
        res.status(404).json({ error: 'File not found' });
      } else if (error instanceof Error && error.message === 'Access denied') {
        res.status(403).json({ error: 'Access denied' });
      } else if (error instanceof Error && error.message === 'Preview not supported') {
        res.status(415).json({ error: 'Preview not supported for this file type' });
      } else {
        res.status(500).json({ 
          error: 'File preview failed',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  // List files and folders
  async listFiles(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const folderId = req.query.folderId as string;
      const pagination: Pagination = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 50,
        sortBy: req.query.sortBy as string,
        sortOrder: (req.query.sortOrder as 'asc' | 'desc') || 'desc',
        fileType: req.query.fileType as string
      };

      const fileList = await fileService.listFiles(userId, folderId, pagination);

      res.json({
        success: true,
        data: fileList
      });
    } catch (error) {
      logger.error('List files error:', error);
      res.status(500).json({ 
        error: 'Failed to list files',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // Search files
  async searchFiles(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const searchQuery: SearchQuery = {
        query: req.query.q as string || '',
        fileType: req.query.fileType as string,
        folderId: req.query.folderId as string,
        tags: req.query.tags ? (req.query.tags as string).split(',') : undefined,
        dateRange: req.query.from && req.query.to ? {
          from: new Date(req.query.from as string),
          to: new Date(req.query.to as string)
        } : undefined
      };

      const results = await fileService.searchFiles(userId, searchQuery);

      res.json({
        success: true,
        data: results
      });
    } catch (error) {
      logger.error('File search error:', error);
      res.status(500).json({ 
        error: 'File search failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // Create folder
  async createFolder(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const folderData: FolderData = {
        name: req.body.name,
        parentId: req.body.parentId
      };

      if (!folderData.name) {
        res.status(400).json({ error: 'Folder name is required' });
        return;
      }

      // Prevent creating "Image Bed" folder in root directory (reserved for system)
      const isInRootDirectory = !folderData.parentId;
      const imageBedNames = ['Image Bed', '图床'];
      
      if (isInRootDirectory && imageBedNames.includes(folderData.name.trim())) {
        res.status(400).json({ 
          error: 'The name "Image Bed" is reserved for the system image bed folder' 
        });
        return;
      }

      // Prevent creating any folders inside the image bed folder
      if (folderData.parentId) {
        const parentFolder = await fileService.getFolderById(folderData.parentId);
        if (parentFolder && parentFolder.name === 'Image Bed' && !parentFolder.parentId) {
          res.status(400).json({ 
            error: 'Cannot create folders inside the image bed folder' 
          });
          return;
        }
      }

      const folder = await fileService.createFolder(userId, folderData);

      // Notify other clients via WebSocket
      if (req.wsService) {
        req.wsService.sendToUser(userId, {
          type: 'folder_created',
          payload: {
            folderId: folder.id,
            folderName: folder.name,
            parentFolderId: folder.parentId
          },
          timestamp: new Date()
        });
      }

      res.status(201).json({
        success: true,
        data: folder
      });
    } catch (error) {
      logger.error('Create folder error:', error);
      res.status(500).json({ 
        error: 'Failed to create folder',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // Move file
  async moveFile(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const { fileId } = req.params;
      const { targetFolderId } = req.body;

      // Get file info before move for WebSocket notification
      const fileInfo = await fileService.getFileInfo(fileId, userId);

      await fileService.moveFile(fileId, targetFolderId, userId);

      // Notify other clients via WebSocket
      if (req.wsService && fileInfo) {
        req.wsService.sendToUser(userId, {
          type: 'file_moved',
          payload: {
            fileId: fileInfo.id,
            fileName: fileInfo.originalName,
            oldFolderId: fileInfo.folderId,
            newFolderId: targetFolderId
          },
          timestamp: new Date()
        });
      }

      res.json({
        success: true,
        message: 'File moved successfully'
      });
    } catch (error) {
      logger.error('Move file error:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({ error: error.message });
      } else if (error instanceof Error && error.message.includes('access denied')) {
        res.status(403).json({ error: error.message });
      } else {
        res.status(500).json({
          error: 'Failed to move file',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  // Delete file
  async deleteFile(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const { fileId } = req.params;

      // Get file info before deletion for WebSocket notification
      const fileInfo = await fileService.getFileInfo(fileId, userId);

      await fileService.deleteFile(fileId, userId);

      // Notify other clients via WebSocket
      if (req.wsService && fileInfo) {
        req.wsService.sendToUser(userId, {
          type: 'file_deleted',
          payload: {
            fileId: fileInfo.id,
            fileName: fileInfo.originalName,
            folderId: fileInfo.folderId
          },
          timestamp: new Date()
        });
      }

      res.json({
        success: true,
        message: 'File deleted successfully'
      });
    } catch (error) {
      logger.error('Delete file error:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({ error: error.message });
      } else if (error instanceof Error && error.message.includes('access denied')) {
        res.status(403).json({ error: error.message });
      } else {
        res.status(500).json({ 
          error: 'Failed to delete file',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  // Share file
  async shareFile(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const { fileId } = req.params;
      const shareOptions: ShareOptions = {
        permissions: req.body.permissions || [{ type: 'read', granted: true }],
        expiresAt: req.body.expiresAt ? new Date(req.body.expiresAt) : undefined,
        password: req.body.password,
        maxDownloads: req.body.maxDownloads
      };

      const shareLink = await fileService.shareFile(fileId, userId, shareOptions);

      res.status(201).json({
        success: true,
        data: shareLink
      });
    } catch (error) {
      logger.error('Share file error:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({ error: error.message });
      } else if (error instanceof Error && error.message.includes('access denied')) {
        res.status(403).json({ error: error.message });
      } else {
        res.status(500).json({ 
          error: 'Failed to share file',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  // Get storage statistics
  async getStorageStats(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const [userStats, systemStats] = await Promise.all([
        fileService.getStorageStats(userId),
        storageService.getStorageStats()
      ]);

      res.json({
        success: true,
        data: {
          user: userStats,
          system: systemStats
        }
      });
    } catch (error) {
      logger.error('Get storage stats error:', error);
      res.status(500).json({ 
        error: 'Failed to get storage statistics',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // Get file info
  async getFileInfo(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const { fileId } = req.params;
      const fileInfo = await fileService.getFileInfo(fileId, userId);

      res.json({
        success: true,
        data: fileInfo
      });
    } catch (error) {
      logger.error('Get file info error:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({ error: error.message });
      } else if (error instanceof Error && error.message.includes('access denied')) {
        res.status(403).json({ error: error.message });
      } else {
        res.status(500).json({ 
          error: 'Failed to get file info',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  // Get folder by ID
  async getFolderById(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const { folderId } = req.params;
      const folder = await fileService.getFolderById(folderId);

      if (!folder) {
        res.status(404).json({ error: 'Folder not found' });
        return;
      }

      // Check if user has access to this folder
      if (folder.userId !== userId) {
        res.status(403).json({ error: 'Access denied' });
        return;
      }

      res.json({
        success: true,
        data: folder
      });
    } catch (error) {
      logger.error('Get folder by ID error:', error);
      res.status(500).json({ error: 'Failed to get folder' });
    }
  }

  // Get folder info
  async getFolderInfo(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const { folderId } = req.params;
      const folderInfo = await fileService.getFolderInfo(folderId, userId);

      res.json({
        success: true,
        data: folderInfo
      });
    } catch (error) {
      logger.error('Get folder info error:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({ error: error.message });
      } else if (error instanceof Error && error.message.includes('access denied')) {
        res.status(403).json({ error: error.message });
      } else {
        res.status(500).json({ error: 'Failed to get folder info' });
      }
    }
  }

  // Rename folder
  async renameFolder(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const { folderId } = req.params;
      const { name } = req.body;

      if (!name || name.trim().length === 0) {
        res.status(400).json({ error: 'Folder name is required' });
        return;
      }

      await fileService.renameFolder(folderId, name.trim(), userId);

      res.json({
        success: true,
        message: 'Folder renamed successfully'
      });
    } catch (error) {
      logger.error('Rename folder error:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({ error: error.message });
      } else if (error instanceof Error && error.message.includes('access denied')) {
        res.status(403).json({ error: error.message });
      } else if (error instanceof Error && error.message.includes('already exists')) {
        res.status(409).json({ error: error.message });
      } else {
        res.status(500).json({ 
          error: 'Failed to rename folder',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  // Move folder
  async moveFolder(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const { folderId } = req.params;
      const { targetFolderId } = req.body;

      // Get folder info before move for WebSocket notification
      const folderInfo = await fileService.getFolderInfo(folderId, userId);

      await fileService.moveFolder(folderId, targetFolderId, userId);

      // Notify other clients via WebSocket
      if (req.wsService && folderInfo) {
        req.wsService.sendToUser(userId, {
          type: 'folder_moved',
          payload: {
            folderId: folderInfo.id,
            folderName: folderInfo.name,
            oldParentId: folderInfo.parentId,
            newParentId: targetFolderId
          },
          timestamp: new Date()
        });
      }

      res.json({
        success: true,
        message: 'Folder moved successfully'
      });
    } catch (error) {
      logger.error('Move folder error:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({ error: error.message });
      } else if (error instanceof Error && error.message.includes('access denied')) {
        res.status(403).json({ error: error.message });
      } else if (error instanceof Error && error.message.includes('circular')) {
        res.status(400).json({ error: error.message });
      } else {
        res.status(500).json({
          error: 'Failed to move folder',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  // Delete folder
  async deleteFolder(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const { folderId } = req.params;
      const { permanent } = req.query;

      if (permanent === 'true') {
        await fileService.deleteFolderPermanently(folderId, userId);
        res.json({
          success: true,
          message: 'Folder permanently deleted'
        });
      } else {
        await fileService.deleteFolderToTrash(folderId, userId);
        res.json({
          success: true,
          message: 'Folder moved to trash'
        });
      }
    } catch (error) {
      logger.error('Delete folder error:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({ error: error.message });
      } else if (error instanceof Error && error.message.includes('access denied')) {
        res.status(403).json({ error: error.message });
      } else {
        res.status(500).json({ 
          error: 'Failed to delete folder',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  // Rename file
  async renameFile(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const { fileId } = req.params;
      const { name } = req.body;

      if (!name || name.trim().length === 0) {
        res.status(400).json({ error: 'File name is required' });
        return;
      }

      await fileService.renameFile(fileId, name.trim(), userId);

      res.json({
        success: true,
        message: 'File renamed successfully'
      });
    } catch (error) {
      logger.error('Rename file error:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({ error: error.message });
      } else if (error instanceof Error && error.message.includes('access denied')) {
        res.status(403).json({ error: error.message });
      } else {
        res.status(500).json({
          error: 'Failed to rename file',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  // Duplicate file
  async duplicateFile(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const { fileId } = req.params;
      const { newName } = req.body;

      const duplicatedFile = await fileService.duplicateFile(fileId, userId, newName);

      res.json({
        success: true,
        data: duplicatedFile,
        message: 'File duplicated successfully'
      });
    } catch (error) {
      logger.error('Duplicate file error:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({ error: error.message });
      } else if (error instanceof Error && error.message.includes('access denied')) {
        res.status(403).json({ error: error.message });
      } else {
        res.status(500).json({
          error: 'Failed to duplicate file',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  // Copy file to folder
  async copyFile(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const { fileId } = req.params;
      const { targetFolderId } = req.body;

      const copiedFile = await fileService.copyFile(fileId, userId, targetFolderId);

      res.json({
        success: true,
        data: copiedFile,
        message: 'File copied successfully'
      });
    } catch (error) {
      logger.error('Copy file error:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({ error: error.message });
      } else if (error instanceof Error && error.message.includes('access denied')) {
        res.status(403).json({ error: error.message });
      } else {
        res.status(500).json({
          error: 'Failed to copy file',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  // Move file to trash (soft delete)
  async moveFileToTrash(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const { fileId } = req.params;
      await fileService.moveFileToTrash(fileId, userId);

      res.json({
        success: true,
        message: 'File moved to trash'
      });
    } catch (error) {
      logger.error('Move file to trash error:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({ error: error.message });
      } else if (error instanceof Error && error.message.includes('access denied')) {
        res.status(403).json({ error: error.message });
      } else {
        res.status(500).json({ 
          error: 'Failed to move file to trash',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  // Get trash contents
  async getTrashContents(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const pagination: Pagination = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 50,
        sortBy: req.query.sortBy as string,
        sortOrder: (req.query.sortOrder as 'asc' | 'desc') || 'desc'
      };

      const trashContents = await fileService.getTrashContents(userId, pagination);

      res.json({
        success: true,
        data: trashContents
      });
    } catch (error) {
      logger.error('Get trash contents error:', error);
      res.status(500).json({ 
        error: 'Failed to get trash contents',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // Restore file from trash
  async restoreFromTrash(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const { fileId } = req.params;
      await fileService.restoreFromTrash(fileId, userId);

      res.json({
        success: true,
        message: 'File restored from trash'
      });
    } catch (error) {
      logger.error('Restore from trash error:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({ error: error.message });
      } else if (error instanceof Error && error.message.includes('access denied')) {
        res.status(403).json({ error: error.message });
      } else {
        res.status(500).json({ 
          error: 'Failed to restore file from trash',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  // Empty trash
  async emptyTrash(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const deletedCount = await fileService.emptyTrash(userId);

      res.json({
        success: true,
        message: `Permanently deleted ${deletedCount} items from trash`,
        deletedCount
      });
    } catch (error) {
      logger.error('Empty trash error:', error);
      res.status(500).json({ 
        error: 'Failed to empty trash',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // Get file shares
  async getFileShares(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const { fileId } = req.params;
      const shares = await fileService.getFileShares(fileId, userId);

      res.json({
        success: true,
        data: shares
      });
    } catch (error) {
      logger.error('Get file shares error:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({ error: error.message });
      } else if (error instanceof Error && error.message.includes('access denied')) {
        res.status(403).json({ error: error.message });
      } else {
        res.status(500).json({ 
          error: 'Failed to get file shares',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  // Update share link
  async updateShareLink(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const { shareId } = req.params;
      const updates = req.body;

      await fileService.updateShareLink(shareId, userId, updates);

      res.json({
        success: true,
        message: 'Share link updated successfully'
      });
    } catch (error) {
      logger.error('Update share link error:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({ error: error.message });
      } else if (error instanceof Error && error.message.includes('access denied')) {
        res.status(403).json({ error: error.message });
      } else {
        res.status(500).json({ 
          error: 'Failed to update share link',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  // Revoke share link
  async revokeShareLink(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const { shareId } = req.params;
      await fileService.revokeShareLink(shareId, userId);

      res.json({
        success: true,
        message: 'Share link revoked successfully'
      });
    } catch (error) {
      logger.error('Revoke share link error:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({ error: error.message });
      } else if (error instanceof Error && error.message.includes('access denied')) {
        res.status(403).json({ error: error.message });
      } else {
        res.status(500).json({ 
          error: 'Failed to revoke share link',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  // Get share statistics
  async getShareStats(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const stats = await fileService.getShareStats(userId);

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error('Get share stats error:', error);
      res.status(500).json({ 
        error: 'Failed to get share statistics',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // Get comprehensive share statistics with charts data
  async getComprehensiveShareStats(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      // Get basic stats
      const basicStats = await fileService.getShareStats(userId);
      
      // Get recent shares with file names
      const recentShares = await fileService.getRecentShares(userId, 10);
      
      // Get download statistics by day (last 30 days)
      const downloadsByDay = await fileService.getDownloadsByDay(userId, 30);

      const comprehensiveStats = {
        ...basicStats,
        recentShares,
        downloadsByDay
      };

      res.json({
        success: true,
        data: comprehensiveStats
      });
    } catch (error) {
      logger.error('Get comprehensive share stats error:', error);
      res.status(500).json({ 
        error: 'Failed to get comprehensive share statistics',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // Get user shares
  async getUserShares(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const shares = await fileService.getUserShares(userId);

      res.json({
        success: true,
        data: shares
      });
    } catch (error) {
      logger.error('Get user shares error:', error);
      res.status(500).json({ 
        error: 'Failed to get user shares',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // Access public share (no auth required)
  async accessPublicShare(req: Request, res: Response): Promise<void> {
    try {
      const { token } = req.params;
      const { password } = req.query;

      const shareInfo = await fileService.getPublicShareInfo(token, password as string);

      res.json({
        success: true,
        data: shareInfo
      });
    } catch (error) {
      logger.error('Access public share error:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({ error: 'Share link not found' });
      } else if (error instanceof Error && error.message.includes('expired')) {
        res.status(410).json({ error: 'Share link has expired' });
      } else if (error instanceof Error && error.message.includes('Password required')) {
        res.status(401).json({ error: 'Password required', reason: 'Password required' });
      } else if (error instanceof Error && error.message.includes('password')) {
        res.status(401).json({ error: 'Password required or incorrect' });
      } else if (error instanceof Error && error.message.includes('limit')) {
        res.status(429).json({ error: 'Download limit exceeded' });
      } else {
        res.status(500).json({
          error: 'Failed to access share link',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  // Access public share with password (no auth required)
  async accessPublicShareWithPassword(req: Request, res: Response): Promise<void> {
    try {
      const { token } = req.params;
      const { password } = req.body;

      const shareInfo = await fileService.getPublicShareInfo(token, password);

      res.json({
        success: true,
        data: shareInfo
      });
    } catch (error) {
      logger.error('Access public share with password error:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({ error: 'Share link not found' });
      } else if (error instanceof Error && error.message.includes('expired')) {
        res.status(410).json({ error: 'Share link has expired' });
      } else if (error instanceof Error && error.message.includes('password')) {
        res.status(401).json({ error: 'Invalid password' });
      } else if (error instanceof Error && error.message.includes('limit')) {
        res.status(429).json({ error: 'Download limit exceeded' });
      } else {
        res.status(500).json({
          error: 'Failed to access share link',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  // Download via public share (no auth required)
  async downloadPublicShare(req: Request, res: Response): Promise<void> {
    try {
      const { token } = req.params;
      const { password } = req.body;

      const fileStream = await fileService.downloadViaPublicShare(token, password);

      // Set appropriate headers
      res.setHeader('Content-Type', fileStream.metadata.mimeType);
      res.setHeader('Content-Length', fileStream.metadata.size);
      res.setHeader('Content-Disposition', `attachment; filename="${fileStream.metadata.originalName}"`);

      // Pipe the stream to response
      fileStream.stream.pipe(res);
    } catch (error) {
      logger.error('Download public share error:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({ error: 'Share link not found' });
      } else if (error instanceof Error && error.message.includes('expired')) {
        res.status(410).json({ error: 'Share link has expired' });
      } else if (error instanceof Error && error.message.includes('password')) {
        res.status(401).json({ error: 'Password required or incorrect' });
      } else if (error instanceof Error && error.message.includes('limit')) {
        res.status(429).json({ error: 'Download limit exceeded' });
      } else {
        res.status(500).json({
          error: 'Failed to download file',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  // Preview via public share (no auth required)
  async previewPublicShare(req: Request, res: Response): Promise<void> {
    try {
      const { token } = req.params;
      const { password } = req.query;

      const fileStream = await fileService.previewViaPublicShare(token, password as string);

      // Set appropriate headers for preview
      res.setHeader('Content-Type', fileStream.metadata.mimeType);
      res.setHeader('Content-Length', fileStream.metadata.size);
      res.setHeader('Cache-Control', 'public, max-age=3600'); // Cache for 1 hour

      // Pipe the stream to response
      fileStream.stream.pipe(res);
    } catch (error) {
      logger.error('Preview public share error:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({ error: 'Share link not found' });
      } else if (error instanceof Error && error.message.includes('expired')) {
        res.status(410).json({ error: 'Share link has expired' });
      } else if (error instanceof Error && error.message.includes('password')) {
        res.status(401).json({ error: 'Password required or incorrect' });
      } else {
        res.status(500).json({
          error: 'Failed to preview file',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  // Debug endpoint to check file data
  async debugFileData(req: Request, res: Response): Promise<void> {
    try {
      // For debugging, use a hardcoded user ID or get from query parameter
      const userId = req.query.userId as string || 'debug-user-id';

      console.log('Debug endpoint called with userId:', userId);

      // Get all files and folders for debugging
      const allFiles = await fileService.getAllFilesForUser(userId);
      const allFolders = await fileService.getAllFoldersForUser(userId);

      res.json({
        success: true,
        userId: userId,
        data: {
          files: allFiles.map(f => ({
            id: f.id,
            originalName: f.originalName,
            folderId: f.folderId,
            isDeleted: f.isDeleted
          })),
          folders: allFolders.map(f => ({
            id: f.id,
            name: f.name,
            parentId: f.parentId,
            isDeleted: f.isDeleted
          }))
        }
      });
    } catch (error) {
      logger.error('Debug file data error:', error);
      res.status(500).json({
        error: 'Failed to get debug data',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
}

// Middleware functions for multer
export const uploadSingle = upload.single('file');
export const uploadMultiple = upload.array('files', MAX_FILES_BATCH);
export const uploadChunk = uploadChunked.single('chunk');

export const fileController = new FileController();
export default fileController;