import { Request, Response } from 'express';
export declare class FileController {
    uploadFile(req: Request, res: Response): Promise<void>;
    uploadFiles(req: Request, res: Response): Promise<void>;
    initChunkedUpload(req: Request, res: Response): Promise<void>;
    uploadChunk(req: Request, res: Response): Promise<void>;
    completeChunkedUpload(req: Request, res: Response): Promise<void>;
    getUploadProgress(req: Request, res: Response): Promise<void>;
    downloadFile(req: Request, res: Response): Promise<void>;
    previewFile(req: Request, res: Response): Promise<void>;
    listFiles(req: Request, res: Response): Promise<void>;
    searchFiles(req: Request, res: Response): Promise<void>;
    createFolder(req: Request, res: Response): Promise<void>;
    moveFile(req: Request, res: Response): Promise<void>;
    deleteFile(req: Request, res: Response): Promise<void>;
    shareFile(req: Request, res: Response): Promise<void>;
    getStorageStats(req: Request, res: Response): Promise<void>;
    getFileInfo(req: Request, res: Response): Promise<void>;
    getFolderById(req: Request, res: Response): Promise<void>;
    getFolderInfo(req: Request, res: Response): Promise<void>;
    renameFolder(req: Request, res: Response): Promise<void>;
    moveFolder(req: Request, res: Response): Promise<void>;
    deleteFolder(req: Request, res: Response): Promise<void>;
    renameFile(req: Request, res: Response): Promise<void>;
    duplicateFile(req: Request, res: Response): Promise<void>;
    copyFile(req: Request, res: Response): Promise<void>;
    moveFileToTrash(req: Request, res: Response): Promise<void>;
    getTrashContents(req: Request, res: Response): Promise<void>;
    restoreFromTrash(req: Request, res: Response): Promise<void>;
    emptyTrash(req: Request, res: Response): Promise<void>;
    getFileShares(req: Request, res: Response): Promise<void>;
    updateShareLink(req: Request, res: Response): Promise<void>;
    revokeShareLink(req: Request, res: Response): Promise<void>;
    getShareStats(req: Request, res: Response): Promise<void>;
    getComprehensiveShareStats(req: Request, res: Response): Promise<void>;
    getUserShares(req: Request, res: Response): Promise<void>;
    accessPublicShare(req: Request, res: Response): Promise<void>;
    accessPublicShareWithPassword(req: Request, res: Response): Promise<void>;
    downloadPublicShare(req: Request, res: Response): Promise<void>;
    previewPublicShare(req: Request, res: Response): Promise<void>;
    debugFileData(req: Request, res: Response): Promise<void>;
}
export declare const uploadSingle: import("express").RequestHandler<import("express-serve-static-core").ParamsDictionary, any, any, import("qs").ParsedQs, Record<string, any>>;
export declare const uploadMultiple: import("express").RequestHandler<import("express-serve-static-core").ParamsDictionary, any, any, import("qs").ParsedQs, Record<string, any>>;
export declare const uploadChunk: import("express").RequestHandler<import("express-serve-static-core").ParamsDictionary, any, any, import("qs").ParsedQs, Record<string, any>>;
export declare const fileController: FileController;
export default fileController;
//# sourceMappingURL=file.controller.d.ts.map