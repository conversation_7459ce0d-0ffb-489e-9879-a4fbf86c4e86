"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const file_controller_1 = require("../controllers/file.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const validation_middleware_1 = require("../middleware/validation.middleware");
const file_validation_1 = require("../validation/file.validation");
const router = (0, express_1.Router)();
// Public share access routes (no auth required) - must be before authMiddleware
router.get('/public/:token', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.accessPublicShare), file_controller_1.fileController.accessPublicShare.bind(file_controller_1.fileController));
// Public share access with password (no auth required)
router.post('/public/:token', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.accessPublicShareWithPassword), file_controller_1.fileController.accessPublicShareWithPassword.bind(file_controller_1.fileController));
router.post('/public/:token/download', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.downloadPublicShare), file_controller_1.fileController.downloadPublicShare.bind(file_controller_1.fileController));
router.get('/public/:token/preview', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.previewPublicShare), file_controller_1.fileController.previewPublicShare.bind(file_controller_1.fileController));
// Debug endpoint (no auth required for debugging)
router.get('/debug/data', file_controller_1.fileController.debugFileData.bind(file_controller_1.fileController));
// Apply authentication middleware to all other routes
router.use(auth_middleware_1.authMiddleware);
// File upload routes
router.post('/upload', file_controller_1.uploadSingle, (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.uploadFile), file_controller_1.fileController.uploadFile.bind(file_controller_1.fileController));
router.post('/upload/batch', file_controller_1.uploadMultiple, (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.uploadFiles), file_controller_1.fileController.uploadFiles.bind(file_controller_1.fileController));
// Chunked upload routes
router.post('/upload/chunked/init', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.initChunkedUpload), file_controller_1.fileController.initChunkedUpload.bind(file_controller_1.fileController));
router.post('/upload/chunked/:uploadId/chunk', file_controller_1.uploadChunk, (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.uploadChunk), file_controller_1.fileController.uploadChunk.bind(file_controller_1.fileController));
router.post('/upload/chunked/:uploadId/complete', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.completeChunkedUpload), file_controller_1.fileController.completeChunkedUpload.bind(file_controller_1.fileController));
router.get('/upload/chunked/:uploadId/progress', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.getUploadProgress), file_controller_1.fileController.getUploadProgress.bind(file_controller_1.fileController));
// File management routes
router.get('/list', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.listFiles), file_controller_1.fileController.listFiles.bind(file_controller_1.fileController));
router.get('/search', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.searchFiles), file_controller_1.fileController.searchFiles.bind(file_controller_1.fileController));
router.get('/:fileId/download', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.downloadFile), file_controller_1.fileController.downloadFile.bind(file_controller_1.fileController));
router.get('/:fileId/preview', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.previewFile), file_controller_1.fileController.previewFile.bind(file_controller_1.fileController));
router.get('/:fileId/info', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.getFileInfo), file_controller_1.fileController.getFileInfo.bind(file_controller_1.fileController));
router.put('/:fileId/move', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.moveFile), file_controller_1.fileController.moveFile.bind(file_controller_1.fileController));
router.delete('/:fileId', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.deleteFile), file_controller_1.fileController.deleteFile.bind(file_controller_1.fileController));
// Folder management routes
router.post('/folders', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.createFolder), file_controller_1.fileController.createFolder.bind(file_controller_1.fileController));
router.put('/folders/:folderId/rename', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.renameFolder), file_controller_1.fileController.renameFolder.bind(file_controller_1.fileController));
router.get('/folders/:folderId', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.getFolderById), file_controller_1.fileController.getFolderById.bind(file_controller_1.fileController));
router.get('/folders/:folderId/info', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.getFolderInfo), file_controller_1.fileController.getFolderInfo.bind(file_controller_1.fileController));
router.put('/folders/:folderId/move', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.moveFolder), file_controller_1.fileController.moveFolder.bind(file_controller_1.fileController));
router.delete('/folders/:folderId', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.deleteFolder), file_controller_1.fileController.deleteFolder.bind(file_controller_1.fileController));
// File rename and trash operations
router.put('/:fileId/rename', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.renameFile), file_controller_1.fileController.renameFile.bind(file_controller_1.fileController));
router.post('/:fileId/duplicate', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.duplicateFile), file_controller_1.fileController.duplicateFile.bind(file_controller_1.fileController));
router.post('/:fileId/copy', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.copyFile), file_controller_1.fileController.copyFile.bind(file_controller_1.fileController));
router.put('/:fileId/trash', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.moveFileToTrash), file_controller_1.fileController.moveFileToTrash.bind(file_controller_1.fileController));
// Trash/Recycle bin operations
router.get('/trash', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.getTrashContents), file_controller_1.fileController.getTrashContents.bind(file_controller_1.fileController));
router.put('/:fileId/restore', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.restoreFromTrash), file_controller_1.fileController.restoreFromTrash.bind(file_controller_1.fileController));
router.delete('/trash/empty', file_controller_1.fileController.emptyTrash.bind(file_controller_1.fileController));
// File sharing routes
router.post('/:fileId/share', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.shareFile), file_controller_1.fileController.shareFile.bind(file_controller_1.fileController));
router.get('/:fileId/shares', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.getFileShares), file_controller_1.fileController.getFileShares.bind(file_controller_1.fileController));
router.put('/shares/:shareId', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.updateShareLink), file_controller_1.fileController.updateShareLink.bind(file_controller_1.fileController));
router.delete('/shares/:shareId', (0, validation_middleware_1.validateRequest)(file_validation_1.fileValidation.revokeShareLink), file_controller_1.fileController.revokeShareLink.bind(file_controller_1.fileController));
router.get('/shares/stats', file_controller_1.fileController.getShareStats.bind(file_controller_1.fileController));
router.get('/shares/stats/comprehensive', file_controller_1.fileController.getComprehensiveShareStats.bind(file_controller_1.fileController));
router.get('/shares', file_controller_1.fileController.getUserShares.bind(file_controller_1.fileController));
// Storage statistics
router.get('/stats', file_controller_1.fileController.getStorageStats.bind(file_controller_1.fileController));
exports.default = router;
//# sourceMappingURL=file.routes.js.map