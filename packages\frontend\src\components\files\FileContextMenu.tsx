import React from 'react'
import {
  DocumentDuplicateIcon,
  PencilIcon,
  TrashIcon,
  ShareIcon,
  ArrowDownTrayIcon,
  FolderIcon,
  InformationCircleIcon,
  ArrowPathIcon,
  PhotoIcon
} from '@heroicons/react/24/outline'
import { FileItem } from '../../pages/Files'
import { useI18n } from '../../contexts/I18nContext'
import { useTheme } from '../../contexts/ThemeContext'
import { cn } from '../../utils/cn'

interface FileContextMenuProps {
  file: FileItem
  position: { x: number; y: number }
  onClose: () => void
  onAction: (action: string, file: FileItem) => void
  showRestoreAction?: boolean
  imageBedFolderId?: string | null
}

const FileContextMenu: React.FC<FileContextMenuProps> = ({
  file,
  position,
  onClose,
  onAction,
  showRestoreAction = false,
  imageBedFolderId
}) => {
  const { t } = useI18n()
  const { theme } = useTheme()
  
  // Check if this is the image bed folder
  const isImageBedFolder = file.isImageBedFolder || (imageBedFolderId && file.id === imageBedFolderId && file.type === 'folder')
  
  // Debug logging for image bed folder detection
  if (file.type === 'folder') {
    console.log('FileContextMenu folder check:', {
      fileName: file.name,
      fileId: file.id,
      imageBedFolderId,
      fileIsImageBedFolder: file.isImageBedFolder,
      calculatedIsImageBedFolder: isImageBedFolder
    })
  }

  const menuItems = showRestoreAction ? [
    {
      id: 'restore',
      label: t('files.restore'),
      icon: ArrowPathIcon,
      show: true
    },
    {
      id: 'delete',
      label: t('files.deletePermanently'),
      icon: TrashIcon,
      show: true,
      danger: true
    }
  ] : isImageBedFolder ? [
    // Image bed folder has limited actions
    {
      id: 'info',
      label: t('files.properties'),
      icon: InformationCircleIcon,
      show: true
    }
  ] : [
    {
      id: 'download',
      label: t('common.download'),
      icon: ArrowDownTrayIcon,
      show: file.type === 'file'
    },
    {
      id: 'rename',
      label: t('common.rename'),
      icon: PencilIcon,
      show: true
    },
    {
      id: 'duplicate',
      label: t('files.duplicate'),
      icon: DocumentDuplicateIcon,
      show: file.type === 'file'
    },
    {
      id: 'move',
      label: t('files.moveTo'),
      icon: FolderIcon,
      show: true
    },
    {
      id: 'share',
      label: t('common.share'),
      icon: ShareIcon,
      show: true
    },
    {
      id: 'moveToImageBed',
      label: t('files.moveToImageBed'),
      icon: PhotoIcon,
      show: file.type === 'file' && file.mimeType?.startsWith('image/')
    },
    {
      id: 'info',
      label: t('files.properties'),
      icon: InformationCircleIcon,
      show: true
    },
    {
      id: 'moveToTrash',
      label: t('files.moveToTrash'),
      icon: TrashIcon,
      show: true,
      danger: true
    }
  ].filter(item => item.show)

  const handleItemClick = (actionId: string) => {
    onAction(actionId, file)
    onClose()
  }

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 z-40"
        onClick={onClose}
      />
      
      {/* Menu */}
      <div
        data-context-menu="true"
        className={cn(
          "fixed z-50 rounded-lg shadow-xl py-2 min-w-52 backdrop-blur-sm",
          "border transition-all duration-200 ease-out",
          "animate-in fade-in-0 zoom-in-95 slide-in-from-left-1 slide-in-from-top-1",
          theme === 'dark'
            ? "bg-gray-900/95 border-gray-700/50 shadow-black/50"
            : "bg-white/95 border-gray-200/50 shadow-gray-900/20"
        )}
        style={{
          left: position.x,
          top: position.y
        }}
      >
        {menuItems.map((item) => {
          const Icon = item.icon
          return (
            <button
              key={item.id}
              onClick={() => handleItemClick(item.id)}
              className={cn(
                'w-full flex items-center px-4 py-2.5 text-sm font-medium transition-all duration-150',
                'hover:scale-[1.02] active:scale-[0.98]',
                'first:rounded-t-md last:rounded-b-md',
                theme === 'dark'
                  ? cn(
                      'text-gray-200 hover:bg-gray-800/80',
                      item.danger
                        ? 'text-red-400 hover:text-red-300 hover:bg-red-900/20'
                        : 'hover:text-white'
                    )
                  : cn(
                      'text-gray-700 hover:bg-gray-100/80',
                      item.danger
                        ? 'text-red-600 hover:text-red-700 hover:bg-red-50'
                        : 'hover:text-gray-900'
                    )
              )}
            >
              <Icon className="w-4 h-4 mr-3 flex-shrink-0" />
              <span className="flex-1 text-left">{item.label}</span>
            </button>
          )
        })}
      </div>
    </>
  )
}

export default FileContextMenu