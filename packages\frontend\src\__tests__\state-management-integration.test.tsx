import { describe, it, expect, beforeEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useAuthStore, useFileStore, useSyncStore } from '../stores'

describe('State Management Integration', () => {
  beforeEach(() => {
    // Reset stores before each test
    useAuthStore.getState().logout()
    useFileStore.setState({
      files: [],
      currentFolder: null,
      selectedFiles: new Set(),
      isLoading: false,
      error: null
    })
    useSyncStore.setState({
      syncStatus: null,
      isOnline: true,
      isConnected: false,
      pendingChanges: [],
      conflicts: [],
      error: null
    })
  })

  describe('Auth Store', () => {
    it('should have initial state', () => {
      const { result } = renderHook(() => useAuthStore())
      
      expect(result.current.user).toBeNull()
      expect(result.current.isAuthenticated).toBe(false)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()
      expect(result.current.sessions).toEqual([])
    })

    it('should clear error', () => {
      const { result } = renderHook(() => useAuthStore())
      
      act(() => {
        useAuthStore.setState({ error: 'Test error' })
      })
      
      expect(result.current.error).toBe('Test error')
      
      act(() => {
        result.current.clearError()
      })
      
      expect(result.current.error).toBeNull()
    })
  })

  describe('File Store', () => {
    it('should have initial state', () => {
      const { result } = renderHook(() => useFileStore())
      
      expect(result.current.files).toEqual([])
      expect(result.current.currentFolder).toBeNull()
      expect(result.current.selectedFiles).toEqual(new Set())
      expect(result.current.viewMode).toBe('grid')
      expect(result.current.searchQuery).toBe('')
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()
    })

    it('should toggle file selection', () => {
      const { result } = renderHook(() => useFileStore())
      
      act(() => {
        result.current.toggleFileSelection('file1')
      })
      
      expect(result.current.selectedFiles.has('file1')).toBe(true)
      
      act(() => {
        result.current.toggleFileSelection('file1')
      })
      
      expect(result.current.selectedFiles.has('file1')).toBe(false)
    })

    it('should set view mode', () => {
      const { result } = renderHook(() => useFileStore())
      
      act(() => {
        result.current.setViewMode('list')
      })
      
      expect(result.current.viewMode).toBe('list')
    })

    it('should set search query', () => {
      const { result } = renderHook(() => useFileStore())
      
      act(() => {
        result.current.setSearchQuery('test query')
      })
      
      expect(result.current.searchQuery).toBe('test query')
    })

    it('should clear selection', () => {
      const { result } = renderHook(() => useFileStore())
      
      act(() => {
        result.current.toggleFileSelection('file1')
        result.current.toggleFileSelection('file2')
      })
      
      expect(result.current.selectedFiles.size).toBe(2)
      
      act(() => {
        result.current.clearSelection()
      })
      
      expect(result.current.selectedFiles.size).toBe(0)
    })

    it('should clear error', () => {
      const { result } = renderHook(() => useFileStore())
      
      act(() => {
        useFileStore.setState({ error: 'Test error' })
      })
      
      expect(result.current.error).toBe('Test error')
      
      act(() => {
        result.current.clearError()
      })
      
      expect(result.current.error).toBeNull()
    })
  })

  describe('Sync Store', () => {
    it('should have initial state', () => {
      const { result } = renderHook(() => useSyncStore())
      
      expect(result.current.syncStatus).toBeNull()
      expect(result.current.isOnline).toBe(true)
      expect(result.current.isConnected).toBe(false)
      expect(result.current.pendingChanges).toEqual([])
      expect(result.current.conflicts).toEqual([])
      expect(result.current.syncProgress).toBe(0)
      expect(result.current.currentOperation).toBeNull()
      expect(result.current.queuedOperations).toBe(0)
      expect(result.current.syncMode).toBe('AUTOMATIC')
      expect(result.current.error).toBeNull()
    })

    it('should set online status', () => {
      const { result } = renderHook(() => useSyncStore())
      
      act(() => {
        result.current.setOnlineStatus(false)
      })
      
      expect(result.current.isOnline).toBe(false)
      
      act(() => {
        result.current.setOnlineStatus(true)
      })
      
      expect(result.current.isOnline).toBe(true)
    })

    it('should clear sync errors', () => {
      const { result } = renderHook(() => useSyncStore())
      
      act(() => {
        useSyncStore.setState({ error: 'Sync error' })
      })
      
      expect(result.current.error).toBe('Sync error')
      
      act(() => {
        result.current.clearSyncErrors()
      })
      
      expect(result.current.error).toBeNull()
    })
  })

  describe('Store Persistence', () => {
    it('should persist auth state', () => {
      const { result } = renderHook(() => useAuthStore())
      
      // Simulate user login
      act(() => {
        useAuthStore.setState({
          user: {
            id: 'user1',
            username: 'testuser',
            email: '<EMAIL>',
            twoFactorEnabled: false
          },
          isAuthenticated: true
        })
      })
      
      expect(result.current.isAuthenticated).toBe(true)
      expect(result.current.user?.username).toBe('testuser')
    })

    it('should persist file store preferences', () => {
      const { result } = renderHook(() => useFileStore())
      
      act(() => {
        result.current.setViewMode('list')
        result.current.setSortBy('size')
        result.current.setSortOrder('desc')
      })
      
      expect(result.current.viewMode).toBe('list')
      expect(result.current.sortBy).toBe('size')
      expect(result.current.sortOrder).toBe('desc')
    })

    it('should persist sync mode', () => {
      const { result } = renderHook(() => useSyncStore())
      
      act(() => {
        useSyncStore.setState({ syncMode: 'MANUAL' })
      })
      
      expect(result.current.syncMode).toBe('MANUAL')
    })
  })
})
