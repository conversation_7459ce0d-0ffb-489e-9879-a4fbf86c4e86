// Export all stores
export { useAuthStore } from './authStore'
export { useFileStore } from './fileStore'
export { useSyncStore } from './syncStore'

// Store initialization helper
export const initializeStores = async () => {
  const { useAuthStore } = await import('./authStore')
  const { useSyncStore } = await import('./syncStore')
  
  // Initialize auth status check
  await useAuthStore.getState().checkAuthStatus()
  
  // Initialize sync status if user is authenticated
  if (useAuthStore.getState().isAuthenticated) {
    await useSyncStore.getState().initializeSyncStatus()
    useSyncStore.getState().connect()
  }
}
