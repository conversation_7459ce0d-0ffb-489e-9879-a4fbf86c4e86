import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { FileInfo, FileMetadata, FolderData, Folder } from '@cloud-storage/shared'
import { fileService } from '../services/fileService'
import { FileItem, FolderPath } from '../pages/Files'

interface FileState {
  // State
  files: FileItem[]
  currentFolder: string | null
  folderPath: FolderPath[]
  selectedFiles: Set<string>
  isLoading: boolean
  uploadProgress: Record<string, number>
  error: string | null
  viewMode: 'grid' | 'list'
  searchQuery: string
  sortBy: 'name' | 'size' | 'modifiedAt' | 'type'
  sortOrder: 'asc' | 'desc'
  fileTypeFilter: string
  imageBedFolderId: string | null

  // Actions
  loadFiles: (folderId?: string) => Promise<void>
  uploadFile: (file: File, folderId?: string, onProgress?: (progress: number) => void) => Promise<FileInfo>
  deleteFile: (fileId: string) => Promise<void>
  deleteFiles: (fileIds: string[]) => Promise<void>
  createFolder: (name: string, parentId?: string) => Promise<Folder>
  moveFile: (fileId: string, targetFolderId: string) => Promise<void>
  moveFiles: (fileIds: string[], targetFolderId: string) => Promise<void>
  renameFile: (fileId: string, newName: string) => Promise<void>
  renameFolder: (folderId: string, newName: string) => Promise<void>
  shareFile: (fileId: string, options: any) => Promise<any>
  setCurrentFolder: (folderId: string | null, folderName?: string) => void
  setSelectedFiles: (fileIds: Set<string>) => void
  toggleFileSelection: (fileId: string) => void
  selectAllFiles: () => void
  clearSelection: () => void
  setViewMode: (mode: 'grid' | 'list') => void
  setSearchQuery: (query: string) => void
  setSortBy: (sortBy: 'name' | 'size' | 'modifiedAt' | 'type') => void
  setSortOrder: (order: 'asc' | 'desc') => void
  setFileTypeFilter: (filter: string) => void
  setImageBedFolderId: (folderId: string | null) => void
  clearError: () => void
  refreshFiles: () => Promise<void>
}

export const useFileStore = create<FileState>()(
  persist(
    (set, get) => ({
      // Initial state
      files: [],
      currentFolder: null,
      folderPath: [{ id: 'root', name: '/root' }],
      selectedFiles: new Set(),
      isLoading: false,
      uploadProgress: {},
      error: null,
      viewMode: 'grid',
      searchQuery: '',
      sortBy: 'name',
      sortOrder: 'asc',
      fileTypeFilter: '',
      imageBedFolderId: null,

      // Actions
      loadFiles: async (folderId?: string) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fileService.getFiles(folderId)
          
          // Transform API response to FileItem format
          const transformedFiles: FileItem[] = [
            ...response.folders.map(folder => ({
              id: folder.id,
              name: folder.name,
              type: 'folder' as const,
              modifiedAt: new Date(folder.modifiedAt),
              isImageBedFolder: folder.name === 'Image Bed' || folder.name === '图床'
            })),
            ...response.files.map(file => ({
              id: file.id,
              name: file.filename,
              type: 'file' as const,
              size: file.size,
              mimeType: file.mimeType,
              modifiedAt: new Date(file.uploadedAt),
              isShared: file.isShared,
              thumbnailUrl: file.thumbnailUrl,
              publicUrl: file.publicUrl,
              cdnUrl: file.cdnUrl,
              thumbnails: file.thumbnails
            }))
          ]

          set({ 
            files: transformedFiles, 
            isLoading: false,
            currentFolder: folderId || null
          })

          // Update folder path if navigating to a specific folder
          if (folderId) {
            const folder = response.folders.find(f => f.id === folderId)
            if (folder) {
              const { folderPath } = get()
              const newPath = [...folderPath, { id: folder.id, name: folder.name }]
              set({ folderPath: newPath })
            }
          }
        } catch (error: any) {
          console.error('Failed to load files:', error)
          set({ 
            error: error.response?.data?.error?.message || '加载文件失败', 
            isLoading: false 
          })
        }
      },

      uploadFile: async (file: File, folderId?: string, onProgress?: (progress: number) => void) => {
        const fileId = `upload-${Date.now()}-${Math.random()}`
        
        try {
          // Initialize upload progress
          set(state => ({
            uploadProgress: { ...state.uploadProgress, [fileId]: 0 }
          }))

          const result = await fileService.resumableUpload(
            file,
            folderId,
            undefined,
            (progress) => {
              set(state => ({
                uploadProgress: { ...state.uploadProgress, [fileId]: progress }
              }))
              onProgress?.(progress)
            }
          )

          // Remove from upload progress and refresh files
          set(state => {
            const newProgress = { ...state.uploadProgress }
            delete newProgress[fileId]
            return { uploadProgress: newProgress }
          })

          await get().refreshFiles()
          return result
        } catch (error: any) {
          // Remove from upload progress on error
          set(state => {
            const newProgress = { ...state.uploadProgress }
            delete newProgress[fileId]
            return { 
              uploadProgress: newProgress,
              error: error.response?.data?.error?.message || '文件上传失败'
            }
          })
          throw error
        }
      },

      deleteFile: async (fileId: string) => {
        try {
          await fileService.deleteFile(fileId)
          
          // Remove file from local state
          set(state => ({
            files: state.files.filter(file => file.id !== fileId),
            selectedFiles: new Set([...state.selectedFiles].filter(id => id !== fileId))
          }))
        } catch (error: any) {
          console.error('Failed to delete file:', error)
          set({ error: error.response?.data?.error?.message || '删除文件失败' })
          throw error
        }
      },

      deleteFiles: async (fileIds: string[]) => {
        try {
          await Promise.all(fileIds.map(id => fileService.deleteFile(id)))
          
          // Remove files from local state
          set(state => ({
            files: state.files.filter(file => !fileIds.includes(file.id)),
            selectedFiles: new Set([...state.selectedFiles].filter(id => !fileIds.includes(id)))
          }))
        } catch (error: any) {
          console.error('Failed to delete files:', error)
          set({ error: error.response?.data?.error?.message || '批量删除文件失败' })
          throw error
        }
      },

      createFolder: async (name: string, parentId?: string) => {
        try {
          const folderData: FolderData = { name, parentId }
          const result = await fileService.createFolder(folderData)
          
          // Add new folder to local state
          const newFolder: FileItem = {
            id: result.id,
            name: result.name,
            type: 'folder',
            modifiedAt: new Date(result.modifiedAt),
            isImageBedFolder: name === 'Image Bed' || name === '图床'
          }
          
          set(state => ({
            files: [...state.files, newFolder]
          }))

          return result
        } catch (error: any) {
          console.error('Failed to create folder:', error)
          set({ error: error.response?.data?.error?.message || '创建文件夹失败' })
          throw error
        }
      },

      moveFile: async (fileId: string, targetFolderId: string) => {
        try {
          await fileService.moveFile(fileId, targetFolderId)
          await get().refreshFiles()
        } catch (error: any) {
          console.error('Failed to move file:', error)
          set({ error: error.response?.data?.error?.message || '移动文件失败' })
          throw error
        }
      },

      moveFiles: async (fileIds: string[], targetFolderId: string) => {
        try {
          await Promise.all(fileIds.map(id => fileService.moveFile(id, targetFolderId)))
          await get().refreshFiles()
        } catch (error: any) {
          console.error('Failed to move files:', error)
          set({ error: error.response?.data?.error?.message || '批量移动文件失败' })
          throw error
        }
      },

      renameFile: async (fileId: string, newName: string) => {
        try {
          await fileService.renameFile(fileId, newName)
          
          // Update file name in local state
          set(state => ({
            files: state.files.map(file => 
              file.id === fileId ? { ...file, name: newName } : file
            )
          }))
        } catch (error: any) {
          console.error('Failed to rename file:', error)
          set({ error: error.response?.data?.error?.message || '重命名文件失败' })
          throw error
        }
      },

      renameFolder: async (folderId: string, newName: string) => {
        try {
          await fileService.renameFolder(folderId, newName)
          
          // Update folder name in local state
          set(state => ({
            files: state.files.map(file => 
              file.id === folderId ? { ...file, name: newName } : file
            )
          }))
        } catch (error: any) {
          console.error('Failed to rename folder:', error)
          set({ error: error.response?.data?.error?.message || '重命名文件夹失败' })
          throw error
        }
      },

      shareFile: async (fileId: string, options: any) => {
        try {
          const result = await fileService.shareFile(fileId, options)
          
          // Update file share status in local state
          set(state => ({
            files: state.files.map(file => 
              file.id === fileId ? { ...file, isShared: true } : file
            )
          }))

          return result
        } catch (error: any) {
          console.error('Failed to share file:', error)
          set({ error: error.response?.data?.error?.message || '分享文件失败' })
          throw error
        }
      },

      setCurrentFolder: (folderId: string | null, folderName?: string) => {
        set({ currentFolder: folderId })
        
        if (folderId && folderName) {
          set(state => {
            const newPath = [...state.folderPath, { id: folderId, name: folderName }]
            return { folderPath: newPath }
          })
        } else if (!folderId) {
          set({ folderPath: [{ id: 'root', name: '/root' }] })
        }
      },

      setSelectedFiles: (fileIds: Set<string>) => {
        set({ selectedFiles: fileIds })
      },

      toggleFileSelection: (fileId: string) => {
        set(state => {
          const newSelection = new Set(state.selectedFiles)
          if (newSelection.has(fileId)) {
            newSelection.delete(fileId)
          } else {
            newSelection.add(fileId)
          }
          return { selectedFiles: newSelection }
        })
      },

      selectAllFiles: () => {
        set(state => ({
          selectedFiles: new Set(state.files.map(file => file.id))
        }))
      },

      clearSelection: () => {
        set({ selectedFiles: new Set() })
      },

      setViewMode: (mode: 'grid' | 'list') => {
        set({ viewMode: mode })
      },

      setSearchQuery: (query: string) => {
        set({ searchQuery: query })
      },

      setSortBy: (sortBy: 'name' | 'size' | 'modifiedAt' | 'type') => {
        set({ sortBy })
      },

      setSortOrder: (order: 'asc' | 'desc') => {
        set({ sortOrder: order })
      },

      setFileTypeFilter: (filter: string) => {
        set({ fileTypeFilter: filter })
      },

      setImageBedFolderId: (folderId: string | null) => {
        set({ imageBedFolderId: folderId })
      },

      clearError: () => {
        set({ error: null })
      },

      refreshFiles: async () => {
        const { currentFolder } = get()
        await get().loadFiles(currentFolder || undefined)
      }
    }),
    {
      name: 'file-storage',
      partialize: (state) => ({
        viewMode: state.viewMode,
        sortBy: state.sortBy,
        sortOrder: state.sortOrder,
        imageBedFolderId: state.imageBedFolderId
      })
    }
  )
)
