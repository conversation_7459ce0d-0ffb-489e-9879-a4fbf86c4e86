import { 
  FileMetadata, 
  FileUpload, 
  FileInfo, 
  FileList, 
  SearchQuery, 
  Pagination,
  FolderData,
  Folder,
  ShareLink,
  ShareOptions
} from '@cloud-storage/shared';
import { storageService, FileChunks } from './storage.service';
import { fileDao } from '../dao/file.dao';
import * as crypto from 'crypto';
import logger from '../utils/logger';

export interface FileStream {
  stream: NodeJS.ReadableStream;
  metadata: FileMetadata;
}

export interface ChunkedUploadInit {
  filename: string;
  fileSize: number;
  mimeType: string;
  totalChunks: number;
  folderId?: string;
  tags?: string[];
}

export interface ChunkUpload {
  chunkIndex: number;
  chunkData: Buffer;
  chunkSize: number;
}

export interface ChunkUploadResult {
  chunkIndex: number;
  uploaded: number;
  total: number;
  progress: number;
}

export interface UploadProgress {
  uploadId: string;
  filename: string;
  totalChunks: number;
  uploadedChunks: number;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'failed';
  createdAt: Date;
  updatedAt: Date;
}

// In-memory storage for upload progress (in production, use Redis)
const uploadProgressMap = new Map<string, UploadProgress>();
const uploadChunksMap = new Map<string, Map<number, Buffer>>();

export class FileService {
  
  async uploadFile(
    userId: string, 
    fileUpload: FileUpload, 
    metadata: Partial<FileMetadata>
  ): Promise<FileInfo> {
    try {
      const fileId = crypto.randomUUID();
      const fileBuffer = Buffer.isBuffer(fileUpload.file) 
        ? fileUpload.file 
        : Buffer.from(await (fileUpload.file as File).arrayBuffer());

      // Upload file chunks to distributed storage
      const fileChunks = await storageService.uploadFileChunked(
        fileId,
        fileBuffer,
        {
          originalName: fileUpload.filename,
          mimeType: fileUpload.mimeType,
          ...metadata
        }
      );

      // Create file metadata
      const fileMetadata: FileMetadata = {
        id: fileId,
        userId,
        filename: this.generateUniqueFilename(fileUpload.filename),
        originalName: fileUpload.filename,
        mimeType: fileUpload.mimeType,
        size: fileBuffer.length,
        checksum: fileChunks.checksum,
        folderId: fileUpload.folderId || metadata.folderId,
        isPublic: metadata.isPublic ?? false, // Use metadata.isPublic if provided, otherwise false
        uploadedAt: new Date(),
        modifiedAt: new Date(),
        tags: fileUpload.tags || [],
        storageNodes: this.extractStorageNodes(fileChunks)
      };

      // Save metadata to database
      await fileDao.createFile(fileMetadata);

      // Store chunk information for retrieval
      await fileDao.saveFileChunks(fileId, fileChunks);

      logger.info(`File uploaded successfully: ${fileId}`);

      return {
        id: fileId,
        filename: fileMetadata.filename,
        size: fileMetadata.size,
        mimeType: fileMetadata.mimeType,
        uploadedAt: fileMetadata.uploadedAt,
        downloadUrl: this.generateDownloadUrl(fileId)
      };
    } catch (error) {
      logger.error('File upload failed:', error);
      throw new Error('File upload failed');
    }
  }

  async uploadFiles(userId: string, files: FileUpload[]): Promise<FileInfo[]> {
    const uploadPromises = files.map(file => 
      this.uploadFile(userId, file, {})
    );

    try {
      const results = await Promise.allSettled(uploadPromises);
      const successful: FileInfo[] = [];
      const failed: string[] = [];

      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          successful.push(result.value);
        } else {
          failed.push(files[index].filename);
          logger.error(`Failed to upload ${files[index].filename}:`, result.reason);
        }
      });

      if (failed.length > 0) {
        logger.warn(`${failed.length} files failed to upload: ${failed.join(', ')}`);
      }

      return successful;
    } catch (error) {
      logger.error('Batch file upload failed:', error);
      throw new Error('Batch file upload failed');
    }
  }

  async downloadFile(fileId: string, userId: string): Promise<FileStream> {
    try {
      // Get file metadata
      const metadata = await fileDao.getFileById(fileId);
      if (!metadata) {
        throw new Error('File not found');
      }

      // Check permissions
      if (metadata.userId !== userId && !metadata.isPublic) {
        throw new Error('Access denied');
      }

      // Get chunk information
      const fileChunks = await fileDao.getFileChunks(fileId);
      if (!fileChunks) {
        throw new Error('File chunks not found');
      }

      // Download file from distributed storage
      const fileBuffer = await storageService.downloadFileChunked(fileChunks);

      // Create readable stream
      const { Readable } = require('stream');
      const stream = Readable.from(fileBuffer);

      logger.info(`File downloaded: ${fileId}`);

      return {
        stream,
        metadata
      };
    } catch (error) {
      logger.error(`File download failed for ${fileId}:`, error);
      throw error;
    }
  }

  async getFilePreview(fileId: string, userId: string, options: {
    quality?: string;
    maxWidth?: number;
    maxHeight?: number;
  } = {}): Promise<FileStream> {
    try {
      // Get file metadata
      const metadata = await fileDao.getFileById(fileId);
      if (!metadata) {
        throw new Error('File not found');
      }

      // Check permissions
      if (metadata.userId !== userId && !metadata.isPublic) {
        throw new Error('Access denied');
      }

      // Check if file type supports preview
      const supportedTypes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/webp',
        'video/mp4', 'video/webm', 'video/ogg',
        'audio/mpeg', 'audio/wav', 'audio/ogg',
        'application/pdf',
        'text/plain', 'text/html', 'text/css', 'text/javascript',
        'application/json', 'application/xml'
      ];

      if (!supportedTypes.includes(metadata.mimeType)) {
        throw new Error('Preview not supported');
      }

      // Get chunk information
      const fileChunks = await fileDao.getFileChunks(fileId);
      if (!fileChunks) {
        throw new Error('File chunks not found');
      }

      // Download file from distributed storage
      let fileBuffer = await storageService.downloadFileChunked(fileChunks);

      // For images, optionally resize for preview
      if (metadata.mimeType.startsWith('image/') && (options.maxWidth || options.maxHeight)) {
        fileBuffer = await this.resizeImage(fileBuffer, metadata.mimeType, {
          maxWidth: options.maxWidth || 1200,
          maxHeight: options.maxHeight || 800,
          quality: options.quality || 'medium'
        });
      }

      // Create readable stream
      const { Readable } = require('stream');
      const stream = Readable.from(fileBuffer);

      logger.info(`File preview generated: ${fileId}`);

      return {
        stream,
        metadata: {
          ...metadata,
          size: fileBuffer.length // Update size if resized
        }
      };
    } catch (error) {
      logger.error(`File preview failed for ${fileId}:`, error);
      throw error;
    }
  }

  private async resizeImage(buffer: Buffer, mimeType: string, options: {
    maxWidth: number;
    maxHeight: number;
    quality: string;
  }): Promise<Buffer> {
    try {
      // For now, return original buffer
      // In a production environment, you would use a library like Sharp to resize images
      // const sharp = require('sharp');
      // return await sharp(buffer)
      //   .resize(options.maxWidth, options.maxHeight, { fit: 'inside', withoutEnlargement: true })
      //   .jpeg({ quality: options.quality === 'high' ? 90 : options.quality === 'low' ? 60 : 75 })
      //   .toBuffer();
      
      return buffer;
    } catch (error) {
      logger.warn('Image resize failed, returning original:', error);
      return buffer;
    }
  }

  async listFiles(
    userId: string, 
    folderId?: string, 
    pagination: Pagination = { page: 1, limit: 50 }
  ): Promise<FileList> {
    try {
      const { files, totalCount } = await fileDao.getFilesByUser(
        userId, 
        folderId, 
        pagination
      );

      const folders = folderId 
        ? await fileDao.getFoldersByParent(userId, folderId)
        : await fileDao.getRootFolders(userId);

      return {
        files,
        folders,
        totalCount,
        hasMore: (pagination.page * pagination.limit) < totalCount
      };
    } catch (error) {
      logger.error('Failed to list files:', error);
      throw new Error('Failed to list files');
    }
  }

  async searchFiles(userId: string, query: SearchQuery): Promise<FileList> {
    try {
      const { files, totalCount } = await fileDao.searchFiles(userId, query);
      
      return {
        files,
        folders: [], // Search typically doesn't return folders
        totalCount,
        hasMore: false // Simplified for now
      };
    } catch (error) {
      logger.error('File search failed:', error);
      throw new Error('File search failed');
    }
  }

  async createFolder(userId: string, folderData: FolderData): Promise<Folder> {
    try {
      // Check if trying to create image bed folder in root directory
      if (!folderData.parentId) {
        const restrictedNames = ['Image Bed', '图床', '图片床'];
        if (restrictedNames.includes(folderData.name)) {
          throw new Error('Cannot create folder with reserved image bed name. The Image Bed folder is automatically created by the system.');
        }
      }

      const folderId = crypto.randomUUID();
      const path = await this.buildFolderPath(folderData.parentId, folderData.name);

      const folder: Folder = {
        id: folderId,
        userId,
        name: folderData.name,
        parentId: folderData.parentId,
        path,
        createdAt: new Date(),
        modifiedAt: new Date()
      };

      await fileDao.createFolder(folder);
      logger.info(`Folder created: ${folderId}`);

      return folder;
    } catch (error) {
      logger.error('Folder creation failed:', error);
      throw error;
    }
  }

  async moveFile(fileId: string, targetFolderId: string, userId: string): Promise<void> {
    try {
      const file = await fileDao.getFileById(fileId);
      if (!file || file.userId !== userId) {
        throw new Error('File not found or access denied');
      }

      if (targetFolderId) {
        const targetFolder = await fileDao.getFolderById(targetFolderId);
        if (!targetFolder || targetFolder.userId !== userId) {
          throw new Error('Target folder not found or access denied');
        }
      }

      await fileDao.updateFile(fileId, { 
        folderId: targetFolderId,
        modifiedAt: new Date()
      });

      logger.info(`File moved: ${fileId} to folder ${targetFolderId}`);
    } catch (error) {
      logger.error(`Failed to move file ${fileId}:`, error);
      throw error;
    }
  }

  async deleteFile(fileId: string, userId: string): Promise<void> {
    try {
      const file = await fileDao.getFileById(fileId);
      if (!file || file.userId !== userId) {
        throw new Error('File not found or access denied');
      }

      // Get chunk information
      const fileChunks = await fileDao.getFileChunks(fileId);
      
      // Delete from distributed storage
      if (fileChunks) {
        await storageService.deleteFileChunked(fileChunks);
      }

      // Delete metadata from database
      await fileDao.deleteFile(fileId);
      await fileDao.deleteFileChunks(fileId);

      logger.info(`File deleted: ${fileId}`);
    } catch (error) {
      logger.error(`Failed to delete file ${fileId}:`, error);
      throw error;
    }
  }

  async shareFile(fileId: string, userId: string, shareOptions: ShareOptions): Promise<ShareLink> {
    try {
      const file = await fileDao.getFileById(fileId);
      if (!file || file.userId !== userId) {
        throw new Error('File not found or access denied');
      }

      const shareId = crypto.randomUUID();
      const token = crypto.randomBytes(32).toString('hex');

      const shareLink: ShareLink = {
        id: shareId,
        fileId,
        userId,
        token,
        permissions: shareOptions.permissions,
        expiresAt: shareOptions.expiresAt,
        password: shareOptions.password,
        downloadCount: 0,
        maxDownloads: shareOptions.maxDownloads,
        createdAt: new Date()
      };

      await fileDao.createShareLink(shareLink);
      logger.info(`Share link created: ${shareId} for file ${fileId}`);

      return shareLink;
    } catch (error) {
      logger.error(`Failed to create share link for file ${fileId}:`, error);
      throw error;
    }
  }

  private generateUniqueFilename(originalName: string): string {
    const timestamp = Date.now();
    const random = crypto.randomBytes(4).toString('hex');
    const ext = originalName.split('.').pop();
    const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '');
    
    return `${nameWithoutExt}_${timestamp}_${random}.${ext}`;
  }

  private extractStorageNodes(fileChunks: FileChunks): any[] {
    const nodeIds = new Set<string>();
    
    fileChunks.chunks.forEach(chunk => {
      chunk.storageNodes.forEach(nodeId => nodeIds.add(nodeId));
    });

    return Array.from(nodeIds).map(nodeId => ({
      id: nodeId,
      url: `storage-node-${nodeId}`, // Simplified
      region: 'us-east-1', // Default region
      isHealthy: true
    }));
  }

  private generateDownloadUrl(fileId: string): string {
    // In a real implementation, this would generate a signed URL
    return `/api/files/${fileId}/download`;
  }

  private async buildFolderPath(parentId?: string, folderName?: string): Promise<string> {
    if (!parentId) {
      return `/${folderName}`;
    }

    const parent = await fileDao.getFolderById(parentId);
    if (!parent) {
      throw new Error('Parent folder not found');
    }

    return `${parent.path}/${folderName}`;
  }

  async getStorageStats(userId: string): Promise<{
    totalFiles: number;
    totalSize: number;
    storageQuota: number;
    usedPercentage: number;
  }> {
    try {
      const stats = await fileDao.getUserStorageStats(userId);
      const storageQuota = 10 * 1024 * 1024 * 1024; // 10GB default quota

      return {
        totalFiles: stats.fileCount,
        totalSize: stats.totalSize,
        storageQuota,
        usedPercentage: (stats.totalSize / storageQuota) * 100
      };
    } catch (error) {
      logger.error(`Failed to get storage stats for user ${userId}:`, error);
      throw new Error('Failed to get storage stats');
    }
  }

  // Initialize chunked upload
  async initChunkedUpload(userId: string, uploadInit: ChunkedUploadInit): Promise<string> {
    try {
      const uploadId = crypto.randomUUID();
      
      const progress: UploadProgress = {
        uploadId,
        filename: uploadInit.filename,
        totalChunks: uploadInit.totalChunks,
        uploadedChunks: 0,
        progress: 0,
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      uploadProgressMap.set(uploadId, progress);
      uploadChunksMap.set(uploadId, new Map());

      logger.info(`Chunked upload initialized: ${uploadId} for user ${userId}`);
      return uploadId;
    } catch (error) {
      logger.error('Failed to initialize chunked upload:', error);
      throw new Error('Failed to initialize chunked upload');
    }
  }

  // Upload chunk
  async uploadChunk(uploadId: string, chunk: ChunkUpload): Promise<ChunkUploadResult> {
    try {
      const progress = uploadProgressMap.get(uploadId);
      if (!progress) {
        throw new Error('Upload session not found');
      }

      const chunks = uploadChunksMap.get(uploadId);
      if (!chunks) {
        throw new Error('Upload chunks not found');
      }

      // Store chunk
      chunks.set(chunk.chunkIndex, chunk.chunkData);

      // Update progress
      progress.uploadedChunks = chunks.size;
      progress.progress = (progress.uploadedChunks / progress.totalChunks) * 100;
      progress.status = 'uploading';
      progress.updatedAt = new Date();

      uploadProgressMap.set(uploadId, progress);

      logger.info(`Chunk ${chunk.chunkIndex} uploaded for upload ${uploadId}`);

      return {
        chunkIndex: chunk.chunkIndex,
        uploaded: progress.uploadedChunks,
        total: progress.totalChunks,
        progress: progress.progress
      };
    } catch (error) {
      logger.error(`Failed to upload chunk for ${uploadId}:`, error);
      throw error;
    }
  }

  // Complete chunked upload
  async completeChunkedUpload(uploadId: string, userId: string): Promise<FileInfo> {
    try {
      const progress = uploadProgressMap.get(uploadId);
      if (!progress) {
        throw new Error('Upload session not found');
      }

      const chunks = uploadChunksMap.get(uploadId);
      if (!chunks || chunks.size !== progress.totalChunks) {
        throw new Error('Not all chunks uploaded');
      }

      // Combine chunks in order
      const sortedChunks = Array.from(chunks.entries())
        .sort(([a], [b]) => a - b)
        .map(([, buffer]) => buffer);

      const fileBuffer = Buffer.concat(sortedChunks);

      // Create file upload object
      const fileUpload: FileUpload = {
        file: fileBuffer,
        filename: progress.filename,
        mimeType: 'application/octet-stream', // Will be determined by file extension
        tags: []
      };

      // Upload the complete file
      const fileInfo = await this.uploadFile(userId, fileUpload, {});

      // Update progress
      progress.status = 'completed';
      progress.progress = 100;
      progress.updatedAt = new Date();
      uploadProgressMap.set(uploadId, progress);

      // Clean up
      setTimeout(() => {
        uploadProgressMap.delete(uploadId);
        uploadChunksMap.delete(uploadId);
      }, 60000); // Clean up after 1 minute

      logger.info(`Chunked upload completed: ${uploadId}`);
      return fileInfo;
    } catch (error) {
      // Mark as failed
      const progress = uploadProgressMap.get(uploadId);
      if (progress) {
        progress.status = 'failed';
        progress.updatedAt = new Date();
        uploadProgressMap.set(uploadId, progress);
      }

      logger.error(`Failed to complete chunked upload ${uploadId}:`, error);
      throw error;
    }
  }

  // Get upload progress
  async getUploadProgress(uploadId: string, userId: string): Promise<UploadProgress> {
    const progress = uploadProgressMap.get(uploadId);
    if (!progress) {
      throw new Error('Upload session not found');
    }

    return progress;
  }

  // Clean up expired uploads (should be called periodically)
  async cleanupExpiredUploads(): Promise<void> {
    const now = new Date();
    const expiredUploads: string[] = [];

    for (const [uploadId, progress] of Array.from(uploadProgressMap.entries())) {
      const ageInMinutes = (now.getTime() - progress.createdAt.getTime()) / (1000 * 60);
      
      // Clean up uploads older than 24 hours
      if (ageInMinutes > 24 * 60) {
        expiredUploads.push(uploadId);
      }
    }

    for (const uploadId of expiredUploads) {
      uploadProgressMap.delete(uploadId);
      uploadChunksMap.delete(uploadId);
      logger.info(`Cleaned up expired upload: ${uploadId}`);
    }
  }

  // Get file info
  async getFileInfo(fileId: string, userId: string): Promise<FileMetadata> {
    try {
      const file = await fileDao.getFileById(fileId);
      if (!file) {
        throw new Error('File not found');
      }

      // Check permissions
      if (file.userId !== userId && !file.isPublic) {
        throw new Error('Access denied');
      }

      return file;
    } catch (error) {
      logger.error(`Failed to get file info for ${fileId}:`, error);
      throw error;
    }
  }

  // Rename folder
  async renameFolder(folderId: string, newName: string, userId: string): Promise<void> {
    try {
      const folder = await fileDao.getFolderById(folderId);
      if (!folder || folder.userId !== userId) {
        throw new Error('Folder not found or access denied');
      }

      // Check if name already exists in the same parent
      const isValid = await fileDao.validateFolderName(userId, folder.parentId, newName);
      if (!isValid) {
        throw new Error('Folder name already exists in this location');
      }

      await fileDao.renameFolder(folderId, newName);
      logger.info(`Folder renamed: ${folderId} to ${newName}`);
    } catch (error) {
      logger.error(`Failed to rename folder ${folderId}:`, error);
      throw error;
    }
  }

  // Get folder info
  // Get folder by ID (without user validation - for internal use)
  async getFolderById(folderId: string): Promise<Folder | null> {
    try {
      return await fileDao.getFolderById(folderId);
    } catch (error) {
      logger.error(`Failed to get folder ${folderId}:`, error);
      throw error;
    }
  }

  async getFolderInfo(folderId: string, userId: string): Promise<Folder> {
    try {
      const folder = await fileDao.getFolderById(folderId);
      if (!folder || folder.userId !== userId) {
        throw new Error('Folder not found or access denied');
      }
      return folder;
    } catch (error) {
      logger.error(`Failed to get folder info ${folderId}:`, error);
      throw error;
    }
  }

  // Move folder
  async moveFolder(folderId: string, targetFolderId: string, userId: string): Promise<void> {
    try {
      const folder = await fileDao.getFolderById(folderId);
      if (!folder || folder.userId !== userId) {
        throw new Error('Folder not found or access denied');
      }

      if (targetFolderId) {
        const targetFolder = await fileDao.getFolderById(targetFolderId);
        if (!targetFolder || targetFolder.userId !== userId) {
          throw new Error('Target folder not found or access denied');
        }

        // Check for circular reference
        if (await this.isCircularReference(folderId, targetFolderId)) {
          throw new Error('Cannot move folder into itself or its subfolder');
        }
      }

      await fileDao.moveFolder(folderId, targetFolderId);
      logger.info(`Folder moved: ${folderId} to ${targetFolderId}`);
    } catch (error) {
      logger.error(`Failed to move folder ${folderId}:`, error);
      throw error;
    }
  }

  // Delete folder to trash (soft delete)
  async deleteFolderToTrash(folderId: string, userId: string): Promise<void> {
    try {
      const folder = await fileDao.getFolderById(folderId);
      if (!folder || folder.userId !== userId) {
        throw new Error('Folder not found or access denied');
      }

      // Mark folder as deleted (move to trash)
      await fileDao.updateFolder(folderId, {
        isDeleted: true,
        deletedAt: new Date(),
        modifiedAt: new Date()
      });

      // Also mark all files and subfolders in this folder as deleted
      await this.markFolderContentsAsDeleted(folderId, userId);

      logger.info(`Folder moved to trash: ${folderId}`);
    } catch (error) {
      logger.error(`Failed to delete folder to trash ${folderId}:`, error);
      throw error;
    }
  }

  // Delete folder permanently
  async deleteFolderPermanently(folderId: string, userId: string): Promise<void> {
    try {
      const folder = await fileDao.getFolderById(folderId);
      if (!folder || folder.userId !== userId) {
        throw new Error('Folder not found or access denied');
      }

      // Delete all files in the folder permanently
      await this.deleteFolderContentsPermanently(folderId, userId);

      // Delete the folder itself
      await fileDao.deleteFolder(folderId);

      logger.info(`Folder permanently deleted: ${folderId}`);
    } catch (error) {
      logger.error(`Failed to permanently delete folder ${folderId}:`, error);
      throw error;
    }
  }

  // Rename file
  async renameFile(fileId: string, newName: string, userId: string): Promise<void> {
    try {
      const file = await fileDao.getFileById(fileId);
      if (!file || file.userId !== userId) {
        throw new Error('File not found or access denied');
      }

      await fileDao.updateFile(fileId, {
        originalName: newName,
        modifiedAt: new Date()
      });

      logger.info(`File renamed: ${fileId} to ${newName}`);
    } catch (error) {
      logger.error(`Failed to rename file ${fileId}:`, error);
      throw error;
    }
  }

  // Duplicate file
  async duplicateFile(fileId: string, userId: string, newName?: string): Promise<FileMetadata> {
    try {
      const originalFile = await fileDao.getFileById(fileId);
      if (!originalFile || originalFile.userId !== userId) {
        throw new Error('File not found or access denied');
      }

      // Generate new name if not provided
      const duplicateName = newName || this.generateDuplicateName(originalFile.originalName);

      // Create new file metadata
      const newFileId = crypto.randomUUID();
      const newFilename = `${newFileId}_${duplicateName}`;

      const duplicateFile: FileMetadata = {
        ...originalFile,
        id: newFileId,
        filename: newFilename,
        originalName: duplicateName,
        uploadedAt: new Date(),
        modifiedAt: new Date(),
        isDeleted: false,
        deletedAt: undefined
      };

      // Get original file chunks and copy them
      const originalChunks = await fileDao.getFileChunks(fileId);
      if (originalChunks) {
        // Download original file data
        const fileBuffer = await storageService.downloadFileChunked(originalChunks);

        // Upload as new file
        const newChunks = await storageService.uploadFileChunked(newFileId, fileBuffer, duplicateFile);

        // Save chunk information
        await fileDao.saveFileChunks(newFileId, newChunks);
      }

      // Save duplicate file metadata
      await fileDao.createFile(duplicateFile);

      logger.info(`File duplicated: ${fileId} -> ${newFileId}`);
      return duplicateFile;
    } catch (error) {
      logger.error(`Failed to duplicate file ${fileId}:`, error);
      throw error;
    }
  }

  // Copy file to folder
  async copyFile(fileId: string, userId: string, targetFolderId?: string): Promise<FileMetadata> {
    try {
      const originalFile = await fileDao.getFileById(fileId);
      if (!originalFile || originalFile.userId !== userId) {
        throw new Error('File not found or access denied');
      }

      // Validate target folder if provided
      if (targetFolderId) {
        const targetFolder = await fileDao.getFolderById(targetFolderId);
        if (!targetFolder || targetFolder.userId !== userId) {
          throw new Error('Target folder not found or access denied');
        }
      }

      // Generate copy name
      const copyName = this.generateCopyName(originalFile.originalName);

      // Create new file metadata
      const newFileId = crypto.randomUUID();
      const newFilename = `${newFileId}_${copyName}`;

      const copiedFile: FileMetadata = {
        ...originalFile,
        id: newFileId,
        filename: newFilename,
        originalName: copyName,
        folderId: targetFolderId,
        uploadedAt: new Date(),
        modifiedAt: new Date(),
        isDeleted: false,
        deletedAt: undefined
      };

      // Get original file chunks and copy them
      const originalChunks = await fileDao.getFileChunks(fileId);
      if (originalChunks) {
        // Download original file data
        const fileBuffer = await storageService.downloadFileChunked(originalChunks);

        // Upload as new file
        const newChunks = await storageService.uploadFileChunked(newFileId, fileBuffer, copiedFile);

        // Save chunk information
        await fileDao.saveFileChunks(newFileId, newChunks);
      }

      // Save copied file metadata
      await fileDao.createFile(copiedFile);

      logger.info(`File copied: ${fileId} -> ${newFileId} to folder ${targetFolderId || 'root'}`);
      return copiedFile;
    } catch (error) {
      logger.error(`Failed to copy file ${fileId}:`, error);
      throw error;
    }
  }

  // Helper method to generate duplicate name
  private generateDuplicateName(originalName: string): string {
    const lastDotIndex = originalName.lastIndexOf('.');
    if (lastDotIndex === -1) {
      return `${originalName} (copy)`;
    }

    const nameWithoutExt = originalName.substring(0, lastDotIndex);
    const extension = originalName.substring(lastDotIndex);
    return `${nameWithoutExt} (copy)${extension}`;
  }

  // Helper method to generate copy name
  private generateCopyName(originalName: string): string {
    const lastDotIndex = originalName.lastIndexOf('.');
    if (lastDotIndex === -1) {
      return `${originalName} - Copy`;
    }

    const nameWithoutExt = originalName.substring(0, lastDotIndex);
    const extension = originalName.substring(lastDotIndex);
    return `${nameWithoutExt} - Copy${extension}`;
  }

  // Move file to trash (soft delete)
  async moveFileToTrash(fileId: string, userId: string): Promise<void> {
    try {
      const file = await fileDao.getFileById(fileId);
      if (!file || file.userId !== userId) {
        throw new Error('File not found or access denied');
      }

      await fileDao.updateFile(fileId, {
        isDeleted: true,
        deletedAt: new Date(),
        modifiedAt: new Date()
      });

      logger.info(`File moved to trash: ${fileId}`);
    } catch (error) {
      logger.error(`Failed to move file to trash ${fileId}:`, error);
      throw error;
    }
  }

  // Get trash contents
  async getTrashContents(userId: string, pagination: Pagination): Promise<FileList> {
    try {
      const { files, totalCount } = await fileDao.getDeletedFiles(userId, pagination);
      const folders = await fileDao.getDeletedFolders(userId);

      return {
        files,
        folders,
        totalCount,
        hasMore: (pagination.page * pagination.limit) < totalCount
      };
    } catch (error) {
      logger.error(`Failed to get trash contents for user ${userId}:`, error);
      throw new Error('Failed to get trash contents');
    }
  }

  // Restore file or folder from trash
  async restoreFromTrash(itemId: string, userId: string): Promise<void> {
    try {
      // First try to find as a file
      const file = await fileDao.getFileById(itemId);
      if (file && file.userId === userId) {
        if (!file.isDeleted) {
          throw new Error('File is not in trash');
        }

        await fileDao.updateFile(itemId, {
          isDeleted: false,
          deletedAt: undefined,
          modifiedAt: new Date()
        });

        logger.info(`File restored from trash: ${itemId}`);
        return;
      }

      // If not found as file, try to find as folder
      const folder = await fileDao.getFolderById(itemId);
      if (folder && folder.userId === userId) {
        if (!folder.isDeleted) {
          throw new Error('Folder is not in trash');
        }

        await fileDao.updateFolder(itemId, {
          isDeleted: false,
          deletedAt: undefined,
          modifiedAt: new Date()
        });

        // Also restore all contents of the folder
        await this.restoreFolderContents(itemId, userId);

        logger.info(`Folder restored from trash: ${itemId}`);
        return;
      }

      throw new Error('Item not found or access denied');
    } catch (error) {
      logger.error(`Failed to restore item from trash ${itemId}:`, error);
      throw error;
    }
  }

  // Empty trash
  async emptyTrash(userId: string): Promise<number> {
    try {
      const deletedFiles = await fileDao.getDeletedFiles(userId, { page: 1, limit: 1000 });
      const deletedFolders = await fileDao.getDeletedFolders(userId);

      let deletedCount = 0;

      // Permanently delete all files in trash
      for (const file of deletedFiles.files) {
        await this.deleteFile(file.id, userId);
        deletedCount++;
      }

      // Permanently delete all folders in trash
      for (const folder of deletedFolders) {
        await fileDao.deleteFolder(folder.id);
        deletedCount++;
      }

      logger.info(`Emptied trash for user ${userId}: ${deletedCount} items deleted`);
      return deletedCount;
    } catch (error) {
      logger.error(`Failed to empty trash for user ${userId}:`, error);
      throw new Error('Failed to empty trash');
    }
  }

  // Helper method to check circular reference
  private async isCircularReference(folderId: string, targetFolderId: string): Promise<boolean> {
    if (folderId === targetFolderId) {
      return true;
    }

    const targetFolder = await fileDao.getFolderById(targetFolderId);
    if (!targetFolder || !targetFolder.parentId) {
      return false;
    }

    return await this.isCircularReference(folderId, targetFolder.parentId);
  }

  // Helper method to restore folder contents from trash
  private async restoreFolderContents(folderId: string, userId: string): Promise<void> {
    // Get all files in the folder (including deleted ones)
    const files = await fileDao.getFilesByUserId(userId, folderId);
    for (const file of files) {
      if (file.isDeleted) {
        await fileDao.updateFile(file.id, {
          isDeleted: false,
          deletedAt: undefined,
          modifiedAt: new Date()
        });
      }
    }

    // Get all subfolders and recursively restore them
    const subfolders = await fileDao.getFoldersByUserId(userId, folderId);
    for (const subfolder of subfolders) {
      if (subfolder.isDeleted) {
        await fileDao.updateFolder(subfolder.id, {
          isDeleted: false,
          deletedAt: undefined,
          modifiedAt: new Date()
        });
        await this.restoreFolderContents(subfolder.id, userId);
      }
    }
  }

  // Helper method to mark folder contents as deleted
  private async markFolderContentsAsDeleted(folderId: string, userId: string): Promise<void> {
    // Get all files in the folder
    const files = await fileDao.getFilesByUserId(userId, folderId);
    for (const file of files) {
      await fileDao.updateFile(file.id, {
        isDeleted: true,
        deletedAt: new Date(),
        modifiedAt: new Date()
      });
    }

    // Get all subfolders and recursively mark them as deleted
    const subfolders = await fileDao.getFoldersByUserId(userId, folderId);
    for (const subfolder of subfolders) {
      await fileDao.updateFolder(subfolder.id, {
        isDeleted: true,
        deletedAt: new Date(),
        modifiedAt: new Date()
      });
      await this.markFolderContentsAsDeleted(subfolder.id, userId);
    }
  }

  // Helper method to permanently delete folder contents
  private async deleteFolderContentsPermanently(folderId: string, userId: string): Promise<void> {
    // Get all files in the folder and delete them permanently
    const files = await fileDao.getFilesByUserId(userId, folderId);
    for (const file of files) {
      await this.deleteFile(file.id, userId);
    }

    // Get all subfolders and recursively delete them
    const subfolders = await fileDao.getFoldersByUserId(userId, folderId);
    for (const subfolder of subfolders) {
      await this.deleteFolderContentsPermanently(subfolder.id, userId);
      await fileDao.deleteFolder(subfolder.id);
    }
  }

  // Advanced sharing methods
  async getFileShares(fileId: string, userId: string): Promise<ShareLink[]> {
    try {
      const file = await fileDao.getFileById(fileId);
      if (!file || file.userId !== userId) {
        throw new Error('File not found or access denied');
      }

      const shares = await fileDao.getShareLinksByFileId(fileId);
      logger.info(`Retrieved ${shares.length} share links for file ${fileId}`);

      return shares;
    } catch (error) {
      logger.error(`Failed to get shares for file ${fileId}:`, error);
      throw error;
    }
  }

  async updateShareLink(shareId: string, userId: string, updates: Partial<ShareOptions>): Promise<void> {
    try {
      const shareLink = await fileDao.getShareLinkById(shareId);
      if (!shareLink || shareLink.userId !== userId) {
        throw new Error('Share link not found or access denied');
      }

      await fileDao.updateShareLink(shareId, updates);
      logger.info(`Share link updated: ${shareId}`);
    } catch (error) {
      logger.error(`Failed to update share link ${shareId}:`, error);
      throw error;
    }
  }

  async revokeShareLink(shareId: string, userId: string): Promise<void> {
    try {
      const shareLink = await fileDao.getShareLinkById(shareId);
      if (!shareLink || shareLink.userId !== userId) {
        throw new Error('Share link not found or access denied');
      }

      await fileDao.deleteShareLink(shareId);
      logger.info(`Share link revoked: ${shareId}`);
    } catch (error) {
      logger.error(`Failed to revoke share link ${shareId}:`, error);
      throw error;
    }
  }

  async getShareStats(userId: string): Promise<{
    totalShares: number;
    activeShares: number;
    totalDownloads: number;
  }> {
    try {
      const stats = await fileDao.getUserShareStats(userId);
      logger.info(`Retrieved share stats for user ${userId}`);
      return stats;
    } catch (error) {
      logger.error(`Failed to get share stats for user ${userId}:`, error);
      throw new Error('Failed to get share statistics');
    }
  }

  async getUserShares(userId: string): Promise<Array<{
    id: string;
    fileId: string;
    fileName: string;
    fileSize: number;
    fileMimeType: string;
    token: string;
    permissions: any[];
    downloadCount: number;
    maxDownloads?: number;
    createdAt: Date;
    expiresAt?: Date;
    password?: string;
  }>> {
    try {
      const shares = await fileDao.getShareLinksByUserId(userId);
      const sharesWithFileInfo = await Promise.all(
        shares.map(async (share) => {
          const file = await fileDao.getFileById(share.fileId);
          return {
            id: share.id,
            fileId: share.fileId,
            fileName: file?.originalName || 'Unknown File',
            fileSize: file?.size || 0,
            fileMimeType: file?.mimeType || 'application/octet-stream',
            token: share.token,
            permissions: share.permissions,
            downloadCount: share.downloadCount,
            maxDownloads: share.maxDownloads,
            createdAt: share.createdAt,
            expiresAt: share.expiresAt,
            password: share.password
          };
        })
      );

      logger.info(`Retrieved ${sharesWithFileInfo.length} shares for user ${userId}`);
      return sharesWithFileInfo;
    } catch (error) {
      logger.error(`Failed to get user shares for user ${userId}:`, error);
      throw new Error('Failed to get user shares');
    }
  }

  async getRecentShares(userId: string, limit: number = 10): Promise<Array<{
    id: string;
    fileName: string;
    createdAt: Date;
    downloadCount: number;
    isActive: boolean;
  }>> {
    try {
      const shares = await fileDao.getShareLinksByUserId(userId);
      const recentShares = await Promise.all(
        shares
          .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
          .slice(0, limit)
          .map(async (share) => {
            const file = await fileDao.getFileById(share.fileId);
            const isActive = !share.expiresAt || new Date(share.expiresAt) > new Date();
            
            return {
              id: share.id,
              fileName: file?.originalName || 'Unknown File',
              createdAt: share.createdAt,
              downloadCount: share.downloadCount,
              isActive
            };
          })
      );

      logger.info(`Retrieved ${recentShares.length} recent shares for user ${userId}`);
      return recentShares;
    } catch (error) {
      logger.error(`Failed to get recent shares for user ${userId}:`, error);
      throw new Error('Failed to get recent shares');
    }
  }

  async getDownloadsByDay(userId: string, days: number = 30): Promise<Array<{
    date: string;
    downloads: number;
  }>> {
    try {
      // This would ideally be implemented with proper analytics tracking
      // For now, we'll create a simplified version
      const shares = await fileDao.getShareLinksByUserId(userId);
      const downloadsByDay: { [key: string]: number } = {};
      
      // Initialize all days with 0 downloads
      for (let i = 0; i < days; i++) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const dateStr = date.toISOString().split('T')[0];
        downloadsByDay[dateStr] = 0;
      }
      
      // Simulate download distribution (in a real implementation, this would come from analytics)
      shares.forEach(share => {
        if (share.downloadCount > 0) {
          // Distribute downloads across recent days (simplified simulation)
          const daysToDistribute = Math.min(7, days);
          const downloadsPerDay = Math.floor(share.downloadCount / daysToDistribute);
          
          for (let i = 0; i < daysToDistribute; i++) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];
            downloadsByDay[dateStr] += downloadsPerDay;
          }
        }
      });

      const result = Object.entries(downloadsByDay)
        .map(([date, downloads]) => ({ date, downloads }))
        .sort((a, b) => a.date.localeCompare(b.date));

      logger.info(`Retrieved download statistics for ${days} days for user ${userId}`);
      return result;
    } catch (error) {
      logger.error(`Failed to get downloads by day for user ${userId}:`, error);
      throw new Error('Failed to get download statistics');
    }
  }

  async getPublicShareInfo(token: string, password?: string): Promise<{
    fileInfo: {
      id: string;
      filename: string;
      size: number;
      mimeType: string;
      uploadedAt: Date;
    };
    shareInfo: {
      downloadCount: number;
      maxDownloads?: number;
      expiresAt?: Date;
      requiresPassword: boolean;
    };
  }> {
    try {
      const validation = await fileDao.validateShareLink(token, password);
      if (!validation.valid || !validation.shareLink) {
        throw new Error(validation.reason || 'Invalid share link');
      }

      const file = await fileDao.getFileById(validation.shareLink.fileId);
      if (!file) {
        throw new Error('File not found');
      }

      return {
        fileInfo: {
          id: file.id,
          filename: file.originalName,
          size: file.size,
          mimeType: file.mimeType,
          uploadedAt: file.uploadedAt
        },
        shareInfo: {
          downloadCount: validation.shareLink.downloadCount,
          maxDownloads: validation.shareLink.maxDownloads,
          expiresAt: validation.shareLink.expiresAt,
          requiresPassword: !!validation.shareLink.password
        }
      };
    } catch (error) {
      logger.error(`Failed to get public share info for token ${token}:`, error);
      throw error;
    }
  }

  async downloadViaPublicShare(token: string, password?: string): Promise<FileStream> {
    try {
      const validation = await fileDao.validateShareLink(token, password);
      if (!validation.valid || !validation.shareLink) {
        throw new Error(validation.reason || 'Invalid share link');
      }

      const file = await fileDao.getFileById(validation.shareLink.fileId);
      if (!file) {
        throw new Error('File not found');
      }

      // Record the download
      await fileDao.recordDownload(validation.shareLink.id);

      // Get chunk information
      const fileChunks = await fileDao.getFileChunks(validation.shareLink.fileId);
      if (!fileChunks) {
        throw new Error('File chunks not found');
      }

      // Download file from distributed storage
      const fileBuffer = await storageService.downloadFileChunked(fileChunks);

      // Create readable stream
      const { Readable } = require('stream');
      const stream = Readable.from(fileBuffer);

      logger.info(`File downloaded via public share: ${validation.shareLink.fileId}`);

      return {
        stream,
        metadata: file
      };
    } catch (error) {
      logger.error(`Failed to download via public share for token ${token}:`, error);
      throw error;
    }
  }

  async previewViaPublicShare(token: string, password?: string): Promise<FileStream> {
    try {
      const validation = await fileDao.validateShareLink(token, password);
      if (!validation.valid || !validation.shareLink) {
        throw new Error(validation.reason || 'Invalid share link');
      }

      const file = await fileDao.getFileById(validation.shareLink.fileId);
      if (!file) {
        throw new Error('File not found');
      }

      // Get chunk information
      const fileChunks = await fileDao.getFileChunks(validation.shareLink.fileId);
      if (!fileChunks) {
        throw new Error('File chunks not found');
      }

      // Download file from distributed storage
      const fileBuffer = await storageService.downloadFileChunked(fileChunks);

      // Create readable stream
      const { Readable } = require('stream');
      const stream = Readable.from(fileBuffer);

      logger.info(`File previewed via public share: ${validation.shareLink.fileId}`);

      return {
        stream,
        metadata: file
      };
    } catch (error) {
      logger.error(`Failed to preview via public share for token ${token}:`, error);
      throw error;
    }
  }

  // Cleanup expired shares (should be called periodically)
  async cleanupExpiredShares(): Promise<number> {
    try {
      const deletedCount = await fileDao.cleanupExpiredShares();
      logger.info(`Cleaned up ${deletedCount} expired share links`);
      return deletedCount;
    } catch (error) {
      logger.error('Failed to cleanup expired shares:', error);
      throw new Error('Failed to cleanup expired shares');
    }
  }

  // Debug methods
  async getAllFilesForUser(userId: string): Promise<FileMetadata[]> {
    const { files } = await fileDao.getFilesByUser(userId, undefined, { page: 1, limit: 1000 });
    return files;
  }

  async getAllFoldersForUser(userId: string): Promise<Folder[]> {
    return await fileDao.getRootFolders(userId);
  }
}

export const fileService = new FileService();
export default fileService;