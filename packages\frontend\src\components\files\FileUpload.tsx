import React, { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import Modal from '../ui/Modal'
import Button from '../ui/Button'
import {
  CloudArrowUpIcon,
  XMarkIcon,
  DocumentIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  CameraIcon,
  PlayIcon,
  PauseIcon
} from '@heroicons/react/24/outline'
import { cn } from '../../utils/cn'
import { CameraCapture } from './CameraCapture'
import { useDataSavingMode } from '../../hooks/useDataSavingMode'
import { useOfflineAccess } from '../../hooks/useOfflineAccess'
import { usePushNotifications } from '../../hooks/usePushNotifications'
import { fileService, ALLOWED_FILE_TYPES, MAX_FILE_SIZE } from '../../services/fileService'
import { imageBedService } from '../../services/imageBedService'
import { FileInfo } from '@cloud-storage/shared'
import { useI18n } from '../../contexts/I18nContext'

interface FileUploadProps {
  onClose: () => void
  onUploadComplete: (files: FileInfo[]) => void
  currentFolderId?: string
  isImageBedFolder?: boolean
}

interface UploadFile {
  file: File
  id: string
  status: 'pending' | 'uploading' | 'completed' | 'error' | 'paused'
  progress: number
  error?: string
  uploadId?: string
  result?: FileInfo
}

const FileUpload: React.FC<FileUploadProps> = ({
  onClose,
  onUploadComplete,
  currentFolderId,
  isImageBedFolder = false
}) => {
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const [showCamera, setShowCamera] = useState(false)

  const { isOnline, addOfflineFile } = useOfflineAccess()
  const { getImageQuality } = useDataSavingMode()
  const { notifyFileUpload } = usePushNotifications()
  const { t } = useI18n()

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    // Handle rejected files
    rejectedFiles.forEach(({ file, errors }) => {
      console.warn(`File ${file.name} rejected:`, errors)
    })

    // Filter files for image bed folder
    let filesToProcess = acceptedFiles;
    if (isImageBedFolder) {
      filesToProcess = acceptedFiles.filter(file => file.type.startsWith('image/'));
      if (filesToProcess.length < acceptedFiles.length) {
        console.warn('Some non-image files were filtered out in image bed folder');
      }
    }

    // Validate accepted files
    const validFiles: UploadFile[] = []
    filesToProcess.forEach(file => {
      const validation = fileService.validateFile(file)
      if (validation.valid) {
        validFiles.push({
          file,
          id: `${file.name}-${Date.now()}-${Math.random()}`,
          status: 'pending',
          progress: 0
        })
      } else {
        console.error(`File ${file.name} validation failed:`, validation.error)
        // You could show a toast notification here
      }
    })

    setUploadFiles(prev => [...prev, ...validFiles])
  }, [isImageBedFolder])

  const handleCameraCapture = useCallback(async (file: File) => {
    // Compress image if data saving mode is enabled
    const quality = getImageQuality()
    let processedFile = file

    if (quality < 0.9 && file.type.startsWith('image/')) {
      processedFile = await compressImage(file, quality)
    }

    // If offline, save to offline storage
    if (!isOnline) {
      const saved = await addOfflineFile(processedFile)
      if (saved) {
        notifyFileUpload(processedFile.name, true)
      }
      return
    }

    // Add to upload queue
    const uploadFile: UploadFile = {
      file: processedFile,
      id: `camera-${Date.now()}-${Math.random()}`,
      status: 'pending',
      progress: 0
    }

    setUploadFiles(prev => [...prev, uploadFile])
    setShowCamera(false)
  }, [getImageQuality, isOnline, addOfflineFile, notifyFileUpload])

  const compressImage = async (file: File, quality: number): Promise<File> => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      img.onload = () => {
        // Calculate new dimensions (max 1920x1080 for data saving)
        const maxWidth = 1920
        const maxHeight = 1080
        let { width, height } = img

        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height)
          width *= ratio
          height *= ratio
        }

        canvas.width = width
        canvas.height = height
        ctx?.drawImage(img, 0, 0, width, height)

        canvas.toBlob((blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: 'image/jpeg',
              lastModified: Date.now()
            })
            resolve(compressedFile)
          } else {
            resolve(file)
          }
        }, 'image/jpeg', quality)
      }

      img.src = URL.createObjectURL(file)
    })
  }

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: Object.keys(ALLOWED_FILE_TYPES).reduce((acc, type) => {
      acc[type] = ALLOWED_FILE_TYPES[type as keyof typeof ALLOWED_FILE_TYPES];
      return acc;
    }, {} as Record<string, string[]>),
    maxSize: MAX_FILE_SIZE,
    multiple: true
  })

  const removeFile = (id: string) => {
    const uploadFile = uploadFiles.find(f => f.id === id)
    if (uploadFile?.uploadId && uploadFile.status === 'uploading') {
      fileService.cancelUpload(uploadFile.uploadId)
    }
    setUploadFiles(prev => prev.filter(f => f.id !== id))
  }

  const pauseUpload = (id: string) => {
    const uploadFile = uploadFiles.find(f => f.id === id)
    if (uploadFile?.uploadId) {
      fileService.cancelUpload(uploadFile.uploadId)
      setUploadFiles(prev => prev.map(f =>
        f.id === id ? { ...f, status: 'paused' } : f
      ))
    }
  }

  const resumeUpload = async (id: string) => {
    const uploadFile = uploadFiles.find(f => f.id === id)
    if (!uploadFile || uploadFile.status !== 'paused') return

    setUploadFiles(prev => prev.map(f =>
      f.id === id ? { ...f, status: 'uploading', error: undefined } : f
    ))

    await uploadSingleFile(uploadFile)
  }

  const formatFileSize = (bytes: number) => {
    return fileService.formatFileSize(bytes)
  }

  const uploadSingleFile = async (uploadFile: UploadFile): Promise<FileInfo | null> => {
    try {
      setUploadFiles(prev => prev.map(f =>
        f.id === uploadFile.id
          ? { ...f, status: 'uploading', progress: 0 }
          : f
      ))

      let result: FileInfo

      if (isImageBedFolder) {
        // Only use image bed service when in image bed folder
        const formData = new FormData()
        formData.append('file', uploadFile.file)

        const imageInfo = await imageBedService.uploadToImageBed(formData)

        // Convert image bed result to FileInfo format
        result = {
          id: imageInfo.id,
          filename: imageInfo.originalName,
          originalName: imageInfo.originalName,
          mimeType: uploadFile.file.type,
          size: uploadFile.file.size,
          uploadedAt: new Date(imageInfo.createdAt),
          modifiedAt: new Date(imageInfo.createdAt),
          isPublic: true,
          tags: [],
          userId: '', // Will be set by backend
          folderId: currentFolderId || null,
          thumbnailUrl: imageInfo.publicUrl,
          downloadUrl: imageInfo.publicUrl
        } as FileInfo
      } else {
        // Use regular file service for all files in non-image-bed folders
        result = await fileService.resumableUpload(
          uploadFile.file,
          currentFolderId,
          undefined,
          (progress) => {
            setUploadFiles(prev => prev.map(f =>
              f.id === uploadFile.id
                ? { ...f, progress }
                : f
            ))
          },
          (chunkIndex, totalChunks) => {
            console.log(`Chunk ${chunkIndex + 1}/${totalChunks} completed for ${uploadFile.file.name}`)
          }
        )
      }

      setUploadFiles(prev => prev.map(f =>
        f.id === uploadFile.id
          ? { ...f, status: 'completed', progress: 100, result }
          : f
      ))

      // Notify about successful upload
      notifyFileUpload(uploadFile.file.name, false)

      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : t('files.uploadFailed')
      setUploadFiles(prev => prev.map(f =>
        f.id === uploadFile.id
          ? { ...f, status: 'error', error: errorMessage }
          : f
      ))
      console.error('Upload failed for file:', uploadFile.file.name, 'Error:', error)
      console.error('Upload context:', {
        isImageBedFolder,
        currentFolderId,
        fileType: uploadFile.file.type,
        fileSize: uploadFile.file.size
      })
      return null
    }
  }

  const handleUpload = async () => {
    const pendingFiles = uploadFiles.filter(f => f.status === 'pending')
    if (pendingFiles.length === 0) return

    setIsUploading(true)

    try {
      // Upload files with limited concurrency (3 concurrent uploads)
      const concurrencyLimit = 3
      const results: FileInfo[] = []

      for (let i = 0; i < pendingFiles.length; i += concurrencyLimit) {
        const batch = pendingFiles.slice(i, i + concurrencyLimit)
        const batchPromises = batch.map(uploadFile => uploadSingleFile(uploadFile))
        const batchResults = await Promise.allSettled(batchPromises)

        batchResults.forEach(result => {
          if (result.status === 'fulfilled' && result.value) {
            results.push(result.value)
          }
        })
      }

      // Notify parent component about successful uploads
      if (results.length > 0) {
        onUploadComplete(results)
      }
    } finally {
      setIsUploading(false)
    }
  }

  const pendingFiles = uploadFiles.filter(f => f.status === 'pending')
  const hasCompletedFiles = uploadFiles.some(f => f.status === 'completed')
  const hasErrors = uploadFiles.some(f => f.status === 'error')

  return (
    <Modal
      isOpen={true}
      onClose={onClose}
      title={isImageBedFolder ? t('files.uploadToImageBed') || '上传到图床' : t('files.uploadFiles')}
      size="lg"
    >
      <div className="space-y-6 p-6">
        {/* Upload Options */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Drop Zone */}
          <div
            {...getRootProps()}
            className={cn(
              'border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors min-h-[160px] flex flex-col justify-center',
              isDragActive
                ? 'border-dracula-purple bg-dracula-purple/10'
                : 'border-dracula-current-line hover:border-dracula-purple hover:bg-dracula-current-line/50'
            )}
          >
            <input {...getInputProps()} />
            <CloudArrowUpIcon className="w-10 h-10 text-dracula-comment mx-auto mb-3" />

            {isDragActive ? (
              <p className="text-dracula-purple font-medium text-sm">
                {t('files.dropToUpload')}
              </p>
            ) : (
              <div>
                <p className="text-dracula-foreground font-medium mb-1 text-sm">
                  {isImageBedFolder ? (t('files.selectImages') || '选择图片') : t('files.selectFiles')}
                </p>
                <p className="text-xs text-dracula-comment mb-2">
                  {t('files.dragOrClick')}
                </p>
                <p className="text-xs text-dracula-comment">
                  {t('files.maxSize')}: {fileService.formatFileSize(MAX_FILE_SIZE)}
                </p>
                <p className="text-xs text-dracula-comment">
                  {isImageBedFolder ? (t('files.onlyImagesAllowed') || '只允许图片文件') : t('files.supportedTypes')}
                </p>
              </div>
            )}
          </div>

          {/* Camera Capture */}
          <div
            onClick={() => setShowCamera(true)}
            className="border-2 border-dashed border-dracula-current-line hover:border-dracula-pink hover:bg-dracula-current-line/50 rounded-lg p-8 text-center cursor-pointer transition-colors min-h-[160px] flex flex-col justify-center"
          >
            <CameraIcon className="w-10 h-10 text-dracula-comment mx-auto mb-3" />
            <p className="text-dracula-foreground font-medium mb-1 text-sm">
              {t('files.cameraUpload')}
            </p>
            <p className="text-xs text-dracula-comment">
              {t('files.takePhoto')}
            </p>
          </div>
        </div>

        {/* Offline Status */}
        {!isOnline && (
          <div className="bg-dracula-orange/10 border border-dracula-orange/20 rounded-lg p-3">
            <p className="text-sm text-dracula-orange">
              {t('files.offlineMode')}
            </p>
          </div>
        )}

        {/* File List */}
        {uploadFiles.length > 0 && (
          <div className="space-y-3 max-h-64 overflow-y-auto">
            <h4 className="text-sm font-medium text-dracula-foreground mb-3">
              {t('files.filesToUpload')} ({uploadFiles.length})
            </h4>

            {uploadFiles.map((uploadFile) => (
              <div
                key={uploadFile.id}
                className="flex items-center space-x-3 p-4 bg-dracula-current-line rounded-lg border border-dracula-current-line/50"
              >
                <DocumentIcon className="w-5 h-5 text-dracula-comment flex-shrink-0" />

                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-dracula-foreground truncate">
                      {uploadFile.file.name}
                    </p>
                    <span className="text-xs text-dracula-comment ml-2">
                      {formatFileSize(uploadFile.file.size)}
                    </span>
                  </div>

                  {/* Progress Bar */}
                  {uploadFile.status === 'uploading' && (
                    <div className="mt-2">
                      <div className="w-full bg-dracula-background rounded-full h-1.5">
                        <div
                          className="bg-dracula-purple h-1.5 rounded-full transition-all duration-300"
                          style={{ width: `${uploadFile.progress}%` }}
                        />
                      </div>
                      <p className="text-xs text-dracula-comment mt-1">
                        {Math.round(uploadFile.progress)}%
                      </p>
                    </div>
                  )}

                  {/* Error Message */}
                  {uploadFile.status === 'error' && (
                    <p className="text-xs text-dracula-red mt-1">
                      {uploadFile.error}
                    </p>
                  )}
                </div>

                {/* Status Icon and Controls */}
                <div className="flex-shrink-0 flex items-center space-x-2">
                  {uploadFile.status === 'completed' && (
                    <CheckCircleIcon className="w-5 h-5 text-dracula-green" />
                  )}
                  {uploadFile.status === 'error' && (
                    <ExclamationTriangleIcon className="w-5 h-5 text-dracula-red" />
                  )}
                  {uploadFile.status === 'uploading' && (
                    <>
                      <div className="w-5 h-5 border-2 border-dracula-purple border-t-transparent rounded-full animate-spin" />
                      <button
                        onClick={() => pauseUpload(uploadFile.id)}
                        className="text-dracula-comment hover:text-dracula-yellow transition-colors"
                        title={t('files.pauseUpload')}
                      >
                        <PauseIcon className="w-4 h-4" />
                      </button>
                    </>
                  )}
                  {uploadFile.status === 'paused' && (
                    <button
                      onClick={() => resumeUpload(uploadFile.id)}
                      className="text-dracula-comment hover:text-dracula-green transition-colors"
                      title={t('files.resumeUpload')}
                    >
                      <PlayIcon className="w-4 h-4" />
                    </button>
                  )}
                  {(uploadFile.status === 'pending' || uploadFile.status === 'error' || uploadFile.status === 'completed') && (
                    <button
                      onClick={() => removeFile(uploadFile.id)}
                      className="text-dracula-comment hover:text-dracula-red transition-colors"
                      disabled={isUploading}
                      title={t('files.removeFile')}
                    >
                      <XMarkIcon className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Upload Status */}
        {hasCompletedFiles && (
          <div className="flex items-center space-x-2 text-dracula-green">
            <CheckCircleIcon className="w-5 h-5" />
            <span className="text-sm">
              {uploadFiles.filter(f => f.status === 'completed').length} {t('files.filesUploadedSuccess')}
            </span>
          </div>
        )}

        {hasErrors && (
          <div className="flex items-center space-x-2 text-dracula-red">
            <ExclamationTriangleIcon className="w-5 h-5" />
            <span className="text-sm">
              {uploadFiles.filter(f => f.status === 'error').length} {t('files.filesUploadFailed')}
            </span>
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-end space-x-3 pt-6 mt-4 border-t border-dracula-current-line">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isUploading}
            className="px-6 py-2.5"
          >
            {hasCompletedFiles ? t('files.done') : t('common.cancel')}
          </Button>

          {pendingFiles.length > 0 && (
            <Button
              variant="primary"
              onClick={handleUpload}
              loading={isUploading}
              disabled={pendingFiles.length === 0}
              className="px-6 py-2.5"
            >
              {t('files.uploadCount', { count: String(pendingFiles.length) })}
            </Button>
          )}
        </div>
      </div>

      {/* Camera Capture Modal */}
      <CameraCapture
        isOpen={showCamera}
        onClose={() => setShowCamera(false)}
        onCapture={handleCameraCapture}
      />
    </Modal>
  )
}

export default FileUpload