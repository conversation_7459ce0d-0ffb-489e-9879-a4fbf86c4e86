import { Request, Response } from 'express';
export declare class ImageBedController {
    /**
     * Get or create image bed folder
     */
    getOrCreateImageBedFolder(req: Request, res: Response): Promise<void>;
    /**
     * Upload image directly to image bed
     */
    uploadToImageBed(req: Request, res: Response): Promise<void>;
    /**
     * Move image to image bed
     */
    moveImageToImageBed(req: Request, res: Response): Promise<void>;
    /**
     * Get public image info
     */
    getPublicImageInfo(req: Request, res: Response): Promise<void>;
    /**
     * List image bed images
     */
    listImageBedImages(req: Request, res: Response): Promise<void>;
    /**
     * Remove image from image bed
     */
    removeFromImageBed(req: Request, res: Response): Promise<void>;
    /**
     * Generate or get public link for an image
     */
    generatePublicLink(req: Request, res: Response): Promise<void>;
    /**
     * Serve public image (for public access without authentication)
     */
    servePublicImage(req: Request, res: Response): Promise<void>;
    /**
     * Clean up duplicate image bed folders
     */
    cleanupImageBedFolders(req: Request, res: Response): Promise<void>;
}
export declare const imageBedController: ImageBedController;
//# sourceMappingURL=image-bed.controller.d.ts.map