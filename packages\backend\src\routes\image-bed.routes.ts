import { Router } from 'express'
import multer from 'multer'
import { imageBedController } from '../controllers/image-bed.controller'
import { authMiddleware } from '../middleware/auth.middleware'

// Configure multer for image bed uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB per file
    files: 10 // Max 10 files at once
  },
  fileFilter: (req, file, cb) => {
    // Only allow image files
    if (file.mimetype.startsWith('image/')) {
      cb(null, true)
    } else {
      cb(new Error('Only image files are allowed'))
    }
  }
})

export function createImageBedRoutes(): Router {
  const router = Router()

  // Get or create image bed folder
  router.get(
    '/folder',
    authMiddleware,
    imageBedController.getOrCreateImageBedFolder
  )

  // Upload image directly to image bed
  router.post(
    '/upload',
    authMiddleware,
    upload.single('file'),
    imageBedController.uploadToImageBed
  )

  // Move image to image bed
  router.post(
    '/move/:fileId',
    authMiddleware,
    imageBedController.moveImageToImageBed
  )

  // List images in image bed
  router.get(
    '/images',
    authMiddleware,
    imageBedController.listImageBedImages
  )

  // Remove image from image bed
  router.post(
    '/remove/:imageId',
    authMiddleware,
    imageBedController.removeFromImageBed
  )

  // Generate public link for an image
  router.post(
    '/public-link/:imageId',
    authMiddleware,
    imageBedController.generatePublicLink
  )

  // Get public image info (authenticated)
  router.get(
    '/info/:imageId',
    authMiddleware,
    imageBedController.getPublicImageInfo
  )

  // Get public image info (no authentication required)
  router.get(
    '/public/:imageId/info',
    imageBedController.getPublicImageInfo
  )

  // Serve public image (no authentication required)
  router.get(
    '/public/:imageId',
    imageBedController.servePublicImage
  )

  // Clean up duplicate image bed folders
  router.post(
    '/cleanup',
    authMiddleware,
    imageBedController.cleanupImageBedFolders
  )

  // Health check for image bed service
  router.get('/health/check', (req, res) => {
    res.json({
      service: 'image-bed-service',
      status: 'healthy',
      timestamp: new Date().toISOString()
    })
  })

  return router
}
