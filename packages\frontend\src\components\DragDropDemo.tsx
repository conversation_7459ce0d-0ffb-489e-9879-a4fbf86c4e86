import React from 'react'
import { useI18n } from '../contexts/I18nContext'

/**
 * Demo component to show drag and drop functionality
 * This component demonstrates how to drag files/folders to sidebar menu items
 */
const DragDropDemo: React.FC = () => {
  const { t } = useI18n()

  return (
    <div className="p-6 bg-current-line rounded-lg border border-primary">
      <h3 className="text-lg font-semibold text-primary mb-4">
        {t('files.dragDropDemo') || 'Drag & Drop Demo'}
      </h3>
      
      <div className="space-y-4">
        <div className="p-4 bg-purple-500/10 border border-purple-500/30 rounded-lg">
          <h4 className="font-medium text-primary mb-2">
            {t('files.dragToShare') || 'Drag to Share'}
          </h4>
          <p className="text-sm text-comment">
            {t('files.dragToShareDesc') || 'Drag any file or folder (except image bed items) to the "Shared Files" menu item to create a public share link.'}
          </p>
        </div>
        
        <div className="p-4 bg-red-500/10 border border-red-500/30 rounded-lg">
          <h4 className="font-medium text-primary mb-2">
            {t('files.dragToTrash') || 'Drag to Trash'}
          </h4>
          <p className="text-sm text-comment">
            {t('files.dragToTrashDesc') || 'Drag any file or folder (except image bed items) to the "Trash" menu item to move it to trash.'}
          </p>
        </div>
        
        <div className="p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
          <h4 className="font-medium text-primary mb-2">
            {t('files.restrictions') || 'Restrictions'}
          </h4>
          <p className="text-sm text-comment">
            {t('files.restrictionsDesc') || 'Image bed folder and its contents cannot be dragged to prevent accidental operations.'}
          </p>
        </div>
      </div>
    </div>
  )
}

export default DragDropDemo
