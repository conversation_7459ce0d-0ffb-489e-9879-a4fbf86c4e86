import React, { createContext, useContext, useState, useCallback } from 'react'
import ToastContainer from '../components/ui/ToastContainer'
import ConfirmDialog from '../components/ui/ConfirmDialog'
import PromptDialog from '../components/ui/PromptDialog'
import { ToastType, ToastProps } from '../components/ui/Toast'

interface ConfirmDialogState {
  isOpen: boolean
  title: string
  message: string
  confirmText?: string
  cancelText?: string
  type?: 'warning' | 'danger' | 'info'
  onConfirm: () => void
  onCancel: () => void
}

interface PromptDialogState {
  isOpen: boolean
  title: string
  message?: string
  defaultValue?: string
  placeholder?: string
  confirmText?: string
  cancelText?: string
  onConfirm: (value: string) => void
  onCancel: () => void
}

interface ToastContextType {
  showToast: (type: ToastType, title: string, message?: string, duration?: number) => void
  removeToast: (id: string) => void
  showConfirm: (options: Omit<ConfirmDialogState, 'isOpen'>) => void
  showPrompt: (options: Omit<PromptDialogState, 'isOpen'>) => void
}

const ToastContext = createContext<ToastContextType | undefined>(undefined)

export const useToast = () => {
  const context = useContext(ToastContext)
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider')
  }
  return context
}

interface ToastProviderProps {
  children: React.ReactNode
}

export const ToastProvider: React.FC<ToastProviderProps> = ({ children }) => {
  const [toasts, setToasts] = useState<ToastProps[]>([])
  const [confirmDialog, setConfirmDialog] = useState<ConfirmDialogState>({
    isOpen: false,
    title: '',
    message: '',
    onConfirm: () => {},
    onCancel: () => {}
  })
  const [promptDialog, setPromptDialog] = useState<PromptDialogState>({
    isOpen: false,
    title: '',
    onConfirm: () => {},
    onCancel: () => {}
  })

  const showToast = useCallback((
    type: ToastType,
    title: string,
    message?: string,
    duration: number = 5000
  ) => {
    const id = Math.random().toString(36).substr(2, 9)
    const newToast: ToastProps = {
      id,
      type,
      title,
      message,
      duration,
      onClose: removeToast
    }

    setToasts(prev => [...prev, newToast])
  }, [])

  const removeToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id))
  }, [])

  const showConfirm = useCallback((options: Omit<ConfirmDialogState, 'isOpen'>) => {
    setConfirmDialog({
      ...options,
      isOpen: true
    })
  }, [])

  const showPrompt = useCallback((options: Omit<PromptDialogState, 'isOpen'>) => {
    setPromptDialog({
      ...options,
      isOpen: true
    })
  }, [])

  return (
    <ToastContext.Provider value={{ showToast, removeToast, showConfirm, showPrompt }}>
      {children}
      <ToastContainer toasts={toasts} onRemoveToast={removeToast} />
      <ConfirmDialog
        {...confirmDialog}
        onConfirm={() => {
          confirmDialog.onConfirm()
          setConfirmDialog(prev => ({ ...prev, isOpen: false }))
        }}
        onCancel={() => {
          confirmDialog.onCancel()
          setConfirmDialog(prev => ({ ...prev, isOpen: false }))
        }}
      />
      <PromptDialog
        {...promptDialog}
        onConfirm={(value) => {
          promptDialog.onConfirm(value)
          setPromptDialog(prev => ({ ...prev, isOpen: false }))
        }}
        onCancel={() => {
          promptDialog.onCancel()
          setPromptDialog(prev => ({ ...prev, isOpen: false }))
        }}
      />
    </ToastContext.Provider>
  )
}

// 全局函数，可以在任何地方使用
let globalShowToast: ((type: ToastType, title: string, message?: string, duration?: number) => void) | null = null
let globalShowConfirm: ((options: Omit<ConfirmDialogState, 'isOpen'>) => void) | null = null
let globalShowPrompt: ((options: Omit<PromptDialogState, 'isOpen'>) => void) | null = null

export const setGlobalToast = (
  showToast: (type: ToastType, title: string, message?: string, duration?: number) => void,
  showConfirm: (options: Omit<ConfirmDialogState, 'isOpen'>) => void,
  showPrompt: (options: Omit<PromptDialogState, 'isOpen'>) => void
) => {
  globalShowToast = showToast
  globalShowConfirm = showConfirm
  globalShowPrompt = showPrompt
}

export const showNotification = (type: ToastType, title: string, message?: string, duration?: number) => {
  if (globalShowToast) {
    globalShowToast(type, title, message, duration)
  } else {
    console.warn('Toast not initialized. Using fallback alert.')
    alert(title + (message ? '\n' + message : ''))
  }
}

export const showConfirmDialog = (options: Omit<ConfirmDialogState, 'isOpen'>): Promise<boolean> => {
  return new Promise((resolve) => {
    if (globalShowConfirm) {
      globalShowConfirm({
        ...options,
        onConfirm: () => {
          options.onConfirm()
          resolve(true)
        },
        onCancel: () => {
          options.onCancel()
          resolve(false)
        }
      })
    } else {
      console.warn('Confirm dialog not initialized. Using fallback confirm.')
      resolve(confirm(options.message))
    }
  })
}

export const showPromptDialog = (options: Omit<PromptDialogState, 'isOpen'>): Promise<string | null> => {
  return new Promise((resolve) => {
    if (globalShowPrompt) {
      globalShowPrompt({
        ...options,
        onConfirm: (value) => {
          options.onConfirm(value)
          resolve(value)
        },
        onCancel: () => {
          options.onCancel()
          resolve(null)
        }
      })
    } else {
      console.warn('Prompt dialog not initialized. Using fallback prompt.')
      resolve(prompt(options.title, options.defaultValue))
    }
  })
}
