"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.fileService = exports.FileService = void 0;
const storage_service_1 = require("./storage.service");
const file_dao_1 = require("../dao/file.dao");
const crypto = __importStar(require("crypto"));
const logger_1 = __importDefault(require("../utils/logger"));
// In-memory storage for upload progress (in production, use Redis)
const uploadProgressMap = new Map();
const uploadChunksMap = new Map();
class FileService {
    async uploadFile(userId, fileUpload, metadata) {
        try {
            const fileId = crypto.randomUUID();
            const fileBuffer = Buffer.isBuffer(fileUpload.file)
                ? fileUpload.file
                : Buffer.from(await fileUpload.file.arrayBuffer());
            // Upload file chunks to distributed storage
            const fileChunks = await storage_service_1.storageService.uploadFileChunked(fileId, fileBuffer, {
                originalName: fileUpload.filename,
                mimeType: fileUpload.mimeType,
                ...metadata
            });
            // Create file metadata
            const fileMetadata = {
                id: fileId,
                userId,
                filename: this.generateUniqueFilename(fileUpload.filename),
                originalName: fileUpload.filename,
                mimeType: fileUpload.mimeType,
                size: fileBuffer.length,
                checksum: fileChunks.checksum,
                folderId: fileUpload.folderId || metadata.folderId,
                isPublic: metadata.isPublic ?? false, // Use metadata.isPublic if provided, otherwise false
                uploadedAt: new Date(),
                modifiedAt: new Date(),
                tags: fileUpload.tags || [],
                storageNodes: this.extractStorageNodes(fileChunks)
            };
            // Save metadata to database
            await file_dao_1.fileDao.createFile(fileMetadata);
            // Store chunk information for retrieval
            await file_dao_1.fileDao.saveFileChunks(fileId, fileChunks);
            logger_1.default.info(`File uploaded successfully: ${fileId}`);
            return {
                id: fileId,
                filename: fileMetadata.filename,
                size: fileMetadata.size,
                mimeType: fileMetadata.mimeType,
                uploadedAt: fileMetadata.uploadedAt,
                downloadUrl: this.generateDownloadUrl(fileId)
            };
        }
        catch (error) {
            logger_1.default.error('File upload failed:', error);
            throw new Error('File upload failed');
        }
    }
    async uploadFiles(userId, files) {
        const uploadPromises = files.map(file => this.uploadFile(userId, file, {}));
        try {
            const results = await Promise.allSettled(uploadPromises);
            const successful = [];
            const failed = [];
            results.forEach((result, index) => {
                if (result.status === 'fulfilled') {
                    successful.push(result.value);
                }
                else {
                    failed.push(files[index].filename);
                    logger_1.default.error(`Failed to upload ${files[index].filename}:`, result.reason);
                }
            });
            if (failed.length > 0) {
                logger_1.default.warn(`${failed.length} files failed to upload: ${failed.join(', ')}`);
            }
            return successful;
        }
        catch (error) {
            logger_1.default.error('Batch file upload failed:', error);
            throw new Error('Batch file upload failed');
        }
    }
    async downloadFile(fileId, userId) {
        try {
            // Get file metadata
            const metadata = await file_dao_1.fileDao.getFileById(fileId);
            if (!metadata) {
                throw new Error('File not found');
            }
            // Check permissions
            if (metadata.userId !== userId && !metadata.isPublic) {
                throw new Error('Access denied');
            }
            // Get chunk information
            const fileChunks = await file_dao_1.fileDao.getFileChunks(fileId);
            if (!fileChunks) {
                throw new Error('File chunks not found');
            }
            // Download file from distributed storage
            const fileBuffer = await storage_service_1.storageService.downloadFileChunked(fileChunks);
            // Create readable stream
            const { Readable } = require('stream');
            const stream = Readable.from(fileBuffer);
            logger_1.default.info(`File downloaded: ${fileId}`);
            return {
                stream,
                metadata
            };
        }
        catch (error) {
            logger_1.default.error(`File download failed for ${fileId}:`, error);
            throw error;
        }
    }
    async getFilePreview(fileId, userId, options = {}) {
        try {
            // Get file metadata
            const metadata = await file_dao_1.fileDao.getFileById(fileId);
            if (!metadata) {
                throw new Error('File not found');
            }
            // Check permissions
            if (metadata.userId !== userId && !metadata.isPublic) {
                throw new Error('Access denied');
            }
            // Check if file type supports preview
            const supportedTypes = [
                'image/jpeg', 'image/png', 'image/gif', 'image/webp',
                'video/mp4', 'video/webm', 'video/ogg',
                'audio/mpeg', 'audio/wav', 'audio/ogg',
                'application/pdf',
                'text/plain', 'text/html', 'text/css', 'text/javascript',
                'application/json', 'application/xml'
            ];
            if (!supportedTypes.includes(metadata.mimeType)) {
                throw new Error('Preview not supported');
            }
            // Get chunk information
            const fileChunks = await file_dao_1.fileDao.getFileChunks(fileId);
            if (!fileChunks) {
                throw new Error('File chunks not found');
            }
            // Download file from distributed storage
            let fileBuffer = await storage_service_1.storageService.downloadFileChunked(fileChunks);
            // For images, optionally resize for preview
            if (metadata.mimeType.startsWith('image/') && (options.maxWidth || options.maxHeight)) {
                fileBuffer = await this.resizeImage(fileBuffer, metadata.mimeType, {
                    maxWidth: options.maxWidth || 1200,
                    maxHeight: options.maxHeight || 800,
                    quality: options.quality || 'medium'
                });
            }
            // Create readable stream
            const { Readable } = require('stream');
            const stream = Readable.from(fileBuffer);
            logger_1.default.info(`File preview generated: ${fileId}`);
            return {
                stream,
                metadata: {
                    ...metadata,
                    size: fileBuffer.length // Update size if resized
                }
            };
        }
        catch (error) {
            logger_1.default.error(`File preview failed for ${fileId}:`, error);
            throw error;
        }
    }
    async resizeImage(buffer, mimeType, options) {
        try {
            // For now, return original buffer
            // In a production environment, you would use a library like Sharp to resize images
            // const sharp = require('sharp');
            // return await sharp(buffer)
            //   .resize(options.maxWidth, options.maxHeight, { fit: 'inside', withoutEnlargement: true })
            //   .jpeg({ quality: options.quality === 'high' ? 90 : options.quality === 'low' ? 60 : 75 })
            //   .toBuffer();
            return buffer;
        }
        catch (error) {
            logger_1.default.warn('Image resize failed, returning original:', error);
            return buffer;
        }
    }
    async listFiles(userId, folderId, pagination = { page: 1, limit: 50 }) {
        try {
            const { files, totalCount } = await file_dao_1.fileDao.getFilesByUser(userId, folderId, pagination);
            const folders = folderId
                ? await file_dao_1.fileDao.getFoldersByParent(userId, folderId)
                : await file_dao_1.fileDao.getRootFolders(userId);
            return {
                files,
                folders,
                totalCount,
                hasMore: (pagination.page * pagination.limit) < totalCount
            };
        }
        catch (error) {
            logger_1.default.error('Failed to list files:', error);
            throw new Error('Failed to list files');
        }
    }
    async searchFiles(userId, query) {
        try {
            const { files, totalCount } = await file_dao_1.fileDao.searchFiles(userId, query);
            return {
                files,
                folders: [], // Search typically doesn't return folders
                totalCount,
                hasMore: false // Simplified for now
            };
        }
        catch (error) {
            logger_1.default.error('File search failed:', error);
            throw new Error('File search failed');
        }
    }
    async createFolder(userId, folderData) {
        try {
            // Check if trying to create image bed folder in root directory
            if (!folderData.parentId) {
                const restrictedNames = ['Image Bed', '图床', '图片床'];
                if (restrictedNames.includes(folderData.name)) {
                    throw new Error('Cannot create folder with reserved image bed name. The Image Bed folder is automatically created by the system.');
                }
            }
            const folderId = crypto.randomUUID();
            const path = await this.buildFolderPath(folderData.parentId, folderData.name);
            const folder = {
                id: folderId,
                userId,
                name: folderData.name,
                parentId: folderData.parentId,
                path,
                createdAt: new Date(),
                modifiedAt: new Date()
            };
            await file_dao_1.fileDao.createFolder(folder);
            logger_1.default.info(`Folder created: ${folderId}`);
            return folder;
        }
        catch (error) {
            logger_1.default.error('Folder creation failed:', error);
            throw error;
        }
    }
    async moveFile(fileId, targetFolderId, userId) {
        try {
            const file = await file_dao_1.fileDao.getFileById(fileId);
            if (!file || file.userId !== userId) {
                throw new Error('File not found or access denied');
            }
            if (targetFolderId) {
                const targetFolder = await file_dao_1.fileDao.getFolderById(targetFolderId);
                if (!targetFolder || targetFolder.userId !== userId) {
                    throw new Error('Target folder not found or access denied');
                }
            }
            await file_dao_1.fileDao.updateFile(fileId, {
                folderId: targetFolderId,
                modifiedAt: new Date()
            });
            logger_1.default.info(`File moved: ${fileId} to folder ${targetFolderId}`);
        }
        catch (error) {
            logger_1.default.error(`Failed to move file ${fileId}:`, error);
            throw error;
        }
    }
    async deleteFile(fileId, userId) {
        try {
            const file = await file_dao_1.fileDao.getFileById(fileId);
            if (!file || file.userId !== userId) {
                throw new Error('File not found or access denied');
            }
            // Get chunk information
            const fileChunks = await file_dao_1.fileDao.getFileChunks(fileId);
            // Delete from distributed storage
            if (fileChunks) {
                await storage_service_1.storageService.deleteFileChunked(fileChunks);
            }
            // Delete metadata from database
            await file_dao_1.fileDao.deleteFile(fileId);
            await file_dao_1.fileDao.deleteFileChunks(fileId);
            logger_1.default.info(`File deleted: ${fileId}`);
        }
        catch (error) {
            logger_1.default.error(`Failed to delete file ${fileId}:`, error);
            throw error;
        }
    }
    async shareFile(fileId, userId, shareOptions) {
        try {
            const file = await file_dao_1.fileDao.getFileById(fileId);
            if (!file || file.userId !== userId) {
                throw new Error('File not found or access denied');
            }
            const shareId = crypto.randomUUID();
            const token = crypto.randomBytes(32).toString('hex');
            const shareLink = {
                id: shareId,
                fileId,
                userId,
                token,
                permissions: shareOptions.permissions,
                expiresAt: shareOptions.expiresAt,
                password: shareOptions.password,
                downloadCount: 0,
                maxDownloads: shareOptions.maxDownloads,
                createdAt: new Date()
            };
            await file_dao_1.fileDao.createShareLink(shareLink);
            logger_1.default.info(`Share link created: ${shareId} for file ${fileId}`);
            return shareLink;
        }
        catch (error) {
            logger_1.default.error(`Failed to create share link for file ${fileId}:`, error);
            throw error;
        }
    }
    generateUniqueFilename(originalName) {
        const timestamp = Date.now();
        const random = crypto.randomBytes(4).toString('hex');
        const ext = originalName.split('.').pop();
        const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '');
        return `${nameWithoutExt}_${timestamp}_${random}.${ext}`;
    }
    extractStorageNodes(fileChunks) {
        const nodeIds = new Set();
        fileChunks.chunks.forEach(chunk => {
            chunk.storageNodes.forEach(nodeId => nodeIds.add(nodeId));
        });
        return Array.from(nodeIds).map(nodeId => ({
            id: nodeId,
            url: `storage-node-${nodeId}`, // Simplified
            region: 'us-east-1', // Default region
            isHealthy: true
        }));
    }
    generateDownloadUrl(fileId) {
        // In a real implementation, this would generate a signed URL
        return `/api/files/${fileId}/download`;
    }
    async buildFolderPath(parentId, folderName) {
        if (!parentId) {
            return `/${folderName}`;
        }
        const parent = await file_dao_1.fileDao.getFolderById(parentId);
        if (!parent) {
            throw new Error('Parent folder not found');
        }
        return `${parent.path}/${folderName}`;
    }
    async getStorageStats(userId) {
        try {
            const stats = await file_dao_1.fileDao.getUserStorageStats(userId);
            const storageQuota = 10 * 1024 * 1024 * 1024; // 10GB default quota
            return {
                totalFiles: stats.fileCount,
                totalSize: stats.totalSize,
                storageQuota,
                usedPercentage: (stats.totalSize / storageQuota) * 100
            };
        }
        catch (error) {
            logger_1.default.error(`Failed to get storage stats for user ${userId}:`, error);
            throw new Error('Failed to get storage stats');
        }
    }
    // Initialize chunked upload
    async initChunkedUpload(userId, uploadInit) {
        try {
            const uploadId = crypto.randomUUID();
            const progress = {
                uploadId,
                filename: uploadInit.filename,
                totalChunks: uploadInit.totalChunks,
                uploadedChunks: 0,
                progress: 0,
                status: 'pending',
                createdAt: new Date(),
                updatedAt: new Date()
            };
            uploadProgressMap.set(uploadId, progress);
            uploadChunksMap.set(uploadId, new Map());
            logger_1.default.info(`Chunked upload initialized: ${uploadId} for user ${userId}`);
            return uploadId;
        }
        catch (error) {
            logger_1.default.error('Failed to initialize chunked upload:', error);
            throw new Error('Failed to initialize chunked upload');
        }
    }
    // Upload chunk
    async uploadChunk(uploadId, chunk) {
        try {
            const progress = uploadProgressMap.get(uploadId);
            if (!progress) {
                throw new Error('Upload session not found');
            }
            const chunks = uploadChunksMap.get(uploadId);
            if (!chunks) {
                throw new Error('Upload chunks not found');
            }
            // Store chunk
            chunks.set(chunk.chunkIndex, chunk.chunkData);
            // Update progress
            progress.uploadedChunks = chunks.size;
            progress.progress = (progress.uploadedChunks / progress.totalChunks) * 100;
            progress.status = 'uploading';
            progress.updatedAt = new Date();
            uploadProgressMap.set(uploadId, progress);
            logger_1.default.info(`Chunk ${chunk.chunkIndex} uploaded for upload ${uploadId}`);
            return {
                chunkIndex: chunk.chunkIndex,
                uploaded: progress.uploadedChunks,
                total: progress.totalChunks,
                progress: progress.progress
            };
        }
        catch (error) {
            logger_1.default.error(`Failed to upload chunk for ${uploadId}:`, error);
            throw error;
        }
    }
    // Complete chunked upload
    async completeChunkedUpload(uploadId, userId) {
        try {
            const progress = uploadProgressMap.get(uploadId);
            if (!progress) {
                throw new Error('Upload session not found');
            }
            const chunks = uploadChunksMap.get(uploadId);
            if (!chunks || chunks.size !== progress.totalChunks) {
                throw new Error('Not all chunks uploaded');
            }
            // Combine chunks in order
            const sortedChunks = Array.from(chunks.entries())
                .sort(([a], [b]) => a - b)
                .map(([, buffer]) => buffer);
            const fileBuffer = Buffer.concat(sortedChunks);
            // Create file upload object
            const fileUpload = {
                file: fileBuffer,
                filename: progress.filename,
                mimeType: 'application/octet-stream', // Will be determined by file extension
                tags: []
            };
            // Upload the complete file
            const fileInfo = await this.uploadFile(userId, fileUpload, {});
            // Update progress
            progress.status = 'completed';
            progress.progress = 100;
            progress.updatedAt = new Date();
            uploadProgressMap.set(uploadId, progress);
            // Clean up
            setTimeout(() => {
                uploadProgressMap.delete(uploadId);
                uploadChunksMap.delete(uploadId);
            }, 60000); // Clean up after 1 minute
            logger_1.default.info(`Chunked upload completed: ${uploadId}`);
            return fileInfo;
        }
        catch (error) {
            // Mark as failed
            const progress = uploadProgressMap.get(uploadId);
            if (progress) {
                progress.status = 'failed';
                progress.updatedAt = new Date();
                uploadProgressMap.set(uploadId, progress);
            }
            logger_1.default.error(`Failed to complete chunked upload ${uploadId}:`, error);
            throw error;
        }
    }
    // Get upload progress
    async getUploadProgress(uploadId, userId) {
        const progress = uploadProgressMap.get(uploadId);
        if (!progress) {
            throw new Error('Upload session not found');
        }
        return progress;
    }
    // Clean up expired uploads (should be called periodically)
    async cleanupExpiredUploads() {
        const now = new Date();
        const expiredUploads = [];
        for (const [uploadId, progress] of Array.from(uploadProgressMap.entries())) {
            const ageInMinutes = (now.getTime() - progress.createdAt.getTime()) / (1000 * 60);
            // Clean up uploads older than 24 hours
            if (ageInMinutes > 24 * 60) {
                expiredUploads.push(uploadId);
            }
        }
        for (const uploadId of expiredUploads) {
            uploadProgressMap.delete(uploadId);
            uploadChunksMap.delete(uploadId);
            logger_1.default.info(`Cleaned up expired upload: ${uploadId}`);
        }
    }
    // Get file info
    async getFileInfo(fileId, userId) {
        try {
            const file = await file_dao_1.fileDao.getFileById(fileId);
            if (!file) {
                throw new Error('File not found');
            }
            // Check permissions
            if (file.userId !== userId && !file.isPublic) {
                throw new Error('Access denied');
            }
            return file;
        }
        catch (error) {
            logger_1.default.error(`Failed to get file info for ${fileId}:`, error);
            throw error;
        }
    }
    // Rename folder
    async renameFolder(folderId, newName, userId) {
        try {
            const folder = await file_dao_1.fileDao.getFolderById(folderId);
            if (!folder || folder.userId !== userId) {
                throw new Error('Folder not found or access denied');
            }
            // Check if name already exists in the same parent
            const isValid = await file_dao_1.fileDao.validateFolderName(userId, folder.parentId, newName);
            if (!isValid) {
                throw new Error('Folder name already exists in this location');
            }
            await file_dao_1.fileDao.renameFolder(folderId, newName);
            logger_1.default.info(`Folder renamed: ${folderId} to ${newName}`);
        }
        catch (error) {
            logger_1.default.error(`Failed to rename folder ${folderId}:`, error);
            throw error;
        }
    }
    // Get folder info
    // Get folder by ID (without user validation - for internal use)
    async getFolderById(folderId) {
        try {
            return await file_dao_1.fileDao.getFolderById(folderId);
        }
        catch (error) {
            logger_1.default.error(`Failed to get folder ${folderId}:`, error);
            throw error;
        }
    }
    async getFolderInfo(folderId, userId) {
        try {
            const folder = await file_dao_1.fileDao.getFolderById(folderId);
            if (!folder || folder.userId !== userId) {
                throw new Error('Folder not found or access denied');
            }
            return folder;
        }
        catch (error) {
            logger_1.default.error(`Failed to get folder info ${folderId}:`, error);
            throw error;
        }
    }
    // Move folder
    async moveFolder(folderId, targetFolderId, userId) {
        try {
            const folder = await file_dao_1.fileDao.getFolderById(folderId);
            if (!folder || folder.userId !== userId) {
                throw new Error('Folder not found or access denied');
            }
            if (targetFolderId) {
                const targetFolder = await file_dao_1.fileDao.getFolderById(targetFolderId);
                if (!targetFolder || targetFolder.userId !== userId) {
                    throw new Error('Target folder not found or access denied');
                }
                // Check for circular reference
                if (await this.isCircularReference(folderId, targetFolderId)) {
                    throw new Error('Cannot move folder into itself or its subfolder');
                }
            }
            await file_dao_1.fileDao.moveFolder(folderId, targetFolderId);
            logger_1.default.info(`Folder moved: ${folderId} to ${targetFolderId}`);
        }
        catch (error) {
            logger_1.default.error(`Failed to move folder ${folderId}:`, error);
            throw error;
        }
    }
    // Delete folder to trash (soft delete)
    async deleteFolderToTrash(folderId, userId) {
        try {
            const folder = await file_dao_1.fileDao.getFolderById(folderId);
            if (!folder || folder.userId !== userId) {
                throw new Error('Folder not found or access denied');
            }
            // Mark folder as deleted (move to trash)
            await file_dao_1.fileDao.updateFolder(folderId, {
                isDeleted: true,
                deletedAt: new Date(),
                modifiedAt: new Date()
            });
            // Also mark all files and subfolders in this folder as deleted
            await this.markFolderContentsAsDeleted(folderId, userId);
            logger_1.default.info(`Folder moved to trash: ${folderId}`);
        }
        catch (error) {
            logger_1.default.error(`Failed to delete folder to trash ${folderId}:`, error);
            throw error;
        }
    }
    // Delete folder permanently
    async deleteFolderPermanently(folderId, userId) {
        try {
            const folder = await file_dao_1.fileDao.getFolderById(folderId);
            if (!folder || folder.userId !== userId) {
                throw new Error('Folder not found or access denied');
            }
            // Delete all files in the folder permanently
            await this.deleteFolderContentsPermanently(folderId, userId);
            // Delete the folder itself
            await file_dao_1.fileDao.deleteFolder(folderId);
            logger_1.default.info(`Folder permanently deleted: ${folderId}`);
        }
        catch (error) {
            logger_1.default.error(`Failed to permanently delete folder ${folderId}:`, error);
            throw error;
        }
    }
    // Rename file
    async renameFile(fileId, newName, userId) {
        try {
            const file = await file_dao_1.fileDao.getFileById(fileId);
            if (!file || file.userId !== userId) {
                throw new Error('File not found or access denied');
            }
            await file_dao_1.fileDao.updateFile(fileId, {
                originalName: newName,
                modifiedAt: new Date()
            });
            logger_1.default.info(`File renamed: ${fileId} to ${newName}`);
        }
        catch (error) {
            logger_1.default.error(`Failed to rename file ${fileId}:`, error);
            throw error;
        }
    }
    // Duplicate file
    async duplicateFile(fileId, userId, newName) {
        try {
            const originalFile = await file_dao_1.fileDao.getFileById(fileId);
            if (!originalFile || originalFile.userId !== userId) {
                throw new Error('File not found or access denied');
            }
            // Generate new name if not provided
            const duplicateName = newName || this.generateDuplicateName(originalFile.originalName);
            // Create new file metadata
            const newFileId = crypto.randomUUID();
            const newFilename = `${newFileId}_${duplicateName}`;
            const duplicateFile = {
                ...originalFile,
                id: newFileId,
                filename: newFilename,
                originalName: duplicateName,
                uploadedAt: new Date(),
                modifiedAt: new Date(),
                isDeleted: false,
                deletedAt: undefined
            };
            // Get original file chunks and copy them
            const originalChunks = await file_dao_1.fileDao.getFileChunks(fileId);
            if (originalChunks) {
                // Download original file data
                const fileBuffer = await storage_service_1.storageService.downloadFileChunked(originalChunks);
                // Upload as new file
                const newChunks = await storage_service_1.storageService.uploadFileChunked(newFileId, fileBuffer, duplicateFile);
                // Save chunk information
                await file_dao_1.fileDao.saveFileChunks(newFileId, newChunks);
            }
            // Save duplicate file metadata
            await file_dao_1.fileDao.createFile(duplicateFile);
            logger_1.default.info(`File duplicated: ${fileId} -> ${newFileId}`);
            return duplicateFile;
        }
        catch (error) {
            logger_1.default.error(`Failed to duplicate file ${fileId}:`, error);
            throw error;
        }
    }
    // Copy file to folder
    async copyFile(fileId, userId, targetFolderId) {
        try {
            const originalFile = await file_dao_1.fileDao.getFileById(fileId);
            if (!originalFile || originalFile.userId !== userId) {
                throw new Error('File not found or access denied');
            }
            // Validate target folder if provided
            if (targetFolderId) {
                const targetFolder = await file_dao_1.fileDao.getFolderById(targetFolderId);
                if (!targetFolder || targetFolder.userId !== userId) {
                    throw new Error('Target folder not found or access denied');
                }
            }
            // Generate copy name
            const copyName = this.generateCopyName(originalFile.originalName);
            // Create new file metadata
            const newFileId = crypto.randomUUID();
            const newFilename = `${newFileId}_${copyName}`;
            const copiedFile = {
                ...originalFile,
                id: newFileId,
                filename: newFilename,
                originalName: copyName,
                folderId: targetFolderId,
                uploadedAt: new Date(),
                modifiedAt: new Date(),
                isDeleted: false,
                deletedAt: undefined
            };
            // Get original file chunks and copy them
            const originalChunks = await file_dao_1.fileDao.getFileChunks(fileId);
            if (originalChunks) {
                // Download original file data
                const fileBuffer = await storage_service_1.storageService.downloadFileChunked(originalChunks);
                // Upload as new file
                const newChunks = await storage_service_1.storageService.uploadFileChunked(newFileId, fileBuffer, copiedFile);
                // Save chunk information
                await file_dao_1.fileDao.saveFileChunks(newFileId, newChunks);
            }
            // Save copied file metadata
            await file_dao_1.fileDao.createFile(copiedFile);
            logger_1.default.info(`File copied: ${fileId} -> ${newFileId} to folder ${targetFolderId || 'root'}`);
            return copiedFile;
        }
        catch (error) {
            logger_1.default.error(`Failed to copy file ${fileId}:`, error);
            throw error;
        }
    }
    // Helper method to generate duplicate name
    generateDuplicateName(originalName) {
        const lastDotIndex = originalName.lastIndexOf('.');
        if (lastDotIndex === -1) {
            return `${originalName} (copy)`;
        }
        const nameWithoutExt = originalName.substring(0, lastDotIndex);
        const extension = originalName.substring(lastDotIndex);
        return `${nameWithoutExt} (copy)${extension}`;
    }
    // Helper method to generate copy name
    generateCopyName(originalName) {
        const lastDotIndex = originalName.lastIndexOf('.');
        if (lastDotIndex === -1) {
            return `${originalName} - Copy`;
        }
        const nameWithoutExt = originalName.substring(0, lastDotIndex);
        const extension = originalName.substring(lastDotIndex);
        return `${nameWithoutExt} - Copy${extension}`;
    }
    // Move file to trash (soft delete)
    async moveFileToTrash(fileId, userId) {
        try {
            const file = await file_dao_1.fileDao.getFileById(fileId);
            if (!file || file.userId !== userId) {
                throw new Error('File not found or access denied');
            }
            await file_dao_1.fileDao.updateFile(fileId, {
                isDeleted: true,
                deletedAt: new Date(),
                modifiedAt: new Date()
            });
            logger_1.default.info(`File moved to trash: ${fileId}`);
        }
        catch (error) {
            logger_1.default.error(`Failed to move file to trash ${fileId}:`, error);
            throw error;
        }
    }
    // Get trash contents
    async getTrashContents(userId, pagination) {
        try {
            const { files, totalCount } = await file_dao_1.fileDao.getDeletedFiles(userId, pagination);
            const folders = await file_dao_1.fileDao.getDeletedFolders(userId);
            return {
                files,
                folders,
                totalCount,
                hasMore: (pagination.page * pagination.limit) < totalCount
            };
        }
        catch (error) {
            logger_1.default.error(`Failed to get trash contents for user ${userId}:`, error);
            throw new Error('Failed to get trash contents');
        }
    }
    // Restore file or folder from trash
    async restoreFromTrash(itemId, userId) {
        try {
            // First try to find as a file
            const file = await file_dao_1.fileDao.getFileById(itemId);
            if (file && file.userId === userId) {
                if (!file.isDeleted) {
                    throw new Error('File is not in trash');
                }
                await file_dao_1.fileDao.updateFile(itemId, {
                    isDeleted: false,
                    deletedAt: undefined,
                    modifiedAt: new Date()
                });
                logger_1.default.info(`File restored from trash: ${itemId}`);
                return;
            }
            // If not found as file, try to find as folder
            const folder = await file_dao_1.fileDao.getFolderById(itemId);
            if (folder && folder.userId === userId) {
                if (!folder.isDeleted) {
                    throw new Error('Folder is not in trash');
                }
                await file_dao_1.fileDao.updateFolder(itemId, {
                    isDeleted: false,
                    deletedAt: undefined,
                    modifiedAt: new Date()
                });
                // Also restore all contents of the folder
                await this.restoreFolderContents(itemId, userId);
                logger_1.default.info(`Folder restored from trash: ${itemId}`);
                return;
            }
            throw new Error('Item not found or access denied');
        }
        catch (error) {
            logger_1.default.error(`Failed to restore item from trash ${itemId}:`, error);
            throw error;
        }
    }
    // Empty trash
    async emptyTrash(userId) {
        try {
            const deletedFiles = await file_dao_1.fileDao.getDeletedFiles(userId, { page: 1, limit: 1000 });
            const deletedFolders = await file_dao_1.fileDao.getDeletedFolders(userId);
            let deletedCount = 0;
            // Permanently delete all files in trash
            for (const file of deletedFiles.files) {
                await this.deleteFile(file.id, userId);
                deletedCount++;
            }
            // Permanently delete all folders in trash
            for (const folder of deletedFolders) {
                await file_dao_1.fileDao.deleteFolder(folder.id);
                deletedCount++;
            }
            logger_1.default.info(`Emptied trash for user ${userId}: ${deletedCount} items deleted`);
            return deletedCount;
        }
        catch (error) {
            logger_1.default.error(`Failed to empty trash for user ${userId}:`, error);
            throw new Error('Failed to empty trash');
        }
    }
    // Helper method to check circular reference
    async isCircularReference(folderId, targetFolderId) {
        if (folderId === targetFolderId) {
            return true;
        }
        const targetFolder = await file_dao_1.fileDao.getFolderById(targetFolderId);
        if (!targetFolder || !targetFolder.parentId) {
            return false;
        }
        return await this.isCircularReference(folderId, targetFolder.parentId);
    }
    // Helper method to restore folder contents from trash
    async restoreFolderContents(folderId, userId) {
        // Get all files in the folder (including deleted ones)
        const files = await file_dao_1.fileDao.getFilesByUserId(userId, folderId);
        for (const file of files) {
            if (file.isDeleted) {
                await file_dao_1.fileDao.updateFile(file.id, {
                    isDeleted: false,
                    deletedAt: undefined,
                    modifiedAt: new Date()
                });
            }
        }
        // Get all subfolders and recursively restore them
        const subfolders = await file_dao_1.fileDao.getFoldersByUserId(userId, folderId);
        for (const subfolder of subfolders) {
            if (subfolder.isDeleted) {
                await file_dao_1.fileDao.updateFolder(subfolder.id, {
                    isDeleted: false,
                    deletedAt: undefined,
                    modifiedAt: new Date()
                });
                await this.restoreFolderContents(subfolder.id, userId);
            }
        }
    }
    // Helper method to mark folder contents as deleted
    async markFolderContentsAsDeleted(folderId, userId) {
        // Get all files in the folder
        const files = await file_dao_1.fileDao.getFilesByUserId(userId, folderId);
        for (const file of files) {
            await file_dao_1.fileDao.updateFile(file.id, {
                isDeleted: true,
                deletedAt: new Date(),
                modifiedAt: new Date()
            });
        }
        // Get all subfolders and recursively mark them as deleted
        const subfolders = await file_dao_1.fileDao.getFoldersByUserId(userId, folderId);
        for (const subfolder of subfolders) {
            await file_dao_1.fileDao.updateFolder(subfolder.id, {
                isDeleted: true,
                deletedAt: new Date(),
                modifiedAt: new Date()
            });
            await this.markFolderContentsAsDeleted(subfolder.id, userId);
        }
    }
    // Helper method to permanently delete folder contents
    async deleteFolderContentsPermanently(folderId, userId) {
        // Get all files in the folder and delete them permanently
        const files = await file_dao_1.fileDao.getFilesByUserId(userId, folderId);
        for (const file of files) {
            await this.deleteFile(file.id, userId);
        }
        // Get all subfolders and recursively delete them
        const subfolders = await file_dao_1.fileDao.getFoldersByUserId(userId, folderId);
        for (const subfolder of subfolders) {
            await this.deleteFolderContentsPermanently(subfolder.id, userId);
            await file_dao_1.fileDao.deleteFolder(subfolder.id);
        }
    }
    // Advanced sharing methods
    async getFileShares(fileId, userId) {
        try {
            const file = await file_dao_1.fileDao.getFileById(fileId);
            if (!file || file.userId !== userId) {
                throw new Error('File not found or access denied');
            }
            const shares = await file_dao_1.fileDao.getShareLinksByFileId(fileId);
            logger_1.default.info(`Retrieved ${shares.length} share links for file ${fileId}`);
            return shares;
        }
        catch (error) {
            logger_1.default.error(`Failed to get shares for file ${fileId}:`, error);
            throw error;
        }
    }
    async updateShareLink(shareId, userId, updates) {
        try {
            const shareLink = await file_dao_1.fileDao.getShareLinkById(shareId);
            if (!shareLink || shareLink.userId !== userId) {
                throw new Error('Share link not found or access denied');
            }
            await file_dao_1.fileDao.updateShareLink(shareId, updates);
            logger_1.default.info(`Share link updated: ${shareId}`);
        }
        catch (error) {
            logger_1.default.error(`Failed to update share link ${shareId}:`, error);
            throw error;
        }
    }
    async revokeShareLink(shareId, userId) {
        try {
            const shareLink = await file_dao_1.fileDao.getShareLinkById(shareId);
            if (!shareLink || shareLink.userId !== userId) {
                throw new Error('Share link not found or access denied');
            }
            await file_dao_1.fileDao.deleteShareLink(shareId);
            logger_1.default.info(`Share link revoked: ${shareId}`);
        }
        catch (error) {
            logger_1.default.error(`Failed to revoke share link ${shareId}:`, error);
            throw error;
        }
    }
    async getShareStats(userId) {
        try {
            const stats = await file_dao_1.fileDao.getUserShareStats(userId);
            logger_1.default.info(`Retrieved share stats for user ${userId}`);
            return stats;
        }
        catch (error) {
            logger_1.default.error(`Failed to get share stats for user ${userId}:`, error);
            throw new Error('Failed to get share statistics');
        }
    }
    async getUserShares(userId) {
        try {
            const shares = await file_dao_1.fileDao.getShareLinksByUserId(userId);
            const sharesWithFileInfo = await Promise.all(shares.map(async (share) => {
                const file = await file_dao_1.fileDao.getFileById(share.fileId);
                return {
                    id: share.id,
                    fileId: share.fileId,
                    fileName: file?.originalName || 'Unknown File',
                    fileSize: file?.size || 0,
                    fileMimeType: file?.mimeType || 'application/octet-stream',
                    token: share.token,
                    permissions: share.permissions,
                    downloadCount: share.downloadCount,
                    maxDownloads: share.maxDownloads,
                    createdAt: share.createdAt,
                    expiresAt: share.expiresAt,
                    password: share.password
                };
            }));
            logger_1.default.info(`Retrieved ${sharesWithFileInfo.length} shares for user ${userId}`);
            return sharesWithFileInfo;
        }
        catch (error) {
            logger_1.default.error(`Failed to get user shares for user ${userId}:`, error);
            throw new Error('Failed to get user shares');
        }
    }
    async getRecentShares(userId, limit = 10) {
        try {
            const shares = await file_dao_1.fileDao.getShareLinksByUserId(userId);
            const recentShares = await Promise.all(shares
                .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
                .slice(0, limit)
                .map(async (share) => {
                const file = await file_dao_1.fileDao.getFileById(share.fileId);
                const isActive = !share.expiresAt || new Date(share.expiresAt) > new Date();
                return {
                    id: share.id,
                    fileName: file?.originalName || 'Unknown File',
                    createdAt: share.createdAt,
                    downloadCount: share.downloadCount,
                    isActive
                };
            }));
            logger_1.default.info(`Retrieved ${recentShares.length} recent shares for user ${userId}`);
            return recentShares;
        }
        catch (error) {
            logger_1.default.error(`Failed to get recent shares for user ${userId}:`, error);
            throw new Error('Failed to get recent shares');
        }
    }
    async getDownloadsByDay(userId, days = 30) {
        try {
            // This would ideally be implemented with proper analytics tracking
            // For now, we'll create a simplified version
            const shares = await file_dao_1.fileDao.getShareLinksByUserId(userId);
            const downloadsByDay = {};
            // Initialize all days with 0 downloads
            for (let i = 0; i < days; i++) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                const dateStr = date.toISOString().split('T')[0];
                downloadsByDay[dateStr] = 0;
            }
            // Simulate download distribution (in a real implementation, this would come from analytics)
            shares.forEach(share => {
                if (share.downloadCount > 0) {
                    // Distribute downloads across recent days (simplified simulation)
                    const daysToDistribute = Math.min(7, days);
                    const downloadsPerDay = Math.floor(share.downloadCount / daysToDistribute);
                    for (let i = 0; i < daysToDistribute; i++) {
                        const date = new Date();
                        date.setDate(date.getDate() - i);
                        const dateStr = date.toISOString().split('T')[0];
                        downloadsByDay[dateStr] += downloadsPerDay;
                    }
                }
            });
            const result = Object.entries(downloadsByDay)
                .map(([date, downloads]) => ({ date, downloads }))
                .sort((a, b) => a.date.localeCompare(b.date));
            logger_1.default.info(`Retrieved download statistics for ${days} days for user ${userId}`);
            return result;
        }
        catch (error) {
            logger_1.default.error(`Failed to get downloads by day for user ${userId}:`, error);
            throw new Error('Failed to get download statistics');
        }
    }
    async getPublicShareInfo(token, password) {
        try {
            const validation = await file_dao_1.fileDao.validateShareLink(token, password);
            if (!validation.valid || !validation.shareLink) {
                throw new Error(validation.reason || 'Invalid share link');
            }
            const file = await file_dao_1.fileDao.getFileById(validation.shareLink.fileId);
            if (!file) {
                throw new Error('File not found');
            }
            return {
                fileInfo: {
                    id: file.id,
                    filename: file.originalName,
                    size: file.size,
                    mimeType: file.mimeType,
                    uploadedAt: file.uploadedAt
                },
                shareInfo: {
                    downloadCount: validation.shareLink.downloadCount,
                    maxDownloads: validation.shareLink.maxDownloads,
                    expiresAt: validation.shareLink.expiresAt,
                    requiresPassword: !!validation.shareLink.password
                }
            };
        }
        catch (error) {
            logger_1.default.error(`Failed to get public share info for token ${token}:`, error);
            throw error;
        }
    }
    async downloadViaPublicShare(token, password) {
        try {
            const validation = await file_dao_1.fileDao.validateShareLink(token, password);
            if (!validation.valid || !validation.shareLink) {
                throw new Error(validation.reason || 'Invalid share link');
            }
            const file = await file_dao_1.fileDao.getFileById(validation.shareLink.fileId);
            if (!file) {
                throw new Error('File not found');
            }
            // Record the download
            await file_dao_1.fileDao.recordDownload(validation.shareLink.id);
            // Get chunk information
            const fileChunks = await file_dao_1.fileDao.getFileChunks(validation.shareLink.fileId);
            if (!fileChunks) {
                throw new Error('File chunks not found');
            }
            // Download file from distributed storage
            const fileBuffer = await storage_service_1.storageService.downloadFileChunked(fileChunks);
            // Create readable stream
            const { Readable } = require('stream');
            const stream = Readable.from(fileBuffer);
            logger_1.default.info(`File downloaded via public share: ${validation.shareLink.fileId}`);
            return {
                stream,
                metadata: file
            };
        }
        catch (error) {
            logger_1.default.error(`Failed to download via public share for token ${token}:`, error);
            throw error;
        }
    }
    async previewViaPublicShare(token, password) {
        try {
            const validation = await file_dao_1.fileDao.validateShareLink(token, password);
            if (!validation.valid || !validation.shareLink) {
                throw new Error(validation.reason || 'Invalid share link');
            }
            const file = await file_dao_1.fileDao.getFileById(validation.shareLink.fileId);
            if (!file) {
                throw new Error('File not found');
            }
            // Get chunk information
            const fileChunks = await file_dao_1.fileDao.getFileChunks(validation.shareLink.fileId);
            if (!fileChunks) {
                throw new Error('File chunks not found');
            }
            // Download file from distributed storage
            const fileBuffer = await storage_service_1.storageService.downloadFileChunked(fileChunks);
            // Create readable stream
            const { Readable } = require('stream');
            const stream = Readable.from(fileBuffer);
            logger_1.default.info(`File previewed via public share: ${validation.shareLink.fileId}`);
            return {
                stream,
                metadata: file
            };
        }
        catch (error) {
            logger_1.default.error(`Failed to preview via public share for token ${token}:`, error);
            throw error;
        }
    }
    // Cleanup expired shares (should be called periodically)
    async cleanupExpiredShares() {
        try {
            const deletedCount = await file_dao_1.fileDao.cleanupExpiredShares();
            logger_1.default.info(`Cleaned up ${deletedCount} expired share links`);
            return deletedCount;
        }
        catch (error) {
            logger_1.default.error('Failed to cleanup expired shares:', error);
            throw new Error('Failed to cleanup expired shares');
        }
    }
    // Debug methods
    async getAllFilesForUser(userId) {
        const { files } = await file_dao_1.fileDao.getFilesByUser(userId, undefined, { page: 1, limit: 1000 });
        return files;
    }
    async getAllFoldersForUser(userId) {
        return await file_dao_1.fileDao.getRootFolders(userId);
    }
}
exports.FileService = FileService;
exports.fileService = new FileService();
exports.default = exports.fileService;
//# sourceMappingURL=file.service.js.map