import React from 'react'
import { ChevronRightIcon } from '@heroicons/react/24/outline'
import { cn } from '../../utils/cn'
import { FolderIcon } from 'lucide-react'

export interface FolderPath {
  id: string
  name: string
}

interface FolderBreadcrumbProps {
  path: FolderPath[]
  onNavigate: (index: number) => void
}

const FolderBreadcrumb: React.FC<FolderBreadcrumbProps> = ({
  path,
  onNavigate
}) => {
  return (
    <nav className="flex items-center space-x-1 text-sm">
      {path.map((folder, index) => (
        <React.Fragment key={folder.id}>
          {index > 0 && (
            <ChevronRightIcon className="w-4 h-4 text-comment" />
          )}
          
          <button
            onClick={() => onNavigate(index)}
            className={cn(
              'flex items-center px-2 py-1 rounded transition-colors',
              'hover-bg',
              index === path.length - 1
                ? 'text-primary font-medium'
                : 'text-comment hover:text-primary'
            )}
          >
            {index === 0 && (
              <FolderIcon className="w-4 h-4 mr-1" />
            )}
            {folder.name}
          </button>
        </React.Fragment>
      ))}
    </nav>
  )
}

export default FolderBreadcrumb