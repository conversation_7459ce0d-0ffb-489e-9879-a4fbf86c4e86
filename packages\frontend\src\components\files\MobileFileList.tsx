import React, { useState, useRef } from 'react'
import { FileItem } from '../../pages/Files'
import { useGestures } from '../../hooks/useGestures'
import { useMobile } from '../../hooks/useMobile'
import { cn } from '../../utils/cn'
import {
  DocumentIcon,
  FolderIcon,
  PhotoIcon,
  VideoCameraIcon,
  MusicalNoteIcon,
  ArchiveBoxIcon,
  EllipsisVerticalIcon,
  CheckIcon
} from '@heroicons/react/24/outline'
import ImageBedFolderIcon from '../icons/ImageBedFolderIcon'

interface MobileFileListProps {
  files: FileItem[]
  selectedFiles: Set<string>
  onFileSelect: (fileId: string, selected: boolean) => void
  onSelectAll: () => void
  onFolderOpen: (folderId: string, folderName: string) => void
  onFilePreview?: (file: FileItem) => void
  onContextMenu?: (file: FileItem, position: { x: number; y: number }) => void
  loading?: boolean
  selectionMode?: boolean
  onSelectionModeChange?: (enabled: boolean) => void
  showRestoreAction?: boolean
  onRestore?: (fileId: string) => void
}

interface SwipeAction {
  id: string
  label: string
  icon: React.ReactNode
  color: string
  action: (file: FileItem) => void
}

const MobileFileList: React.FC<MobileFileListProps> = ({
  files,
  selectedFiles,
  onFileSelect,
  onSelectAll,
  onFolderOpen,
  onFilePreview,
  onContextMenu,
  loading = false,
  selectionMode = false,
  onSelectionModeChange,
  showRestoreAction = false,
  onRestore
}) => {
  const { isMobile } = useMobile()
  const [swipedItem, setSwipedItem] = useState<string | null>(null)
  const [swipeOffset, setSwipeOffset] = useState(0)
  const swipeItemRef = useRef<string | null>(null)

  const getFileIcon = (file: FileItem) => {
    if (file.type === 'folder') {
      if (file.isImageBedFolder) {
        return <ImageBedFolderIcon size="lg" />
      }
      return <FolderIcon className="w-8 h-8 text-cyan-500" />
    }

    if (!file.mimeType) {
      return <DocumentIcon className="w-8 h-8 text-comment" />
    }

    if (file.mimeType.startsWith('image/')) {
      return <PhotoIcon className="w-8 h-8 text-green-500" />
    }

    if (file.mimeType.startsWith('video/')) {
      return <VideoCameraIcon className="w-8 h-8 text-red-500" />
    }

    if (file.mimeType.startsWith('audio/')) {
      return <MusicalNoteIcon className="w-8 h-8 text-purple-500" />
    }

    if (file.mimeType.includes('zip') || file.mimeType.includes('rar') || file.mimeType.includes('tar')) {
      return <ArchiveBoxIcon className="w-8 h-8 text-orange-500" />
    }

    return <DocumentIcon className="w-8 h-8 text-comment" />
  }

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return ''
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
  }

  const formatDate = (date: Date) => {
    const now = new Date()
    const diffTime = now.getTime() - date.getTime()
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 0) {
      return new Intl.DateTimeFormat('en-US', {
        hour: '2-digit',
        minute: '2-digit'
      }).format(date)
    } else if (diffDays < 7) {
      return `${diffDays}d ago`
    } else {
      return new Intl.DateTimeFormat('en-US', {
        month: 'short',
        day: 'numeric'
      }).format(date)
    }
  }

  // Swipe actions for files
  const getSwipeActions = (file: FileItem): SwipeAction[] => [
    {
      id: 'share',
      label: 'Share',
      icon: <span className="text-lg">📤</span>,
      color: 'bg-blue-500',
      action: (file) => {
        // Handle share action
        console.log('Share file:', file.name)
      }
    },
    {
      id: 'download',
      label: 'Download',
      icon: <span className="text-lg">⬇️</span>,
      color: 'bg-green-500',
      action: (file) => {
        // Handle download action
        console.log('Download file:', file.name)
      }
    },
    {
      id: 'delete',
      label: 'Delete',
      icon: <span className="text-lg">🗑️</span>,
      color: 'bg-red-500',
      action: (file) => {
        // Handle delete action
        console.log('Delete file:', file.name)
      }
    }
  ]

  const FileListItem: React.FC<{ file: FileItem; index: number }> = ({ file, index }) => {
    const itemRef = useRef<HTMLDivElement>(null)
    const [isDragging, setIsDragging] = useState(false)
    const [dragOffset, setDragOffset] = useState(0)
    const [showActions, setShowActions] = useState(false)
    const [isPressed, setIsPressed] = useState(false)
    const [pressTimer, setPressTimer] = useState<NodeJS.Timeout | null>(null)

    const swipeActions = getSwipeActions(file)
    const isSelected = selectedFiles.has(file.id)
    const isSwipedOut = swipedItem === file.id

    const { attachGestures } = useGestures({
      onSwipeLeft: () => {
        if (!selectionMode) {
          setSwipedItem(file.id)
          setShowActions(true)
          // Haptic feedback
          if ('vibrate' in navigator) {
            navigator.vibrate(50)
          }
        }
      },
      onSwipeRight: () => {
        if (isSwipedOut) {
          setSwipedItem(null)
          setShowActions(false)
        }
      },
      onLongPress: (x, y) => {
        if (!selectionMode) {
          onSelectionModeChange?.(true)
          onFileSelect(file.id, true)
          // Stronger haptic feedback for selection mode
          if ('vibrate' in navigator) {
            navigator.vibrate([100, 50, 100])
          }
        } else if (onContextMenu) {
          onContextMenu(file, { x, y })
        }
      },
      onDoubleTap: () => {
        if (!selectionMode) {
          if (file.type === 'folder') {
            onFolderOpen(file.id, file.name)
          } else if (onFilePreview) {
            onFilePreview(file)
          }
        }
      }
    }, {
      swipeThreshold: 60,
      longPressDelay: 500 // Reduced for better responsiveness
    })

    React.useEffect(() => {
      const cleanup = attachGestures(itemRef.current)
      return cleanup
    }, [attachGestures])

    const handleTouchStart = () => {
      setIsPressed(true)
      // Light haptic feedback on touch
      if ('vibrate' in navigator) {
        navigator.vibrate(10)
      }
    }

    const handleTouchEnd = () => {
      setIsPressed(false)
      if (pressTimer) {
        clearTimeout(pressTimer)
        setPressTimer(null)
      }
    }

    const handleTap = () => {
      if (selectionMode) {
        onFileSelect(file.id, !isSelected)
        // Haptic feedback for selection
        if ('vibrate' in navigator) {
          navigator.vibrate(30)
        }
      } else {
        if (file.type === 'folder') {
          onFolderOpen(file.id, file.name)
        } else if (onFilePreview) {
          onFilePreview(file)
        }
      }
    }

    const handleActionTap = (action: SwipeAction) => {
      action.action(file)
      setSwipedItem(null)
      setShowActions(false)
    }

    return (
      <div className="relative overflow-hidden">
        {/* Swipe Actions Background */}
        {showActions && (
          <div className="absolute inset-y-0 right-0 flex">
            {swipeActions.map((action, actionIndex) => (
              <button
                key={action.id}
                onClick={() => handleActionTap(action)}
                className={cn(
                  'flex flex-col items-center justify-center w-20 text-white text-xs font-medium transition-all duration-200',
                  action.color,
                  isSwipedOut && 'translate-x-0'
                )}
                style={{
                  transform: isSwipedOut ? 'translateX(0)' : `translateX(${(actionIndex + 1) * 80}px)`
                }}
              >
                {action.icon}
                <span className="mt-1">{action.label}</span>
              </button>
            ))}
          </div>
        )}

        {/* File Item */}
        <div
          ref={itemRef}
          data-file-item="true"
          className={cn(
            'flex items-center p-4 bg-primary border-b border-primary transition-all duration-200 touch-manipulation select-none',
            'active:bg-current-line active:scale-[0.98]',
            isSelected && 'bg-purple-500/10 border-purple-500/30',
            isSwipedOut && '-translate-x-60',
            isPressed && 'bg-current-line scale-[0.98]'
          )}
          onClick={handleTap}
          onTouchStart={handleTouchStart}
          onTouchEnd={handleTouchEnd}
        >
          {/* Selection Checkbox - 图床文件夹不显示勾选框 */}
          {selectionMode && !file.isImageBedFolder && (
            <div className="mr-3 flex-shrink-0">
              <div
                className={cn(
                  'w-6 h-6 rounded-full border-2 flex items-center justify-center transition-colors',
                  isSelected
                    ? 'bg-purple-500 border-purple-500'
                    : 'border-comment'
                )}
              >
                {isSelected && <CheckIcon className="w-4 h-4 text-white" />}
              </div>
            </div>
          )}

          {/* File Icon */}
          <div className="mr-4 flex-shrink-0">
            {file.thumbnailUrl && file.type !== 'folder' ? (
              <img
                src={file.thumbnailUrl}
                alt={file.name}
                className="w-12 h-12 object-cover rounded-lg"
              />
            ) : (
              <div className="w-12 h-12 flex items-center justify-center">
                {getFileIcon(file)}
              </div>
            )}
          </div>

          {/* File Info */}
          <div className="flex-1 min-w-0">
            <h3 className="text-primary font-medium truncate">
              {file.name}
            </h3>
            {/* 图床文件夹不显示大小和时间信息 */}
            {!file.isImageBedFolder && (
              <div className="flex items-center space-x-2 mt-1 text-sm text-comment">
                {file.size && <span>{formatFileSize(file.size)}</span>}
                <span>•</span>
                <span>{formatDate(file.modifiedAt)}</span>
              </div>
            )}
          </div>

          {/* More Options */}
          {!selectionMode && (
            <button
              onClick={(e) => {
                e.stopPropagation()
                if (onContextMenu) {
                  const rect = e.currentTarget.getBoundingClientRect()
                  onContextMenu(file, {
                    x: rect.right,
                    y: rect.top + rect.height / 2
                  })
                }
              }}
              className="p-2 hover-bg rounded-lg transition-colors"
            >
              <EllipsisVerticalIcon className="w-5 h-5 text-comment" />
            </button>
          )}
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
      </div>
    )
  }

  if (files.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-comment p-8">
        <FolderIcon className="w-16 h-16 mb-4" />
        <p className="text-lg font-medium">No files found</p>
        <p className="text-sm text-center">Upload files or create folders to get started</p>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full">
      {/* Selection Header */}
      {selectionMode && (
        <div className="flex items-center justify-between p-4 bg-current-line border-b border-primary">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => onSelectionModeChange?.(false)}
              className="text-purple-500 font-medium"
            >
              Cancel
            </button>
            <span className="text-primary">
              {selectedFiles.size} selected
            </span>
          </div>
          <button
            onClick={onSelectAll}
            className="text-purple-500 font-medium"
          >
            Select All
          </button>
        </div>
      )}

      {/* File List */}
      <div className="flex-1 overflow-y-auto">
        {files.map((file, index) => (
          <FileListItem key={file.id} file={file} index={index} />
        ))}
      </div>

      {/* Swipe Hint */}
      {isMobile && !selectionMode && files.length > 0 && (
        <div className="p-2 text-center text-xs text-comment bg-current-line">
          Swipe left for actions • Long press to select • Double tap to open
        </div>
      )}
    </div>
  )
}

export default MobileFileList