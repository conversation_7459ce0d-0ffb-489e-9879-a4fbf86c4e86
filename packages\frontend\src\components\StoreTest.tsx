import React from 'react'
import { useAuthStore, useFileStore, useSyncStore } from '../stores'

const StoreTest: React.FC = () => {
  const { user, isAuthenticated, isLoading } = useAuthStore()
  const { files, currentFolder, isLoading: filesLoading } = useFileStore()
  const { syncStatus, isOnline, isConnected } = useSyncStore()

  return (
    <div className="p-4 bg-gray-100 rounded-lg m-4">
      <h3 className="text-lg font-bold mb-4">Store Test</h3>
      
      <div className="mb-4">
        <h4 className="font-semibold">Auth Store:</h4>
        <p>Authenticated: {isAuthenticated ? 'Yes' : 'No'}</p>
        <p>Loading: {isLoading ? 'Yes' : 'No'}</p>
        <p>User: {user ? user.username : 'None'}</p>
      </div>

      <div className="mb-4">
        <h4 className="font-semibold">File Store:</h4>
        <p>Files count: {files.length}</p>
        <p>Current folder: {currentFolder || 'Root'}</p>
        <p>Loading: {filesLoading ? 'Yes' : 'No'}</p>
      </div>

      <div className="mb-4">
        <h4 className="font-semibold">Sync Store:</h4>
        <p>Online: {isOnline ? 'Yes' : 'No'}</p>
        <p>Connected: {isConnected ? 'Yes' : 'No'}</p>
        <p>Sync Status: {syncStatus ? 'Available' : 'None'}</p>
      </div>
    </div>
  )
}

export default StoreTest
