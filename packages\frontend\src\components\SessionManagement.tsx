import React, { useState, useEffect } from 'react';
import { Session } from '@cloud-storage/shared';
import { useAuth } from '../contexts/AuthContext';
import { Laptop, Smartphone, Tablet, Monitor, X, RefreshCw, AlertTriangle, Wifi, WifiOff } from 'lucide-react';
import { useI18n } from '../contexts/I18nContext';
import { showConfirmDialog, showNotification } from '../contexts/ToastContext';
import { formatDistanceToNow } from 'date-fns';
import useWebSocket from '../hooks/useWebSocket';

interface SessionManagementProps {
  onClose?: () => void;
}

const SessionManagement: React.FC<SessionManagementProps> = ({ onClose }) => {
  const { sessions, getSessions, revokeSession, revokeAllSessions, isLoading } = useAuth();
  const { t } = useI18n();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { isConnected, subscribe } = useWebSocket();

  // Load sessions on initial render
  useEffect(() => {
    loadSessions();
  }, []);
  
  // Subscribe to session-related WebSocket events
  useEffect(() => {
    if (!isConnected) return;
    
    // Subscribe to session events
    const unsubscribeSessionRevoked = subscribe('session_revoked', () => {
      // Refresh sessions when a session is revoked
      loadSessions();
    });
    
    const unsubscribeSessionCreated = subscribe('session_created', () => {
      // Refresh sessions when a new session is created
      loadSessions();
    });
    
    const unsubscribeSessionUpdated = subscribe('session_updated', () => {
      // Refresh sessions when a session is updated
      loadSessions();
    });
    
    // Cleanup subscriptions
    return () => {
      unsubscribeSessionRevoked();
      unsubscribeSessionCreated();
      unsubscribeSessionUpdated();
    };
  }, [isConnected, subscribe]);

  const loadSessions = async () => {
    setIsRefreshing(true);
    setError(null);
    try {
      await getSessions();
    } catch (err) {
      setError(t('error.failedToLoadSessions'));
      console.error('Failed to load sessions:', err);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleRevokeSession = async (sessionId: string) => {
    try {
      await revokeSession(sessionId);
    } catch (err) {
      setError(t('error.failedToRevokeSession'));
      console.error('Failed to revoke session:', err);
    }
  };

  const handleRevokeAllSessions = async () => {
    const confirmed = await showConfirmDialog({
      title: t('auth.confirmRevokeAllSessions'),
      message: t('auth.confirmRevokeAllSessionsMessage') || 'This will log you out from all devices. You will need to log in again.',
      confirmText: t('auth.revokeAll'),
      cancelText: t('common.cancel'),
      type: 'warning',
      onConfirm: () => {},
      onCancel: () => {}
    });

    if (!confirmed) return;

    try {
      await revokeAllSessions();
      showNotification('success', t('auth.allSessionsRevoked') || 'All sessions revoked successfully');
    } catch (err: any) {
      const errorMessage = err?.response?.data?.error || err?.message || t('error.failedToRevokeAllSessions');
      setError(errorMessage);
      showNotification('error', t('error.failedToRevokeAllSessions'), errorMessage);
      console.error('Failed to revoke all sessions:', err);
    }
  };

  const getDeviceIcon = (session: Session) => {
    const deviceType = session.deviceInfo.type.toLowerCase();
    
    if (deviceType.includes('mobile') || deviceType.includes('phone')) {
      return <Smartphone className="h-5 w-5" />;
    } else if (deviceType.includes('tablet')) {
      return <Tablet className="h-5 w-5" />;
    } else if (deviceType.includes('desktop')) {
      return <Monitor className="h-5 w-5" />;
    } else {
      return <Laptop className="h-5 w-5" />;
    }
  };

  const formatLastActive = (date: Date) => {
    return formatDistanceToNow(new Date(date), { addSuffix: true });
  };

  return (
    <div className="bg-secondary rounded-lg shadow-lg p-4 w-full max-w-2xl">
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center">
          <h2 className="text-xl font-semibold text-primary">{t('auth.activeSessions')}</h2>
          {isConnected ? (
            <div className="ml-2 flex items-center text-green-500 text-xs">
              <Wifi className="h-3 w-3 mr-1" />
              <span>{t('status.realTimeUpdates')}</span>
            </div>
          ) : (
            <div className="ml-2 flex items-center text-gray-500 text-xs">
              <WifiOff className="h-3 w-3 mr-1" />
              <span>{t('status.offlineMode')}</span>
            </div>
          )}
        </div>
        <div className="flex space-x-2">
          <button 
            onClick={loadSessions} 
            className="p-2 rounded-full hover:bg-primary/10 transition-colors"
            disabled={isRefreshing}
            title={t('actions.refresh')}
          >
            <RefreshCw className={`h-5 w-5 ${isRefreshing ? 'animate-spin' : ''}`} />
          </button>
          {onClose && (
            <button 
              onClick={onClose} 
              className="p-2 rounded-full hover:bg-primary/10 transition-colors"
              title={t('actions.close')}
            >
              <X className="h-5 w-5" />
            </button>
          )}
        </div>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg flex items-center space-x-2">
          <AlertTriangle className="h-5 w-5 text-red-500 flex-shrink-0" />
          <span className="text-red-700 dark:text-red-300 text-sm">{error}</span>
        </div>
      )}

      <div className="space-y-4">
        {isLoading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : sessions.length === 0 ? (
          <div className="text-center py-8 text-comment">
            {t('auth.noActiveSessions')}
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 gap-3">
              {sessions.map((session) => (
                <div 
                  key={session.id} 
                  className={`border rounded-lg p-3 flex justify-between items-center ${
                    session.isCurrent 
                      ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20' 
                      : 'border-gray-200 dark:border-gray-700'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-primary/5 rounded-full">
                      {getDeviceIcon(session)}
                    </div>
                    <div>
                      <div className="font-medium text-primary flex items-center">
                        {session.deviceInfo.name || session.deviceInfo.browser || t('auth.unknownDevice')}
                        {session.isCurrent && (
                          <span className="ml-2 text-xs bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-0.5 rounded-full">
                            {t('auth.currentDevice')}
                          </span>
                        )}
                      </div>
                      <div className="text-sm text-comment">
                        {session.deviceInfo.os} {session.deviceInfo.osVersion || ''}
                        {session.deviceInfo.browser && ` • ${session.deviceInfo.browser} ${session.deviceInfo.browserVersion || ''}`}
                      </div>
                      <div className="text-xs text-comment mt-1">
                        {session.location && `${session.location} • `}
                        {t('auth.lastActive')} {formatLastActive(session.lastUsedAt)}
                      </div>
                    </div>
                  </div>
                  {!session.isCurrent && (
                    <button
                      onClick={() => handleRevokeSession(session.id)}
                      className="ml-2 p-2 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-full transition-colors"
                      title={t('auth.revokeSession')}
                    >
                      <X className="h-5 w-5" />
                    </button>
                  )}
                </div>
              ))}
            </div>

            <div className="flex justify-end pt-2">
              <button
                onClick={handleRevokeAllSessions}
                className="text-sm text-red-500 hover:text-red-600 dark:hover:text-red-400 transition-colors"
              >
                {t('auth.logoutAllDevices')}
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default SessionManagement;