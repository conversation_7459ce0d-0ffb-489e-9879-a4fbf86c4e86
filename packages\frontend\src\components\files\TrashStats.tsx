import React from 'react'
import { useI18n } from '../../contexts/I18nContext'
import { FileList as FileListType } from '@cloud-storage/shared'
import { 
  DocumentIcon,
  FolderIcon,
  PhotoIcon,
  VideoCameraIcon,
  MusicalNoteIcon,
  ArchiveBoxIcon
} from '@heroicons/react/24/outline'

interface TrashStatsProps {
  trashContents: FileListType | null
}

const TrashStats: React.FC<TrashStatsProps> = ({ trashContents }) => {
  const { t } = useI18n()

  if (!trashContents || trashContents.totalCount === 0) {
    return null
  }

  // Calculate statistics
  const stats = {
    totalFiles: trashContents.files.length,
    totalFolders: trashContents.folders.length,
    totalSize: trashContents.files.reduce((sum, file) => sum + file.size, 0),
    fileTypes: {
      images: trashContents.files.filter(f => f.mimeType.startsWith('image/')).length,
      videos: trashContents.files.filter(f => f.mimeType.startsWith('video/')).length,
      audio: trashContents.files.filter(f => f.mimeType.startsWith('audio/')).length,
      documents: trashContents.files.filter(f => 
        f.mimeType.includes('pdf') || 
        f.mimeType.includes('document') || 
        f.mimeType.includes('text/')
      ).length,
      archives: trashContents.files.filter(f => 
        f.mimeType.includes('zip') || 
        f.mimeType.includes('rar') || 
        f.mimeType.includes('archive')
      ).length,
      others: 0
    }
  }

  stats.fileTypes.others = stats.totalFiles - (
    stats.fileTypes.images + 
    stats.fileTypes.videos + 
    stats.fileTypes.audio + 
    stats.fileTypes.documents + 
    stats.fileTypes.archives
  )

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const typeStats = [
    { 
      type: 'folders', 
      count: stats.totalFolders, 
      icon: FolderIcon, 
      color: 'text-blue-500',
      label: t('files.folders')
    },
    { 
      type: 'images', 
      count: stats.fileTypes.images, 
      icon: PhotoIcon, 
      color: 'text-green-500',
      label: t('files.images')
    },
    { 
      type: 'videos', 
      count: stats.fileTypes.videos, 
      icon: VideoCameraIcon, 
      color: 'text-red-500',
      label: t('files.videos')
    },
    { 
      type: 'audio', 
      count: stats.fileTypes.audio, 
      icon: MusicalNoteIcon, 
      color: 'text-purple-500',
      label: t('files.audio')
    },
    { 
      type: 'documents', 
      count: stats.fileTypes.documents, 
      icon: DocumentIcon, 
      color: 'text-orange-500',
      label: t('files.documents')
    },
    { 
      type: 'archives', 
      count: stats.fileTypes.archives, 
      icon: ArchiveBoxIcon, 
      color: 'text-gray-500',
      label: t('files.archives')
    }
  ].filter(stat => stat.count > 0)

  return (
    <div className="bg-secondary border border-current-line rounded-lg p-4 mb-4">
      <h3 className="text-sm font-medium text-primary mb-3">{t('files.trashStatistics')}</h3>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
        <div className="text-center p-2">
          <div className="text-2xl font-bold text-primary">{trashContents.totalCount}</div>
          <div className="text-sm text-comment">{t('files.totalItems')}</div>
        </div>
        <div className="text-center p-2">
          <div className="text-2xl font-bold text-primary">{stats.totalFiles}</div>
          <div className="text-sm text-comment">{t('files.files')}</div>
        </div>
        <div className="text-center p-2">
          <div className="text-2xl font-bold text-primary">{stats.totalFolders}</div>
          <div className="text-sm text-comment">{t('files.folders')}</div>
        </div>
        <div className="text-center p-2">
          <div className="text-2xl font-bold text-primary">{formatFileSize(stats.totalSize)}</div>
          <div className="text-sm text-comment">{t('files.totalSize')}</div>
        </div>
      </div>

      {/* {typeStats.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
          {typeStats.map(stat => {
            const Icon = stat.icon
            return (
              <div key={stat.type} className="flex items-center space-x-3 p-3 bg-current-line rounded-lg">
                <Icon className={`w-6 h-6 ${stat.color} flex-shrink-0`} />
                <div className="flex-1 min-w-0">
                  <div className="text-lg font-semibold text-primary">{stat.count}</div>
                  <div className="text-sm text-comment truncate">{stat.label}</div>
                </div>
              </div>
            )
          })}
        </div>
      )} */}
    </div>
  )
}

export default TrashStats