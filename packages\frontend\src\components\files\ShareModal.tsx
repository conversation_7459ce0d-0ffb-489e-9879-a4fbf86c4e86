import React, { useState } from 'react'
import { 
  Share2, 
  <PERSON><PERSON>, 
  <PERSON>, 
  Calendar, 
  Download, 
  X, 
  CheckCircle,
  AlertCircle,
  Eye,
  EyeOff
} from 'lucide-react'
import { FileMetadata } from '@cloud-storage/shared'
import { fileService } from '../../services/fileService'

interface ShareModalProps {
  file: FileMetadata
  isOpen: boolean
  onClose: () => void
  onShareCreated?: (shareLink: any) => void
}

const ShareModal: React.FC<ShareModalProps> = ({ file, isOpen, onClose, onShareCreated }) => {
  const [loading, setLoading] = useState(false)
  const [shareLink, setShareLink] = useState<string | null>(null)
  const [copied, setCopied] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // 分享设置
  const [hasPassword, setHasPassword] = useState(false)
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [hasExpiration, setHasExpiration] = useState(false)
  const [expirationDate, setExpirationDate] = useState('')
  const [hasDownloadLimit, setHasDownloadLimit] = useState(false)
  const [downloadLimit, setDownloadLimit] = useState(100)

  const createShare = async () => {
    try {
      setLoading(true)
      setError(null)

      const shareOptions: any = {
        permissions: [{ type: 'read', granted: true }]
      }

      const shareLink = await fileService.shareFile(fileId, shareOptions)
      onShareCreated?.(shareLink)
      onClose()
    } catch (error: any) {
      console.error('Failed to create share:', error)
      setError(error?.response?.data?.error || error?.message || 'Failed to create share')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={`Share ${fileName}`}>
      <div className="space-y-4">
        {error && (
          <div className="text-red-600 text-sm">{error}</div>
        )}

        <div className="flex justify-end space-x-2">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
            disabled={loading}
          >
            Cancel
          </button>
          <button
            onClick={handleCreateShare}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Creating...' : 'Create Share'}
          </button>
        </div>
      </div>
    </Modal>
  )
}
    