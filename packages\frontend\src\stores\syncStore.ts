import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { SyncStatus, SyncEvent, SyncConflict } from '@cloud-storage/shared'
import { syncService } from '../services/syncService'
import { offlineQueueService } from '../services/offlineQueueService'
import { websocketService } from '../services/websocketService'

interface SyncState {
  // State
  syncStatus: SyncStatus | null
  isOnline: boolean
  isConnected: boolean
  pendingChanges: SyncEvent[]
  conflicts: SyncConflict[]
  syncProgress: number
  currentOperation: string | null
  queuedOperations: number
  lastSyncTime: Date | null
  syncMode: 'AUTOMATIC' | 'MANUAL' | 'PAUSED'
  error: string | null

  // Actions
  connect: () => void
  disconnect: () => void
  startSync: () => Promise<void>
  pauseSync: () => void
  resumeSync: () => void
  resolveConflict: (conflictId: string, strategy: 'KEEP_LOCAL' | 'KEEP_REMOTE' | 'MERGE' | 'RENAME') => Promise<void>
  retryFailedSync: () => Promise<void>
  clearSyncErrors: () => void
  setSyncMode: (mode: 'AUTOMATIC' | 'MANUAL' | 'PAUSED') => Promise<void>
  queueOfflineOperation: (operation: {
    type: 'SYNC_EVENT' | 'API_CALL'
    data: any
    priority?: number
    maxRetries?: number
  }) => void
  updateStatus: (status: SyncStatus) => void
  addEvent: (event: SyncEvent) => void
  initializeSyncStatus: () => Promise<void>
  setOnlineStatus: (isOnline: boolean) => void
}

export const useSyncStore = create<SyncState>()(
  persist(
    (set, get) => ({
      // Initial state
      syncStatus: null,
      isOnline: navigator.onLine,
      isConnected: false,
      pendingChanges: [],
      conflicts: [],
      syncProgress: 0,
      currentOperation: null,
      queuedOperations: 0,
      lastSyncTime: null,
      syncMode: 'AUTOMATIC',
      error: null,

      // Actions
      connect: () => {
        try {
          websocketService.connect()
          set({ isConnected: true })
        } catch (error) {
          console.error('Failed to connect WebSocket:', error)
          set({ error: 'WebSocket连接失败' })
        }
      },

      disconnect: () => {
        try {
          websocketService.disconnect()
          set({ isConnected: false })
        } catch (error) {
          console.error('Failed to disconnect WebSocket:', error)
        }
      },

      startSync: async () => {
        const { isOnline, syncMode } = get()
        
        if (!isOnline || syncMode === 'PAUSED') {
          return
        }

        set({ 
          syncProgress: 0, 
          currentOperation: '正在同步...', 
          error: null 
        })

        try {
          // Start sync process
          await syncService.triggerSync()

          // Update sync status
          const status = await syncService.getSyncStatus()
          set({
            syncStatus: status,
            syncProgress: 100,
            currentOperation: null,
            lastSyncTime: new Date()
          })
        } catch (error: any) {
          console.error('Sync failed:', error)
          set({
            error: error.message || '同步失败',
            syncProgress: 0,
            currentOperation: null
          })
        }
      },

      pauseSync: () => {
        set({ syncMode: 'PAUSED' })
        // Update sync mode via API
        syncService.updateSyncMode('PAUSED').catch(error => {
          console.error('Failed to pause sync:', error)
        })
      },

      resumeSync: () => {
        set({ syncMode: 'AUTOMATIC' })
        // Update sync mode via API
        syncService.updateSyncMode('AUTOMATIC').then(() => {
          // Auto-start sync if there are pending changes
          const { pendingChanges } = get()
          if (pendingChanges.length > 0) {
            get().startSync()
          }
        }).catch(error => {
          console.error('Failed to resume sync:', error)
        })
      },

      resolveConflict: async (conflictId: string, strategy: 'KEEP_LOCAL' | 'KEEP_REMOTE' | 'MERGE' | 'RENAME') => {
        try {
          await syncService.resolveConflict(conflictId, strategy)
          
          // Remove resolved conflict from state
          set(state => ({
            conflicts: state.conflicts.filter(conflict => conflict.id !== conflictId)
          }))
        } catch (error: any) {
          console.error('Failed to resolve conflict:', error)
          set({ error: error.message || '解决冲突失败' })
        }
      },

      retryFailedSync: async () => {
        try {
          await get().startSync()
        } catch (error: any) {
          console.error('Retry sync failed:', error)
          set({ error: error.message || '重试同步失败' })
        }
      },

      clearSyncErrors: () => {
        set({ error: null })
      },

      setSyncMode: async (mode: 'AUTOMATIC' | 'MANUAL' | 'PAUSED') => {
        try {
          // Update sync mode via API
          await syncService.updateSyncMode(mode)
          
          set({ syncMode: mode })
          
          // Update sync status
          set(state => 
            state.syncStatus 
              ? { syncStatus: { ...state.syncStatus, syncMode: mode } }
              : {}
          )
        } catch (error: any) {
          console.error('Failed to update sync mode:', error)
          set({ error: error.message || '更新同步模式失败' })
        }
      },

      queueOfflineOperation: (operation: {
        type: 'SYNC_EVENT' | 'API_CALL'
        data: any
        priority?: number
        maxRetries?: number
      }) => {
        try {
          offlineQueueService.enqueue({
            type: operation.type,
            data: operation.data,
            priority: operation.priority || 1,
            maxRetries: operation.maxRetries || 3
          })

          set(state => ({
            queuedOperations: state.queuedOperations + 1
          }))
        } catch (error) {
          console.error('Failed to queue offline operation:', error)
        }
      },

      updateStatus: (status: SyncStatus) => {
        set({ 
          syncStatus: status,
          syncProgress: status.syncProgress || 0,
          currentOperation: status.currentOperation || null
        })
      },

      addEvent: (event: SyncEvent) => {
        set(state => ({
          pendingChanges: [...state.pendingChanges, event]
        }))
      },

      initializeSyncStatus: async () => {
        try {
          const status = await syncService.getSyncStatus()
          set({ 
            syncStatus: status,
            syncMode: status.syncMode || 'AUTOMATIC',
            lastSyncTime: status.lastSyncTime ? new Date(status.lastSyncTime) : null
          })
        } catch (error: any) {
          console.error('Failed to initialize sync status:', error)
          
          // Set default values on error
          const deviceId = syncService.getDeviceId()
          set({
            syncStatus: {
              userId: '',
              deviceId,
              lastSyncTime: new Date(),
              pendingChanges: 0,
              conflictCount: 0,
              isOnline: get().isOnline,
              syncProgress: 0,
              currentOperation: undefined,
              errorCount: 1,
              lastError: error.message || 'Unknown error',
              syncMode: 'MANUAL'
            },
            error: error.message || '初始化同步状态失败'
          })
        }
      },

      setOnlineStatus: (isOnline: boolean) => {
        set({ isOnline })
        
        if (isOnline) {
          // When coming back online, process offline queue
          offlineQueueService.processQueue().then(() => {
            // Auto-sync if there are pending changes and mode is automatic
            const { syncMode, pendingChanges } = get()
            if (syncMode === 'AUTOMATIC' && pendingChanges.length > 0) {
              get().startSync()
            }
          }).catch(error => {
            console.error('Failed to process offline queue:', error)
          })
        }
      }
    }),
    {
      name: 'sync-storage',
      partialize: (state) => ({
        syncMode: state.syncMode,
        lastSyncTime: state.lastSyncTime
      })
    }
  )
)

// Initialize online/offline event listeners
if (typeof window !== 'undefined') {
  window.addEventListener('online', () => {
    useSyncStore.getState().setOnlineStatus(true)
  })
  
  window.addEventListener('offline', () => {
    useSyncStore.getState().setOnlineStatus(false)
  })
}
