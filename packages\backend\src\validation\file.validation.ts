import Joi from 'joi';

export const fileValidation = {
  uploadFile: {
    body: Joi.object({
      folderId: Joi.alternatives().try(
        Joi.string().uuid(),
        Joi.string().regex(/^[0-9a-fA-F]{24}$/) // MongoDB ObjectId format
      ).optional(),
      tags: Joi.string().optional(), // JSON string of tags array
      isPublic: Joi.string().valid('true', 'false').optional()
    })
  },

  uploadFiles: {
    body: Joi.object({
      folderId: Joi.alternatives().try(
        Joi.string().uuid(),
        Joi.string().regex(/^[0-9a-fA-F]{24}$/) // MongoDB ObjectId format
      ).optional(),
      tags: Joi.string().optional(), // JSON string of tags array
      isPublic: Joi.string().valid('true', 'false').optional()
    })
  },

  downloadFile: {
    params: Joi.object({
      fileId: Joi.alternatives().try(
        Joi.string().uuid(),
        Joi.string().regex(/^[0-9a-fA-F]{24}$/) // MongoDB ObjectId format
      ).required()
    })
  },

  previewFile: {
    params: Joi.object({
      fileId: Joi.alternatives().try(
        Joi.string().uuid(),
        Joi.string().regex(/^[0-9a-fA-F]{24}$/) // MongoDB ObjectId format
      ).required()
    }),
    query: Joi.object({
      quality: Joi.string().valid('low', 'medium', 'high').optional(),
      maxWidth: Joi.number().integer().min(100).max(2000).optional(),
      maxHeight: Joi.number().integer().min(100).max(2000).optional()
    })
  },

  listFiles: {
    query: Joi.object({
      folderId: Joi.alternatives().try(
        Joi.string().uuid(),
        Joi.string().regex(/^[0-9a-fA-F]{24}$/) // MongoDB ObjectId format
      ).optional(),
      page: Joi.number().integer().min(1).optional(),
      limit: Joi.number().integer().min(1).max(100).optional(),
      sortBy: Joi.string().valid('name', 'size', 'uploadedAt', 'modifiedAt', 'type').optional(),
      sortOrder: Joi.string().valid('asc', 'desc').optional(),
      fileType: Joi.string().valid('image', 'video', 'audio', 'document', 'archive').optional()
    })
  },

  searchFiles: {
    query: Joi.object({
      q: Joi.string().min(1).required(),
      fileType: Joi.string().optional(),
      folderId: Joi.alternatives().try(
        Joi.string().uuid(),
        Joi.string().regex(/^[0-9a-fA-F]{24}$/) // MongoDB ObjectId format
      ).optional(),
      tags: Joi.string().optional(), // Comma-separated tags
      from: Joi.date().iso().optional(),
      to: Joi.date().iso().optional()
    })
  },

  createFolder: {
    body: Joi.object({
      name: Joi.string().min(1).max(255).required(),
      parentId: Joi.alternatives().try(
        Joi.string().uuid(),
        Joi.string().regex(/^[0-9a-fA-F]{24}$/) // MongoDB ObjectId format
      ).optional()
    })
  },

  moveFile: {
    params: Joi.object({
      fileId: Joi.alternatives().try(
        Joi.string().uuid(),
        Joi.string().regex(/^[0-9a-fA-F]{24}$/) // MongoDB ObjectId format
      ).required()
    }),
    body: Joi.object({
      targetFolderId: Joi.alternatives().try(
        Joi.string().uuid(),
        Joi.string().regex(/^[0-9a-fA-F]{24}$/) // MongoDB ObjectId format
      ).optional().allow(null)
    })
  },

  deleteFile: {
    params: Joi.object({
      fileId: Joi.alternatives().try(
        Joi.string().uuid(),
        Joi.string().regex(/^[0-9a-fA-F]{24}$/) // MongoDB ObjectId format
      ).required()
    })
  },

  shareFile: {
    params: Joi.object({
      fileId: Joi.alternatives().try(
        Joi.string().uuid(),
        Joi.string().regex(/^[0-9a-fA-F]{24}$/) // MongoDB ObjectId format
      ).required()
    }),
    body: Joi.object({
      permissions: Joi.array().items(
        Joi.object({
          type: Joi.string().valid('read', 'write', 'delete').required(),
          granted: Joi.boolean().required()
        })
      ).optional(),
      expiresAt: Joi.date().iso().optional(),
      password: Joi.string().min(4).optional(),
      maxDownloads: Joi.number().integer().min(1).optional()
    })
  },

  getFileInfo: {
    params: Joi.object({
      fileId: Joi.alternatives().try(
        Joi.string().uuid(),
        Joi.string().regex(/^[0-9a-fA-F]{24}$/) // MongoDB ObjectId format
      ).required()
    })
  },

  initChunkedUpload: {
    body: Joi.object({
      filename: Joi.string().min(1).max(255).required(),
      fileSize: Joi.number().integer().min(1).max(500 * 1024 * 1024).required(), // 500MB max
      mimeType: Joi.string().required(),
      totalChunks: Joi.number().integer().min(1).max(1000).required(),
      folderId: Joi.alternatives().try(
        Joi.string().uuid(),
        Joi.string().regex(/^[0-9a-fA-F]{24}$/) // MongoDB ObjectId format
      ).optional(),
      tags: Joi.array().items(Joi.string()).optional()
    })
  },

  uploadChunk: {
    params: Joi.object({
      uploadId: Joi.string().uuid().required()
    }),
    body: Joi.object({
      chunkIndex: Joi.number().integer().min(0).required()
    })
  },

  completeChunkedUpload: {
    params: Joi.object({
      uploadId: Joi.string().uuid().required()
    })
  },

  getUploadProgress: {
    params: Joi.object({
      uploadId: Joi.string().uuid().required()
    })
  },

  getFolderById: {
    params: Joi.object({
      folderId: Joi.alternatives().try(
        Joi.string().uuid(),
        Joi.string().regex(/^[0-9a-fA-F]{24}$/) // MongoDB ObjectId format
      ).required()
    })
  },

  getFolderInfo: {
    params: Joi.object({
      folderId: Joi.alternatives().try(
        Joi.string().uuid(),
        Joi.string().regex(/^[0-9a-fA-F]{24}$/) // MongoDB ObjectId format
      ).required()
    })
  },

  renameFolder: {
    params: Joi.object({
      folderId: Joi.alternatives().try(
        Joi.string().uuid(),
        Joi.string().regex(/^[0-9a-fA-F]{24}$/) // MongoDB ObjectId format
      ).required()
    }),
    body: Joi.object({
      name: Joi.string().min(1).max(255).required()
    })
  },

  moveFolder: {
    params: Joi.object({
      folderId: Joi.alternatives().try(
        Joi.string().uuid(),
        Joi.string().regex(/^[0-9a-fA-F]{24}$/) // MongoDB ObjectId format
      ).required()
    }),
    body: Joi.object({
      targetFolderId: Joi.alternatives().try(
        Joi.string().uuid(),
        Joi.string().regex(/^[0-9a-fA-F]{24}$/) // MongoDB ObjectId format
      ).optional().allow(null)
    })
  },

  deleteFolder: {
    params: Joi.object({
      folderId: Joi.alternatives().try(
        Joi.string().uuid(),
        Joi.string().regex(/^[0-9a-fA-F]{24}$/) // MongoDB ObjectId format
      ).required()
    }),
    query: Joi.object({
      permanent: Joi.string().valid('true', 'false').optional()
    })
  },

  renameFile: {
    params: Joi.object({
      fileId: Joi.alternatives().try(
        Joi.string().uuid(),
        Joi.string().regex(/^[0-9a-fA-F]{24}$/) // MongoDB ObjectId format
      ).required()
    }),
    body: Joi.object({
      name: Joi.string().min(1).max(255).required()
    })
  },

  duplicateFile: {
    params: Joi.object({
      fileId: Joi.alternatives().try(
        Joi.string().uuid(),
        Joi.string().regex(/^[0-9a-fA-F]{24}$/) // MongoDB ObjectId format
      ).required()
    }),
    body: Joi.object({
      newName: Joi.string().min(1).max(255).optional()
    })
  },

  copyFile: {
    params: Joi.object({
      fileId: Joi.alternatives().try(
        Joi.string().uuid(),
        Joi.string().regex(/^[0-9a-fA-F]{24}$/) // MongoDB ObjectId format
      ).required()
    }),
    body: Joi.object({
      targetFolderId: Joi.alternatives().try(
        Joi.string().uuid(),
        Joi.string().regex(/^[0-9a-fA-F]{24}$/) // MongoDB ObjectId format
      ).optional()
    })
  },

  moveFileToTrash: {
    params: Joi.object({
      fileId: Joi.alternatives().try(
        Joi.string().uuid(),
        Joi.string().regex(/^[0-9a-fA-F]{24}$/) // MongoDB ObjectId format
      ).required()
    })
  },

  getTrashContents: {
    query: Joi.object({
      page: Joi.number().integer().min(1).optional(),
      limit: Joi.number().integer().min(1).max(100).optional(),
      sortBy: Joi.string().valid('name', 'size', 'deletedAt', 'modifiedAt').optional(),
      sortOrder: Joi.string().valid('asc', 'desc').optional()
    })
  },

  restoreFromTrash: {
    params: Joi.object({
      fileId: Joi.alternatives().try(
        Joi.string().uuid(),
        Joi.string().regex(/^[0-9a-fA-F]{24}$/) // MongoDB ObjectId format
      ).required()
    })
  },

  getFileShares: {
    params: Joi.object({
      fileId: Joi.alternatives().try(
        Joi.string().uuid(),
        Joi.string().regex(/^[0-9a-fA-F]{24}$/) // MongoDB ObjectId format
      ).required()
    })
  },

  updateShareLink: {
    params: Joi.object({
      shareId: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).required()
    }),
    body: Joi.object({
      permissions: Joi.array().items(
        Joi.object({
          type: Joi.string().valid('read', 'write', 'delete').required(),
          granted: Joi.boolean().required()
        })
      ).optional(),
      expiresAt: Joi.date().iso().optional().allow(null),
      password: Joi.string().min(4).optional().allow(null),
      maxDownloads: Joi.number().integer().min(1).optional().allow(null)
    })
  },

  revokeShareLink: {
    params: Joi.object({
      shareId: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).required()
    })
  },

  accessPublicShare: {
    params: Joi.object({
      token: Joi.string().length(64).required()
    }),
    query: Joi.object({
      password: Joi.string().optional()
    })
  },

  accessPublicShareWithPassword: {
    params: Joi.object({
      token: Joi.string().length(64).required()
    }),
    body: Joi.object({
      password: Joi.string().required()
    })
  },

  downloadPublicShare: {
    params: Joi.object({
      token: Joi.string().length(64).required()
    }),
    body: Joi.object({
      password: Joi.string().optional()
    })
  },

  previewPublicShare: {
    params: Joi.object({
      token: Joi.string().length(64).required()
    }),
    query: Joi.object({
      password: Joi.string().optional()
    })
  }
};

export default fileValidation;