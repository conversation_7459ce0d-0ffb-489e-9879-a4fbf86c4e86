import React from 'react'
import <PERSON><PERSON> from '../ui/Button'
import { useI18n } from '../../contexts/I18nContext'
import { 
  TrashIcon,
  ArrowDownTrayIcon,
  FolderIcon,
  ShareIcon,
  DocumentDuplicateIcon
} from '@heroicons/react/24/outline'

interface BatchOperationsProps {
  selectedCount: number
  onDownload: () => void
  onMove: () => void
  onCopy: () => void
  onShare: () => void
  onDelete: () => void
  loading?: boolean
  isImageBedFolder?: boolean
}

const BatchOperations: React.FC<BatchOperationsProps> = ({
  selectedCount,
  onDownload,
  onMove,
  onCopy,
  onShare,
  onDelete,
  loading = false,
  isImageBedFolder = false
}) => {
  const { t } = useI18n()
  
  if (selectedCount === 0) return null

  return (
    <div className="flex items-center space-x-2 p-3 bg-current-line rounded-lg border border-purple-500/30">
      <span className="text-sm text-primary font-medium">
        {selectedCount} {selectedCount === 1 ? t('files.item') : t('files.items')} {t('files.selected') || 'selected'}
      </span>
      
      <div className="flex items-center space-x-1 ml-4">
        <Button
          variant="outline"
          size="sm"
          onClick={onDownload}
          disabled={loading}
          title={t('files.batchDownload')}
        >
          <ArrowDownTrayIcon className="w-4 h-4" />
        </Button>
        
        {/* In image bed folder, disable move and copy operations */}
        {!isImageBedFolder && (
          <>
            <Button
              variant="outline"
              size="sm"
              onClick={onMove}
              disabled={loading}
              title={t('files.batchMove')}
            >
              <FolderIcon className="w-4 h-4" />
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={onCopy}
              disabled={loading}
              title={t('files.batchCopy')}
            >
              <DocumentDuplicateIcon className="w-4 h-4" />
            </Button>
          </>
        )}
        
        <Button
          variant="outline"
          size="sm"
          onClick={onShare}
          disabled={loading}
          title={t('files.batchShare')}
        >
          <ShareIcon className="w-4 h-4" />
        </Button>
        
        <Button
          variant="danger"
          size="sm"
          onClick={onDelete}
          disabled={loading}
          title={t('files.moveToTrash')}
        >
          <TrashIcon className="w-4 h-4" />
        </Button>
      </div>
    </div>
  )
}

export default BatchOperations