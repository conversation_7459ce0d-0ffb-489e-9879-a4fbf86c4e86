import React, { useState, useEffect } from 'react'
import { useI18n } from '../../contexts/I18nContext'
import { showConfirmDialog, showNotification } from '../../contexts/ToastContext'
import { fileService } from '../../services/fileService'
import { FileList as FileListType } from '@cloud-storage/shared'
import Button from '../ui/Button'
import FileList from './FileList'
import { 
  TrashIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'

interface TrashBinProps {
  onClose: () => void
}

const TrashBin: React.FC<TrashBinProps> = ({ onClose }) => {
  const { t } = useI18n()
  const [trashContents, setTrashContents] = useState<FileListType | null>(null)
  const [loading, setLoading] = useState(false)
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set())
  const [error, setError] = useState<string | null>(null)

  const loadTrashContents = async () => {
    try {
      setLoading(true)
      setError(null)
      const contents = await fileService.getTrashContents({ page: 1, limit: 100 })
      setTrashContents(contents)
    } catch (error) {
      console.error('Failed to load trash contents:', error)
      setError('Failed to load trash contents. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadTrashContents()
  }, [])

  const handleRestore = async (fileId: string) => {
    try {
      setLoading(true)
      await fileService.restoreFromTrash(fileId)
      await loadTrashContents() // Reload trash contents
      setSelectedFiles(prev => {
        const newSet = new Set(prev)
        newSet.delete(fileId)
        return newSet
      })
    } catch (error) {
      console.error('Failed to restore file:', error)
      setError('Failed to restore file. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleRestoreSelected = async () => {
    if (selectedFiles.size === 0) return

    try {
      setLoading(true)
      const restorePromises = Array.from(selectedFiles).map(fileId =>
        fileService.restoreFromTrash(fileId)
      )
      await Promise.all(restorePromises)
      await loadTrashContents()
      setSelectedFiles(new Set())
    } catch (error) {
      console.error('Failed to restore files:', error)
      setError('Failed to restore files. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleEmptyTrash = async () => {
    const itemCount = (trashContents?.files.length || 0) + (trashContents?.folders.length || 0)
    if (itemCount === 0) return

    const confirmed = await showConfirmDialog({
      title: t('files.confirmEmptyTrash'),
      message: t('files.confirmEmptyTrashMessage', { count: itemCount.toString() }),
      confirmText: t('files.emptyTrash'),
      cancelText: t('common.cancel'),
      type: 'danger',
      onConfirm: () => {},
      onCancel: () => {}
    })

    if (!confirmed) return

    try {
      setLoading(true)
      const result = await fileService.emptyTrash()
      await loadTrashContents()

      showNotification(
        'success',
        t('files.emptyTrashSuccess'),
        t('files.emptyTrashSuccessMessage', { count: (result.deletedCount || 0).toString() })
      )
      setSelectedFiles(new Set())
    } catch (error) {
      console.error('Failed to empty trash:', error)
      setError('Failed to empty trash. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleFileSelect = (fileId: string, selected: boolean) => {
    setSelectedFiles(prev => {
      const newSet = new Set(prev)
      if (selected) {
        newSet.add(fileId)
      } else {
        newSet.delete(fileId)
      }
      return newSet
    })
  }

  const handleSelectAll = () => {
    if (!trashContents) return

    const allFiles = [...trashContents.files, ...trashContents.folders]
    if (selectedFiles.size === allFiles.length) {
      setSelectedFiles(new Set())
    } else {
      setSelectedFiles(new Set(allFiles.map(f => f.id)))
    }
  }

  // Convert trash contents to FileItem format for display
  const trashItems = trashContents ? [
    ...trashContents.folders.map(folder => ({
      id: folder.id,
      name: folder.name,
      type: 'folder' as const,
      modifiedAt: new Date(folder.deletedAt || folder.modifiedAt),
      isShared: false
    })),
    ...trashContents.files.map(file => ({
      id: file.id,
      name: file.originalName || file.filename,
      type: 'file' as const,
      size: file.size,
      mimeType: file.mimeType,
      modifiedAt: new Date(file.deletedAt || file.modifiedAt),
      isShared: file.isPublic
    }))
  ] : []

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-secondary border border-current-line rounded-lg w-full max-w-4xl h-3/4 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-current-line">
          <div className="flex items-center space-x-4">
            <TrashIcon className="w-8 h-8 text-comment" />
            <div>
              <h2 className="text-xl font-semibold text-primary">
                {t('files.trash')}
              </h2>
              {trashContents && (
                <span className="text-sm text-comment">
                  {trashContents.totalCount} {trashContents.totalCount === 1 ? t('files.item') : t('files.items')}
                </span>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {selectedFiles.size > 0 && (
              <Button
                variant="outline"
                size="md"
                onClick={handleRestoreSelected}
                disabled={loading}
                className="px-4 py-2"
              >
                <ArrowPathIcon className="w-5 h-5 mr-2" />
                {t('files.restoreSelected')} ({selectedFiles.size})
              </Button>
            )}

            {trashItems.length > 0 && (
              <Button
                variant="danger"
                size="md"
                onClick={handleEmptyTrash}
                disabled={loading}
                className="px-4 py-2"
              >
                <TrashIcon className="w-5 h-5 mr-2" />
                {t('files.emptyTrash')}
              </Button>
            )}

            <Button
              variant="ghost"
              size="md"
              onClick={onClose}
              className="px-4 py-2"
            >
              {t('common.close')}
            </Button>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mx-4 mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded flex items-center">
            <ExclamationTriangleIcon className="w-5 h-5 mr-2" />
            {error}
            <button
              onClick={() => setError(null)}
              className="ml-auto text-red-500 hover:text-red-700"
            >
              ×
            </button>
          </div>
        )}

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          {loading && !trashContents ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto mb-4"></div>
                <p className="text-comment">{t('common.loading')}</p>
              </div>
            </div>
          ) : trashItems.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <TrashIcon className="w-16 h-16 text-comment mx-auto mb-4" />
                <p className="text-lg text-primary mb-2">{t('files.trashEmpty')}</p>
                <p className="text-comment">{t('files.trashEmptyDescription')}</p>
              </div>
            </div>
          ) : (
            <FileList
              files={trashItems}
              viewMode="list"
              selectedFiles={selectedFiles}
              onFileSelect={handleFileSelect}
              onSelectAll={handleSelectAll}
              onFolderOpen={() => {}} // Disabled in trash
              onContextMenu={(e, file) => {
                e.preventDefault()
                // Show restore context menu
                handleRestore(file.id)
              }}
              onFilePreview={() => {}} // Disabled in trash
              loading={loading}
              showRestoreAction={true}
              onRestore={handleRestore}
            />
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-current-line bg-current-line">
          <div className="flex items-center justify-between text-sm text-comment">
            <div>
              {t('files.trashRetentionNotice')}
            </div>
            <div>
              {selectedFiles.size > 0 && (
                <span>
                  {selectedFiles.size} {selectedFiles.size === 1 ? t('files.item') : t('files.items')} {t('files.selected')}
                </span>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TrashBin