import React from 'react'
import { FileItem } from '../../pages/Files'
import { cn } from '../../utils/cn'
import { useI18n } from '../../contexts/I18nContext'
import { CheckIcon } from '@heroicons/react/24/outline'
import ImageBedFolderIcon from '../icons/ImageBedFolderIcon'

interface FileListItemProps {
  file: FileItem
  selected: boolean
  onSelect: (selected: boolean) => void
  onOpen: () => void
  icon: React.ReactNode
  formattedSize: string
  formattedDate: string
}

const FileListItem: React.FC<FileListItemProps> = ({
  file,
  selected,
  onSelect,
  onOpen,
  icon,
  formattedSize,
  formattedDate
}) => {
  const { t } = useI18n()
  const handleClick = (e: React.MouseEvent) => {
    if (e.ctrlKey || e.metaKey) {
      onSelect(!selected)
    } else {
      onOpen()
    }
  }

  const handleCheckboxClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    // Prevent selecting image bed folder
    if (file.isImageBedFolder) {
      return
    }
    onSelect(!selected)
  }

  return (
    <div
      data-file-item="true"
      className={cn(
        'flex items-center px-4 py-3 hover-bg cursor-pointer transition-colors',
        selected && 'bg-purple-500/10'
      )}
      onClick={handleClick}
    >
      {/* Selection Checkbox - 图床文件夹不显示勾选框 */}
      {!file.isImageBedFolder && (
        <div className="flex items-center mr-4">
          <div
            className={cn(
              'w-5 h-5 rounded border-2 flex items-center justify-center transition-colors',
              selected
                ? 'bg-purple-500 border-purple-500'
                : 'border-comment hover:border-purple-500 cursor-pointer'
            )}
            onClick={handleCheckboxClick}
          >
            {selected && <CheckIcon className="w-3 h-3 text-white" />}
          </div>
        </div>
      )}

      {/* File Content */}
      <div className="flex-1 grid grid-cols-12 gap-4 items-center">
        {/* Name and Icon */}
        <div className={cn(
          "flex items-center space-x-3 min-w-0",
          file.isImageBedFolder ? "col-span-10" : "col-span-6"
        )}>
          <div className="flex-shrink-0">
            {file.thumbnailUrl && file.type !== 'folder' ? (
              <img
                src={file.thumbnailUrl}
                alt={file.name}
                className="w-8 h-8 object-cover rounded"
              />
            ) : file.isImageBedFolder ? (
              <ImageBedFolderIcon size="md" />
            ) : (
              icon
            )}
          </div>
          <span className="text-primary font-medium truncate">
            {file.name}
          </span>
        </div>

        {/* Size - 图床文件夹不显示 */}
        {!file.isImageBedFolder && (
          <div className="col-span-2 text-sm text-comment">
            {formattedSize}
          </div>
        )}

        {/* Type - 图床文件夹不显示 */}
        {!file.isImageBedFolder && (
          <div className="col-span-2 text-sm text-comment">
            {file.type === 'folder' ? t('files.folder') || 'Folder' : file.mimeType?.split('/')[0] || t('files.file') || 'File'}
          </div>
        )}

        {/* Modified Date - 图床文件夹不显示 */}
        {!file.isImageBedFolder && (
          <div className="col-span-2 text-sm text-comment">
            {formattedDate}
          </div>
        )}
      </div>
    </div>
  )
}

export default FileListItem