import React, { useState, useEffect } from 'react'
import { 
  <PERSON>hare2, 
  <PERSON><PERSON>, 
  <PERSON>, 
  Download, 
  Calendar, 
  Lock, 
  Unlock,
  MoreVertical,
  Trash2,
  Edit3,
  ExternalLink,
  AlertCircle,
  CheckCircle,
  RefreshCw
} from 'lucide-react'
import { useI18n } from '../contexts/I18nContext'
import { fileService } from '../services/fileService'
import { ShareLink } from '@cloud-storage/shared'

interface ShareStats {
  totalShares: number
  activeShares: number
  totalDownloads: number
  recentShares: Array<{
    id: string
    fileName: string
    createdAt: Date
    downloadCount: number
    isActive: boolean
  }>
  downloadsByDay: Array<{
    date: string
    downloads: number
  }>
}

interface ShareItem extends ShareLink {
  fileName: string
  fileSize: number
  fileMimeType: string
}

const ShareManagement: React.FC = () => {
  const { t } = useI18n()
  const [shares, setShares] = useState<ShareItem[]>([])
  const [stats, setStats] = useState<ShareStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedShare, setSelectedShare] = useState<ShareItem | null>(null)
  const [showEditModal, setShowEditModal] = useState(false)
  const [copiedToken, setCopiedToken] = useState<string | null>(null)

  useEffect(() => {
    loadShares()
    loadStats()
  }, [])

  const loadShares = async () => {
    try {
      setLoading(true)
      const userShares = await fileService.getUserShares()
      const sharesWithFileInfo: ShareItem[] = userShares.map(share => ({
        id: share.id,
        fileId: share.fileId,
        userId: '', // 不需要在前端显示
        token: share.token,
        fileName: share.fileName,
        fileSize: share.fileSize,
        fileMimeType: share.fileMimeType,
        permissions: share.permissions,
        downloadCount: share.downloadCount,
        maxDownloads: share.maxDownloads,
        createdAt: share.createdAt,
        expiresAt: share.expiresAt,
        password: share.password
      }))
      setShares(sharesWithFileInfo)
    } catch (error) {
      console.error('Failed to load shares:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const shareStats = await fileService.getComprehensiveShareStats()
      setStats(shareStats)
    } catch (error) {
      console.error('Failed to load share stats:', error)
    }
  }

  const copyShareLink = async (token: string) => {
    const shareUrl = `${window.location.origin}/share/${token}`
    try {
      await navigator.clipboard.writeText(shareUrl)
      setCopiedToken(token)
      setTimeout(() => setCopiedToken(null), 2000)
    } catch (error) {
      console.error('Failed to copy link:', error)
    }
  }

  const revokeShare = async (shareId: string) => {
    try {
      await fileService.revokeShareLink(shareId)
      await loadShares()
      await loadStats()
    } catch (error) {
      console.error('Failed to revoke share:', error)
    }
  }

  const isExpired = (share: ShareItem): boolean => {
    return share.expiresAt ? new Date() > new Date(share.expiresAt) : false
  }

  const isDownloadLimitReached = (share: ShareItem): boolean => {
    return share.maxDownloads ? share.downloadCount >= share.maxDownloads : false
  }

  const getShareStatus = (share: ShareItem): 'active' | 'expired' | 'limit_reached' => {
    if (isExpired(share)) return 'expired'
    if (isDownloadLimitReached(share)) return 'limit_reached'
    return 'active'
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDate = (date: Date): string => {
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date))
  }

  const getFileIcon = (mimeType: string): string => {
    if (mimeType.startsWith('image/')) return '🖼️'
    if (mimeType.startsWith('video/')) return '🎥'
    if (mimeType.startsWith('audio/')) return '🎵'
    if (mimeType.includes('pdf')) return '📄'
    if (mimeType.includes('word')) return '📝'
    if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return '📊'
    if (mimeType.includes('zip') || mimeType.includes('rar')) return '🗜️'
    return '📄'
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="w-8 h-8 animate-spin text-accent-purple" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-primary">{t('nav.shared') || '分享管理'}</h1>
          <p className="text-secondary mt-1">{t('shareStats.title') || '管理您的文件分享链接'}</p>
        </div>
        <button
          onClick={loadShares}
          className="flex items-center gap-2 px-4 py-2 bg-accent-purple text-white rounded-lg hover:bg-accent-purple/80 transition-colors"
        >
          <RefreshCw className="w-4 h-4" />
          刷新
        </button>
      </div>

      {/* 统计卡片 */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-secondary rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary text-sm">总分享数</p>
                <p className="text-2xl font-bold text-primary">{stats.totalShares}</p>
              </div>
              <Share2 className="w-8 h-8 text-accent-purple" />
            </div>
          </div>
          <div className="bg-secondary rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary text-sm">活跃分享</p>
                <p className="text-2xl font-bold text-primary">{stats.activeShares}</p>
              </div>
              <Eye className="w-8 h-8 text-accent-green" />
            </div>
          </div>
          <div className="bg-secondary rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-secondary text-sm">总下载次数</p>
                <p className="text-2xl font-bold text-primary">{stats.totalDownloads}</p>
              </div>
              <Download className="w-8 h-8 text-accent-cyan" />
            </div>
          </div>
        </div>
      )}

      {/* 分享列表 */}
      <div className="bg-secondary rounded-lg">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-primary">我的分享</h2>
        </div>
        
        {shares.length === 0 ? (
          <div className="p-12 text-center">
            <Share2 className="w-12 h-12 text-secondary mx-auto mb-4" />
            <p className="text-secondary">暂无分享文件</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {shares.map((share) => {
              const status = getShareStatus(share)
              return (
                <div key={share.id} className="p-6 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 flex-1">
                      <div className="text-2xl">{getFileIcon(share.fileMimeType)}</div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <h3 className="text-primary font-medium truncate">{share.fileName}</h3>
                          {share.password && <Lock className="w-4 h-4 text-accent-orange" />}
                          {status === 'active' && <CheckCircle className="w-4 h-4 text-accent-green" />}
                          {status === 'expired' && <AlertCircle className="w-4 h-4 text-accent-red" />}
                          {status === 'limit_reached' && <AlertCircle className="w-4 h-4 text-accent-orange" />}
                        </div>
                        <div className="flex items-center gap-4 mt-1 text-sm text-secondary">
                          <span>{formatFileSize(share.fileSize)}</span>
                          <span>下载 {share.downloadCount} 次</span>
                          {share.maxDownloads && (
                            <span>限制 {share.maxDownloads} 次</span>
                          )}
                          <span>创建于 {formatDate(share.createdAt)}</span>
                          {share.expiresAt && (
                            <span>过期于 {formatDate(share.expiresAt)}</span>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => copyShareLink(share.token)}
                        className="flex items-center gap-2 px-3 py-1.5 text-sm bg-accent-purple text-white rounded hover:bg-accent-purple/80 transition-colors"
                      >
                        {copiedToken === share.token ? (
                          <>
                            <CheckCircle className="w-4 h-4" />
                            已复制
                          </>
                        ) : (
                          <>
                            <Copy className="w-4 h-4" />
                            复制链接
                          </>
                        )}
                      </button>
                      
                      <button
                        onClick={() => window.open(`/share/${share.token}`, '_blank')}
                        className="p-2 text-secondary hover:text-primary hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
                        title="预览分享"
                      >
                        <ExternalLink className="w-4 h-4" />
                      </button>
                      
                      <button
                        onClick={() => {
                          setSelectedShare(share)
                          setShowEditModal(true)
                        }}
                        className="p-2 text-secondary hover:text-primary hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
                        title="编辑分享"
                      >
                        <Edit3 className="w-4 h-4" />
                      </button>
                      
                      <button
                        onClick={() => revokeShare(share.id)}
                        className="p-2 text-accent-red hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors"
                        title="撤销分享"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </div>

      {/* 编辑分享模态框 */}
      {showEditModal && selectedShare && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-secondary rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold text-primary mb-4">编辑分享设置</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-primary mb-2">
                  文件名
                </label>
                <p className="text-secondary">{selectedShare.fileName}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-primary mb-2">
                  分享链接
                </label>
                <div className="flex items-center gap-2">
                  <input
                    type="text"
                    value={`${window.location.origin}/share/${selectedShare.token}`}
                    readOnly
                    className="flex-1 px-3 py-2 bg-primary border border-gray-300 dark:border-gray-600 rounded text-sm"
                  />
                  <button
                    onClick={() => copyShareLink(selectedShare.token)}
                    className="p-2 text-accent-purple hover:bg-accent-purple/10 rounded"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-primary mb-2">
                  下载统计
                </label>
                <p className="text-secondary">
                  已下载 {selectedShare.downloadCount} 次
                  {selectedShare.maxDownloads && ` / ${selectedShare.maxDownloads} 次`}
                </p>
              </div>
              
              {selectedShare.expiresAt && (
                <div>
                  <label className="block text-sm font-medium text-primary mb-2">
                    过期时间
                  </label>
                  <p className="text-secondary">{formatDate(selectedShare.expiresAt)}</p>
                </div>
              )}
            </div>
            
            <div className="flex justify-end gap-3 mt-6">
              <button
                onClick={() => setShowEditModal(false)}
                className="px-4 py-2 text-secondary hover:text-primary transition-colors"
              >
                关闭
              </button>
              <button
                onClick={() => revokeShare(selectedShare.id)}
                className="px-4 py-2 bg-accent-red text-white rounded hover:bg-accent-red/80 transition-colors"
              >
                撤销分享
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ShareManagement