import { Collection, ObjectId, WithId } from 'mongodb'
import { MongoDB } from '../config/mongodb'
import { Folder } from '@cloud-storage/shared'

export interface FolderDocument extends Omit<Folder, 'id'> {
  _id?: ObjectId
  folderId?: string  // Store the original UUID here
  isDeleted?: boolean
  deletedAt?: Date
}

export class FolderModel {
  private collection: Collection<FolderDocument>

  constructor(mongodb: MongoDB) {
    this.collection = mongodb.getCollection<FolderDocument>('folders')
  }

  async create(folder: Folder): Promise<Folder> {
    const document: FolderDocument = {
      ...folder,
      folderId: folder.id,  // Store the UUID in folderId field
      createdAt: new Date(folder.createdAt),
      modifiedAt: new Date(folder.modifiedAt)
    }
    // Remove the id field since MongoDB will generate _id
    delete (document as any).id

    const result = await this.collection.insertOne(document)

    return {
      ...folder,
      id: folder.id  // Return the original UUID
    }
  }

  async findById(id: string): Promise<Folder | null> {
    try {
      // Try to find by folderId (UUID) first
      let document = await this.collection.findOne({ folderId: id })

      // If not found and id looks like ObjectId, try finding by _id
      if (!document && /^[0-9a-fA-F]{24}$/.test(id)) {
        document = await this.collection.findOne({ _id: new ObjectId(id) })
      }

      return document ? this.documentToFolder(document) : null
    } catch (error) {
      return null
    }
  }

  async findByUserId(userId: string, parentId?: string): Promise<Folder[]> {
    const filter: any = { userId, isDeleted: { $ne: true } }

    // When parentId is undefined, we want root folders (parentId is null)
    // When parentId is provided, we want folders in that specific parent folder
    if (parentId === undefined) {
      filter.parentId = null
    } else {
      filter.parentId = parentId
    }

    const documents = await this.collection.find(filter).sort({ name: 1 }).toArray()
    return documents.map(doc => this.documentToFolder(doc))
  }

  async findByPath(userId: string, path: string): Promise<Folder | null> {
    const document = await this.collection.findOne({ userId, path })
    return document ? this.documentToFolder(document) : null
  }

  async findChildren(userId: string, parentId: string): Promise<Folder[]> {
    const documents = await this.collection.find({ 
      userId, 
      parentId, 
      isDeleted: { $ne: true } 
    }).sort({ name: 1 }).toArray()
    return documents.map(doc => this.documentToFolder(doc))
  }

  async update(id: string, updates: Partial<Omit<Folder, 'id'>>): Promise<boolean> {
    try {
      const updateDoc: any = { ...updates }
      if (updates.modifiedAt) {
        updateDoc.modifiedAt = new Date(updates.modifiedAt)
      }

      // Try to update by folderId (UUID) first
      let result = await this.collection.updateOne(
        { folderId: id },
        { $set: updateDoc }
      )

      // If not found and id looks like ObjectId, try updating by _id
      if (result.matchedCount === 0 && /^[0-9a-fA-F]{24}$/.test(id)) {
        result = await this.collection.updateOne(
          { _id: new ObjectId(id) },
          { $set: updateDoc }
        )
      }

      return result.modifiedCount > 0
    } catch (error) {
      return false
    }
  }

  async delete(id: string): Promise<boolean> {
    try {
      // Try to delete by folderId (UUID) first
      let result = await this.collection.deleteOne({ folderId: id })

      // If not found and id looks like ObjectId, try deleting by _id
      if (result.deletedCount === 0 && /^[0-9a-fA-F]{24}$/.test(id)) {
        result = await this.collection.deleteOne({ _id: new ObjectId(id) })
      }

      return result.deletedCount > 0
    } catch (error) {
      return false
    }
  }

  async move(id: string, newParentId: string | undefined, newPath: string): Promise<boolean> {
    try {
      // Try to update by folderId (UUID) first
      let result = await this.collection.updateOne(
        { folderId: id },
        {
          $set: {
            parentId: newParentId,
            path: newPath,
            modifiedAt: new Date()
          }
        }
      )

      // If not found and id looks like ObjectId, try updating by _id
      if (result.matchedCount === 0 && /^[0-9a-fA-F]{24}$/.test(id)) {
        result = await this.collection.updateOne(
          { _id: new ObjectId(id) },
          {
            $set: {
              parentId: newParentId,
              path: newPath,
              modifiedAt: new Date()
            }
          }
        )
      }

      return result.modifiedCount > 0
    } catch (error) {
      return false
    }
  }

  async rename(id: string, newName: string): Promise<boolean> {
    try {
      // First get the current folder to update the path
      const folder = await this.findById(id)
      if (!folder) return false

      // Calculate new path
      const pathParts = folder.path.split('/')
      pathParts[pathParts.length - 1] = newName
      const newPath = pathParts.join('/')

      // Update the folder itself - use consistent query logic like other methods
      let result = await this.collection.updateOne(
        { folderId: id },
        { 
          $set: { 
            name: newName,
            path: newPath,
            modifiedAt: new Date()
          }
        }
      )

      // If not found and id looks like ObjectId, try updating by _id
      if (result.matchedCount === 0 && /^[0-9a-fA-F]{24}$/.test(id)) {
        result = await this.collection.updateOne(
          { _id: new ObjectId(id) },
          { 
            $set: { 
              name: newName,
              path: newPath,
              modifiedAt: new Date()
            }
          }
        )
      }

      // If the folder was successfully renamed, update all child folders' paths
      if (result.modifiedCount > 0) {
        // Find all child folders that need path updates
        const childFolders = await this.collection.find({
          path: { $regex: `^${folder.path.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}/` }
        }).toArray()

        // Update each child folder's path
        for (const child of childFolders) {
          const updatedChildPath = child.path.replace(folder.path, newPath)
          await this.collection.updateOne(
            { _id: child._id },
            { 
              $set: { 
                path: updatedChildPath,
                modifiedAt: new Date()
              }
            }
          )
        }
      }

      return result.modifiedCount > 0
    } catch (error) {
      return false
    }
  }

  async getFolderTree(userId: string, rootId?: string): Promise<Folder[]> {
    const pipeline = [
      { $match: { userId, parentId: rootId || null } },
      {
        $graphLookup: {
          from: 'folders',
          startWith: '$_id',
          connectFromField: '_id',
          connectToField: 'parentId',
          as: 'children',
          restrictSearchWithMatch: { userId }
        }
      },
      { $sort: { name: 1 } }
    ]

    const documents = await this.collection.aggregate(pipeline).toArray()
    return documents.map(doc => this.documentToFolder(doc as WithId<FolderDocument>))
  }

  async validatePath(userId: string, parentId: string | undefined, name: string): Promise<boolean> {
    const filter: any = {
      userId,
      name,
      isDeleted: { $ne: true }
    };
    
    if (parentId) {
      filter.parentId = parentId;
    } else {
      filter.parentId = { $exists: false };
    }
    
    const existingFolder = await this.collection.findOne(filter)
    return !existingFolder
  }

  async findDeleted(userId: string): Promise<Folder[]> {
    const documents = await this.collection.find({
      userId,
      isDeleted: true
    }).sort({ deletedAt: -1 }).toArray()
    
    return documents.map(doc => this.documentToFolder(doc))
  }

  async restore(id: string): Promise<boolean> {
    try {
      const result = await this.collection.updateOne(
        { _id: new ObjectId(id) },
        { 
          $unset: { isDeleted: "", deletedAt: "" },
          $set: { modifiedAt: new Date() }
        }
      )
      return result.modifiedCount > 0
    } catch (error) {
      return false
    }
  }

  async buildPath(userId: string, parentId?: string): Promise<string> {
    if (!parentId) return '/'

    const parent = await this.findById(parentId)
    if (!parent) return '/'

    return parent.path === '/' ? `/${parent.name}` : `${parent.path}/${parent.name}`
  }

  private documentToFolder(document: WithId<FolderDocument>): Folder {
    return {
      id: document.folderId || document._id.toString(), // Use folderId if available, fallback to _id
      userId: document.userId,
      name: document.name,
      parentId: document.parentId,
      path: document.path,
      createdAt: document.createdAt,
      modifiedAt: document.modifiedAt,
      isDeleted: document.isDeleted,
      deletedAt: document.deletedAt
    }
  }
}