import React from 'react'
import { FileItem } from '../../pages/Files'
import FileGridItem from './FileGridItem'
import FileListItem from './FileListItem'
import Spinner from '../ui/Spinner'
import { useI18n } from '../../contexts/I18nContext'
import { cn } from '../../utils/cn'
import { 
  DocumentIcon,
  FolderIcon,
  PhotoIcon,
  VideoCameraIcon,
  MusicalNoteIcon,
  ArchiveBoxIcon
} from '@heroicons/react/24/outline'
import ImageBedFolderIcon from '../icons/ImageBedFolderIcon'

interface FileListProps {
  files: FileItem[]
  viewMode: 'grid' | 'list'
  selectedFiles: Set<string>
  onFileSelect: (fileId: string, selected: boolean) => void
  onSelectAll: () => void
  onFolderOpen: (folderId: string, folderName: string) => void
  onContextMenu?: (e: React.MouseEvent, file: FileItem) => void
  onFilePreview?: (file: FileItem) => void
  loading?: boolean
  showRestoreAction?: boolean
  onRestore?: (fileId: string) => void
  onFileDrop?: (fileId: string, targetFolderId: string) => void
  isImageBedFolder?: boolean
}

const FileList: React.FC<FileListProps> = ({
  files,
  viewMode,
  selectedFiles,
  onFileSelect,
  onSelectAll,
  onFolderOpen,
  onContextMenu,
  onFilePreview,
  loading = false,
  showRestoreAction = false,
  onRestore,
  onFileDrop,
  isImageBedFolder = false
}) => {
  const { t, language } = useI18n()

  // Drag and drop handlers
  const handleDragStart = (e: React.DragEvent, file: FileItem) => {
    // Prevent dragging image bed folder and its contents
    if (file.isImageBedFolder || isImageBedFolder) {
      e.preventDefault()
      return
    }

    // Allow dragging both files and folders
    e.dataTransfer.setData('text/plain', file.id)
    e.dataTransfer.setData('application/json', JSON.stringify({
      id: file.id,
      type: file.type,
      isImageBedItem: file.isImageBedFolder || isImageBedFolder // Mark if this is from image bed
    }))
    e.dataTransfer.effectAllowed = 'move'

    // Add visual feedback
    const target = e.currentTarget as HTMLElement
    target.style.opacity = '0.7'
  }

  const handleDragEnd = (e: React.DragEvent) => {
    // Reset visual feedback
    const target = e.currentTarget as HTMLElement
    target.style.opacity = '1'
  }

  const handleDragOver = (e: React.DragEvent, file: FileItem) => {
    if (file.type !== 'folder') return // Only allow dropping on folders
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'

    // Add visual feedback
    const target = e.currentTarget as HTMLElement
    target.classList.add('drag-over')
  }

  const handleDragLeave = (e: React.DragEvent) => {
    // Remove visual feedback
    const target = e.currentTarget as HTMLElement
    target.classList.remove('drag-over')
  }

  const handleDrop = (e: React.DragEvent, targetFolder: FileItem) => {
    if (targetFolder.type !== 'folder') return
    
    // Prevent dropping into image bed folder (except images)
    if (targetFolder.isImageBedFolder) {
      const draggedData = e.dataTransfer.getData('application/json')
      try {
        const draggedItem = JSON.parse(draggedData)
        // Only allow image files to be dropped into image bed folder
        if (draggedItem.type !== 'file') {
          e.preventDefault()
          return
        }
        // Additional check for image type would need file metadata
      } catch (error) {
        e.preventDefault()
        return
      }
    }
    
    e.preventDefault()

    // Remove visual feedback
    const target = e.currentTarget as HTMLElement
    target.classList.remove('drag-over')

    const fileId = e.dataTransfer.getData('text/plain')
    if (fileId && onFileDrop) {
      onFileDrop(fileId, targetFolder.id)
    }
  }
  const getFileIcon = (file: FileItem) => {
    if (file.type === 'folder') {
      if (file.isImageBedFolder) {
        return <ImageBedFolderIcon size="md" />
      }
      return <FolderIcon className="w-6 h-6 text-cyan-500" />
    }

    if (!file.mimeType) {
      return <DocumentIcon className="w-6 h-6 text-comment" />
    }

    if (file.mimeType.startsWith('image/')) {
      return <PhotoIcon className="w-6 h-6 text-green-500" />
    }

    if (file.mimeType.startsWith('video/')) {
      return <VideoCameraIcon className="w-6 h-6 text-red-500" />
    }

    if (file.mimeType.startsWith('audio/')) {
      return <MusicalNoteIcon className="w-6 h-6 text-purple-500" />
    }

    if (file.mimeType.includes('zip') || file.mimeType.includes('rar') || file.mimeType.includes('tar')) {
      return <ArchiveBoxIcon className="w-6 h-6 text-orange-500" />
    }

    return <DocumentIcon className="w-6 h-6 text-comment" />
  }

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return ''
    
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat(language === 'zh-CN' ? 'zh-CN' : 'en-US', {
      year: 'numeric',
      month: language === 'zh-CN' ? 'numeric' : 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      ...(language === 'zh-CN' && {
        hour12: false
      })
    }).format(date)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spinner size="lg" />
      </div>
    )
  }

  if (files.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-comment">
        <FolderIcon className="w-16 h-16 mb-4" />
        <p className="text-lg font-medium">{t('files.noFiles')}</p>
        <p className="text-sm">{t('files.uploadToGetStarted') || 'Upload files or create folders to get started'}</p>
      </div>
    )
  }

  if (viewMode === 'list') {
    return (
      <div className="flex flex-col h-full">
        {/* List Header */}
        <div className="flex-shrink-0 file-list-header">
          <div className="flex items-center px-4 py-3 text-sm font-medium text-comment">
            <div className="flex items-center mr-4">
              <input
                type="checkbox"
                checked={selectedFiles.size === files.length && files.length > 0}
                onChange={onSelectAll}
                className="rounded border-comment text-purple-500 focus:ring-purple-500"
              />
            </div>
            <div className="flex-1 grid grid-cols-12 gap-4">
              <div className="col-span-6">{t('files.name')}</div>
              <div className="col-span-2">{t('files.size')}</div>
              <div className="col-span-2">{t('files.type')}</div>
              <div className="col-span-2">{t('files.modified')}</div>
            </div>
          </div>
        </div>

        {/* List Items */}
        <div className="flex-1 overflow-y-auto">
          {files.map((file) => (
            <div
              key={file.id}
              data-file-item="true"
              draggable={true}
              onDragStart={(e) => handleDragStart(e, file)}
              onDragEnd={handleDragEnd}
              onDragOver={(e) => handleDragOver(e, file)}
              onDragLeave={handleDragLeave}
              onDrop={(e) => handleDrop(e, file)}
              onContextMenu={(e) => onContextMenu && onContextMenu(e, file)}
              className={file.type === 'folder' ? 'drop-zone' : ''}
            >
              <FileListItem
                file={file}
                selected={selectedFiles.has(file.id)}
                onSelect={(selected) => onFileSelect(file.id, selected)}
                onOpen={() => {
                  if (file.type === 'folder') {
                    onFolderOpen(file.id, file.name)
                  } else if (onFilePreview) {
                    onFilePreview(file)
                  }
                }}
                icon={getFileIcon(file)}
                formattedSize={formatFileSize(file.size)}
                formattedDate={formatDate(file.modifiedAt)}
              />
            </div>
          ))}
        </div>
      </div>
    )
  }

  // Grid view
  return (
    <div className="h-full overflow-y-auto">
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 p-4 auto-rows-fr">
        {files.map((file) => (
          <div
            key={file.id}
            data-file-item="true"
            draggable={true}
            onDragStart={(e) => handleDragStart(e, file)}
            onDragEnd={handleDragEnd}
            onDragOver={(e) => handleDragOver(e, file)}
            onDragLeave={handleDragLeave}
            onDrop={(e) => handleDrop(e, file)}
            onContextMenu={(e) => onContextMenu && onContextMenu(e, file)}
            className={cn(
              'h-36 flex flex-col',
              file.type === 'folder' ? 'drop-zone' : ''
            )}
          >
            <FileGridItem
              file={file}
              selected={selectedFiles.has(file.id)}
              onSelect={(selected) => onFileSelect(file.id, selected)}
              onOpen={() => {
                if (file.type === 'folder') {
                  onFolderOpen(file.id, file.name)
                } else if (onFilePreview) {
                  onFilePreview(file)
                }
              }}
              icon={getFileIcon(file)}
              formattedSize={formatFileSize(file.size)}
            />
          </div>
        ))}
      </div>
    </div>
  )
}

export default FileList