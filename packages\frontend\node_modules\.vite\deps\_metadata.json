{"hash": "8ada1056", "browserHash": "11033705", "optimized": {"react": {"src": "../../../../../node_modules/react/index.js", "file": "react.js", "fileHash": "ade2e6de", "needsInterop": true}, "react-dom": {"src": "../../../../../node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "ab9e2856", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../../../../node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "aa234971", "needsInterop": true}, "react/jsx-runtime": {"src": "../../../../../node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "9a65b808", "needsInterop": true}, "@heroicons/react/24/outline": {"src": "../../../../../node_modules/@heroicons/react/24/outline/esm/index.js", "file": "@heroicons_react_24_outline.js", "fileHash": "41990c04", "needsInterop": false}, "@heroicons/react/24/solid": {"src": "../../../../../node_modules/@heroicons/react/24/solid/esm/index.js", "file": "@heroicons_react_24_solid.js", "fileHash": "da2812dd", "needsInterop": false}, "clsx": {"src": "../../../../../node_modules/clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "30ad4743", "needsInterop": false}, "lucide-react": {"src": "../../../../../node_modules/lucide-react/dist/esm/lucide-react.mjs", "file": "lucide-react.js", "fileHash": "75c1d59b", "needsInterop": false}, "react-dom/client": {"src": "../../../../../node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "b402dfa7", "needsInterop": true}, "react-dropzone": {"src": "../../../../../node_modules/react-dropzone/dist/es/index.js", "file": "react-dropzone.js", "fileHash": "56756cb1", "needsInterop": false}, "react-router-dom": {"src": "../../../../../node_modules/react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "97fc0bd2", "needsInterop": false}, "tailwind-merge": {"src": "../../../../../node_modules/tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "6f59aba8", "needsInterop": false}}, "chunks": {"chunk-OC434Q3S": {"file": "chunk-OC434Q3S.js"}, "chunk-2DCM46BG": {"file": "chunk-2DCM46BG.js"}, "chunk-5WWUZCGV": {"file": "chunk-5WWUZCGV.js"}}}