import React, { useEffect } from 'react'
import { useAuthStore, useFileStore } from '../stores'
import { useI18n } from '../contexts/I18nContext'

const FilesSimple: React.FC = () => {
  const { isAuthenticated } = useAuthStore()
  const { t } = useI18n()
  
  const {
    files,
    currentFolder,
    folderPath,
    selectedFiles,
    viewMode,
    searchQuery,
    isLoading,
    error,
    loadFiles,
    setViewMode,
    setSearchQuery,
    toggleFileSelection,
    selectAllFiles,
    clearSelection,
    clearError
  } = useFileStore()

  // Load files when component mounts or when authentication status changes
  useEffect(() => {
    if (isAuthenticated) {
      loadFiles()
    }
  }, [isAuthenticated, loadFiles])

  // Handle file selection
  const handleFileSelect = (fileId: string, selected: boolean) => {
    toggleFileSelection(fileId)
  }

  // Handle folder navigation
  const handleFolderOpen = (folderId: string, folderName: string) => {
    loadFiles(folderId)
  }

  if (!isAuthenticated) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500">{t('files.notAuthenticated')}</p>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          {t('files.title')}
        </h1>
        
        {/* Breadcrumb */}
        <nav className="flex mb-4" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            {folderPath.map((folder, index) => (
              <li key={folder.id} className="inline-flex items-center">
                {index > 0 && (
                  <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  </svg>
                )}
                <span className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-400 dark:hover:text-white">
                  {folder.name}
                </span>
              </li>
            ))}
          </ol>
        </nav>

        {/* Controls */}
        <div className="flex flex-col sm:flex-row gap-4 mb-4">
          {/* Search */}
          <div className="flex-1">
            <input
              type="text"
              placeholder={t('files.search')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>

          {/* View Mode Toggle */}
          <div className="flex rounded-md shadow-sm">
            <button
              onClick={() => setViewMode('grid')}
              className={`px-4 py-2 text-sm font-medium border ${
                viewMode === 'grid'
                  ? 'bg-blue-50 border-blue-500 text-blue-700'
                  : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
              } rounded-l-md`}
            >
              Grid
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`px-4 py-2 text-sm font-medium border-t border-r border-b ${
                viewMode === 'list'
                  ? 'bg-blue-50 border-blue-500 text-blue-700'
                  : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
              } rounded-r-md`}
            >
              List
            </button>
          </div>
        </div>

        {/* Selection Controls */}
        {selectedFiles.size > 0 && (
          <div className="flex items-center gap-4 mb-4 p-3 bg-blue-50 rounded-md">
            <span className="text-sm text-blue-700">
              {selectedFiles.size} {t('files.selected')}
            </span>
            <button
              onClick={clearSelection}
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              {t('files.clearSelection')}
            </button>
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
              <button
                onClick={clearError}
                className="mt-2 text-sm text-red-600 hover:text-red-800"
              >
                {t('common.dismiss')}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
        </div>
      )}

      {/* Files List */}
      {!isLoading && (
        <div className={viewMode === 'grid' ? 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4' : 'space-y-2'}>
          {files.length === 0 ? (
            <div className="col-span-full text-center py-12">
              <p className="text-gray-500">{t('files.empty')}</p>
            </div>
          ) : (
            files.map((file) => (
              <div
                key={file.id}
                className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                  selectedFiles.has(file.id)
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                } ${viewMode === 'list' ? 'flex items-center space-x-4' : ''}`}
                onClick={() => {
                  if (file.type === 'folder') {
                    handleFolderOpen(file.id, file.name)
                  } else {
                    handleFileSelect(file.id, !selectedFiles.has(file.id))
                  }
                }}
              >
                <div className={`flex-shrink-0 ${viewMode === 'grid' ? 'mb-2' : ''}`}>
                  {file.type === 'folder' ? (
                    <svg className="w-8 h-8 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" />
                    </svg>
                  ) : (
                    <svg className="w-8 h-8 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">{file.name}</p>
                  <p className="text-sm text-gray-500">
                    {file.type === 'folder' ? t('files.folder') : `${file.size ? Math.round(file.size / 1024) : 0} KB`}
                  </p>
                </div>
                {selectedFiles.has(file.id) && (
                  <div className="flex-shrink-0">
                    <svg className="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      )}

      {/* Debug Info */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-8 p-4 bg-gray-100 rounded-lg">
          <h3 className="text-sm font-semibold mb-2">Debug Info:</h3>
          <pre className="text-xs text-gray-600">
            {JSON.stringify({
              filesCount: files.length,
              currentFolder,
              selectedCount: selectedFiles.size,
              viewMode,
              searchQuery,
              isLoading,
              error: error ? error.substring(0, 100) : null
            }, null, 2)}
          </pre>
        </div>
      )}
    </div>
  )
}

export default FilesSimple
