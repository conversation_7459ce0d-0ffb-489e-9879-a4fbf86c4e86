.App {
  min-height: 100vh;
  height: 100vh;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  overflow: hidden;
}

/* Ensure html and body take full height */
html, body, #root {
  height: 100%;
  margin: 0;
  padding: 0;
}

/* Loading spinner */
.loading-spinner {
  border: 2px solid var(--bg-secondary);
  border-top: 2px solid var(--accent-purple);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Button styles */
.btn-primary {
  background-color: var(--accent-purple);
  color: var(--text-primary);
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background-color: var(--accent-pink);
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--text-secondary);
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background-color: var(--text-secondary);
  color: var(--bg-primary);
}

/* Form styles */
.form-input {
  background-color: var(--bg-secondary);
  border: 1px solid var(--text-secondary);
  color: var(--text-primary);
  padding: 0.75rem;
  border-radius: 0.375rem;
  width: 100%;
  transition: border-color 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--accent-purple);
  box-shadow: 0 0 0 2px rgba(189, 147, 249, 0.2);
}

.form-input::placeholder {
  color: var(--text-secondary);
}

/* Card styles */
.card {
  background-color: var(--bg-secondary);
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}