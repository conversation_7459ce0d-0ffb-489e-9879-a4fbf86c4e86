import React from 'react'
import {
  FolderPlusIcon,
  CloudArrowUpIcon
} from '@heroicons/react/24/outline'
import { useI18n } from '../../contexts/I18nContext'
import { useTheme } from '../../contexts/ThemeContext'
import { cn } from '../../utils/cn'

interface BackgroundContextMenuProps {
  position: { x: number; y: number }
  onClose: () => void
  onAction: (action: string) => void
  isImageBedFolder?: boolean
}

const BackgroundContextMenu: React.FC<BackgroundContextMenuProps> = ({
  position,
  onClose,
  onAction,
  isImageBedFolder = false
}) => {
  const { t } = useI18n()
  const { theme } = useTheme()

  const menuItems = isImageBedFolder ? [
    {
      id: 'uploadFiles',
      label: t('files.uploadToImageBed'),
      icon: CloudArrowUpIcon,
      show: true
    }
  ] : [
    {
      id: 'createFolder',
      label: t('files.newFolder'),
      icon: FolderPlusIcon,
      show: true
    },
    {
      id: 'uploadFiles',
      label: t('files.uploadFiles'),
      icon: CloudArrowUpIcon,
      show: true
    }
  ]

  const handleItemClick = (actionId: string) => {
    onAction(actionId)
    onClose()
  }

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 z-40"
        onClick={onClose}
      />
      
      {/* Menu */}
      <div
        data-context-menu="true"
        className={cn(
          "fixed z-50 rounded-lg shadow-xl py-2 min-w-52 backdrop-blur-sm",
          "border transition-all duration-200 ease-out",
          "animate-in fade-in-0 zoom-in-95 slide-in-from-left-1 slide-in-from-top-1",
          theme === 'dark'
            ? "bg-gray-900/95 border-gray-700/50 shadow-black/50"
            : "bg-white/95 border-gray-200/50 shadow-gray-900/20"
        )}
        style={{
          left: position.x,
          top: position.y
        }}
      >
        {menuItems.map((item) => {
          const Icon = item.icon
          return (
            <button
              key={item.id}
              onClick={() => handleItemClick(item.id)}
              className={cn(
                'w-full flex items-center px-4 py-2.5 text-sm font-medium transition-all duration-150',
                'hover:scale-[1.02] active:scale-[0.98]',
                'first:rounded-t-md last:rounded-b-md',
                theme === 'dark'
                  ? 'text-gray-200 hover:bg-gray-800/80 hover:text-white'
                  : 'text-gray-700 hover:bg-gray-100/80 hover:text-gray-900'
              )}
            >
              <Icon className="w-4 h-4 mr-3 flex-shrink-0" />
              <span className="flex-1 text-left">{item.label}</span>
            </button>
          )
        })}
      </div>
    </>
  )
}

export default BackgroundContextMenu
