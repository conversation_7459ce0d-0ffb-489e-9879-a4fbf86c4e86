import React from 'react'
import { useTheme } from '../../contexts/ThemeContext'

interface ImageBedFolderIconProps {
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

const ImageBedFolderIcon: React.FC<ImageBedFolderIconProps> = ({
  className = '',
  size = 'md'
}) => {
  const { theme } = useTheme()

  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  }

  // 如果提供了自定义 className，优先使用，否则使用预设尺寸
  const finalClassName = className.includes('w-') || className.includes('h-')
    ? className
    : `${sizeClasses[size]} ${className}`

  const gradientId = `imageBedGradient-${Math.random().toString(36).substr(2, 9)}`
  const shadowId = `imageBedShadow-${Math.random().toString(36).substr(2, 9)}`

  return (
    <svg
      className={finalClassName}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        {/* 渐变定义 */}
        <linearGradient id={gradientId} x1="0%" y1="0%" x2="100%" y2="100%">
          {theme === 'dark' ? (
            <>
              <stop offset="0%" stopColor="#8B5CF6" />
              <stop offset="50%" stopColor="#A855F7" />
              <stop offset="100%" stopColor="#C084FC" />
            </>
          ) : (
            <>
              <stop offset="0%" stopColor="#7C3AED" />
              <stop offset="50%" stopColor="#8B5CF6" />
              <stop offset="100%" stopColor="#A855F7" />
            </>
          )}
        </linearGradient>
        
        {/* 阴影滤镜 */}
        <filter id={shadowId} x="-20%" y="-20%" width="140%" height="140%">
          <feDropShadow 
            dx="0" 
            dy="1" 
            stdDeviation="1" 
            floodColor={theme === 'dark' ? '#000000' : '#6B7280'} 
            floodOpacity="0.3"
          />
        </filter>
      </defs>
      
      {/* 文件夹主体 */}
      <path
        d="M3 7V19C3 20.1046 3.89543 21 5 21H19C20.1046 21 21 20.1046 21 19V9C21 7.89543 20.1046 7 19 7H12L10 5H5C3.89543 5 3 5.89543 3 7Z"
        fill={`url(#${gradientId})`}
        filter={`url(#${shadowId})`}
        stroke={theme === 'dark' ? '#A855F7' : '#7C3AED'}
        strokeWidth="0.5"
      />
      
      {/* 图片图标 */}
      <g transform="translate(7, 10)">
        {/* 图片框架 */}
        <rect
          x="0"
          y="0"
          width="10"
          height="7"
          rx="1"
          fill={theme === 'dark' ? '#1F2937' : '#FFFFFF'}
          stroke={theme === 'dark' ? '#374151' : '#E5E7EB'}
          strokeWidth="0.5"
        />
        
        {/* 山峰图标 */}
        <path
          d="M2 5L4 3L6 4.5L8 2.5L10 4V6H2V5Z"
          fill={theme === 'dark' ? '#6B7280' : '#9CA3AF'}
        />
        
        {/* 太阳/光点 */}
        <circle
          cx="3"
          cy="2.5"
          r="0.8"
          fill={theme === 'dark' ? '#FCD34D' : '#F59E0B'}
        />
      </g>
      
      {/* 云朵图标 (表示云存储) */}
      <g transform="translate(15, 8)">
        <path
          d="M3.5 1.5C2.67 1.5 2 2.17 2 3C2 3.83 2.67 4.5 3.5 4.5H5.5C6.33 4.5 7 3.83 7 3C7 2.17 6.33 1.5 5.5 1.5C5.22 0.64 4.39 0 3.5 0C2.61 0 1.78 0.64 1.5 1.5C0.67 1.5 0 2.17 0 3C0 3.83 0.67 4.5 1.5 4.5H3.5Z"
          fill={theme === 'dark' ? '#60A5FA' : '#3B82F6'}
          transform="scale(0.6)"
        />
      </g>
    </svg>
  )
}

export default ImageBedFolderIcon
