import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { Upload, Folder, Image, FileText, HardDrive, Shield, Monitor } from 'lucide-react'
import { useAuthStore } from '../stores'
import { useI18n } from '../contexts/I18nContext'

const Dashboard: React.FC = () => {
  const { user } = useAuthStore()
  const { t } = useI18n()

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold text-primary">{t('dashboard.title')}</h1>
          {user && (
            <p className="text-comment">{t('dashboard.welcome', { username: user.username })}</p>
          )}
        </div>
        {/* <div className="flex items-center space-x-3">
          <button className="btn-primary flex items-center space-x-2 w-full sm:w-auto px-4 py-2">
            <Upload className="w-4 h-4" />
            <span>{t('files.uploadFiles')}</span>
          </button>
        </div> */}
      </div>

      {/* Storage Usage */}
      <div className="bg-secondary p-6 rounded-lg shadow border border-primary">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg md:text-xl font-semibold text-primary">{t('dashboard.storageUsage')}</h2>
          <HardDrive className="w-6 h-6 text-comment" />
        </div>
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-comment">{t('dashboard.used')}</span>
            <span className="text-primary">2.5 GB / 5 GB</span>
          </div>
          <div className="w-full bg-current-line rounded-full h-2">
            <div
              className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-300"
              style={{ width: '50%' }}
            ></div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Link to="/files">
          <div className="bg-secondary p-6 rounded-lg shadow border border-primary hover-bg cursor-pointer transition-colors">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-purple-500 rounded-lg flex-shrink-0">
                <Folder className="w-6 h-6 text-white" />
              </div>
              <div className="min-w-0">
                <h3 className="font-semibold text-primary truncate">{t('dashboard.myFiles')}</h3>
                <p className="text-comment text-sm truncate">{t('dashboard.browseFiles')}</p>
              </div>
            </div>
          </div>
        </Link>

        <div className="bg-secondary p-6 rounded-lg shadow border border-primary hover-bg cursor-pointer transition-colors">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-pink-500 rounded-lg flex-shrink-0">
              <Image className="w-6 h-6 text-white" />
            </div>
            <div className="min-w-0">
              <h3 className="font-semibold text-primary truncate">{t('dashboard.imageGallery')}</h3>
              <p className="text-comment text-sm truncate">{t('dashboard.viewImages')}</p>
            </div>
          </div>
        </div>

        <div className="bg-secondary p-6 rounded-lg shadow border border-primary hover-bg cursor-pointer transition-colors">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-green-500 rounded-lg flex-shrink-0">
              <FileText className="w-6 h-6 text-white" />
            </div>
            <div className="min-w-0">
              <h3 className="font-semibold text-primary truncate">{t('dashboard.documents')}</h3>
              <p className="text-comment text-sm truncate">{t('dashboard.accessDocuments')}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Security Settings */}
      <div className="bg-secondary p-6 rounded-lg shadow border border-primary">
        <h2 className="text-lg md:text-xl font-semibold text-primary mb-4">{t('dashboard.securitySettings')}</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Link to="/two-factor-setup">
            <div className="bg-primary p-4 rounded-lg shadow border border-primary hover-bg cursor-pointer h-full transition-colors">
              <div className="flex items-center space-x-4">
                <div className="p-3 bg-orange-500 rounded-lg flex-shrink-0">
                  <Shield className="w-6 h-6 text-white" />
                </div>
                <div className="min-w-0">
                  <h3 className="font-semibold text-primary truncate">
                    {t('dashboard.twoFactorAuth')}
                  </h3>
                  <p className="text-comment text-sm truncate">
                    {user?.twoFactorEnabled ? t('dashboard.manage2FA') : t('dashboard.enable2FA')}
                  </p>
                  {user?.twoFactorEnabled && (
                    <span className="inline-block mt-1 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                      {t('dashboard.enabled')}
                    </span>
                  )}
                </div>
              </div>
            </div>
          </Link>

          <Link to="/session-management">
            <div className="bg-primary p-4 rounded-lg shadow border border-primary hover-bg cursor-pointer h-full transition-colors">
              <div className="flex items-center space-x-4">
                <div className="p-3 bg-cyan-500 rounded-lg flex-shrink-0">
                  <Monitor className="w-6 h-6 text-white" />
                </div>
                <div className="min-w-0">
                  <h3 className="font-semibold text-primary truncate">{t('dashboard.sessionManagement')}</h3>
                  <p className="text-comment text-sm truncate">{t('dashboard.manageSessions')}</p>
                </div>
              </div>
            </div>
          </Link>
        </div>
      </div>

      {/* Recent Files */}
      <div className="bg-secondary p-6 rounded-lg shadow border border-primary">
        <h2 className="text-lg md:text-xl font-semibold text-primary mb-4">{t('dashboard.recentFiles')}</h2>
        <div className="divide-y divide-primary">
          {[1, 2, 3].map((item, index) => (
            <div
              key={item}
              className={`flex items-center space-x-4 p-4 hover:bg-primary hover:bg-opacity-50 rounded-lg transition-all duration-200 cursor-pointer group ${
                index === 0 ? 'pt-0' : ''
              } ${index === 2 ? 'pb-0' : ''}`}
            >
              <div className="p-2 bg-cyan-500 rounded flex-shrink-0 group-hover:bg-cyan-600 transition-colors">
                <FileText className="w-4 h-4 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-primary truncate group-hover:text-accent-purple transition-colors">
                  文档 {item}.pdf
                </h4>
                <p className="text-comment text-sm truncate">
                  {t('dashboard.modifiedAgo', { time: '2小时' })}
                </p>
              </div>
              <span className="text-comment text-sm flex-shrink-0 group-hover:text-primary transition-colors">
                2.5 MB
              </span>
            </div>
          ))}
        </div>
        {/* 查看更多链接 */}
        <div className="mt-4 pt-4 border-t border-primary">
          <Link 
            to="/files" 
            className="text-accent-purple hover:text-purple-400 text-sm font-medium transition-colors"
          >
            {t('dashboard.viewAllFiles')} →
          </Link>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
