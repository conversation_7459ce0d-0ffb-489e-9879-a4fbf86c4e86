import React, { useState, useEffect, useCallback, useRef } from 'react'
import { useDropzone } from 'react-dropzone'
import { useNavigate } from 'react-router-dom'
import { useI18n } from '../contexts/I18nContext'
import { useAuthStore, useFileStore } from '../stores'
import { showPromptDialog } from '../contexts/ToastContext'
import { fileService } from '../services/fileService'
import { imageBedService } from '../services/imageBedService'
import { ApiError } from '../services/api'
import FileList from '../components/files/FileList'
import MobileFileList from '../components/files/MobileFileList'
import MobileFilePreview from '../components/files/MobileFilePreview'
import FileUpload from '../components/files/FileUpload'
import FileSearch from '../components/files/FileSearch'
import ImageBedUpload from '../components/files/ImageBedUpload'
import ImageBedViewer from '../components/files/ImageBedViewer'
// Removed AdvancedFileSearch as it's no longer needed
import FolderManagementModal from '../components/files/FolderManagementModal'
import HierarchicalMoveModal from '../components/files/HierarchicalMoveModal'
import FolderBreadcrumb from '../components/files/FolderBreadcrumb'
import CreateFolderModal from '../components/files/CreateFolderModal'
import FileContextMenu from '../components/files/FileContextMenu'
import BackgroundContextMenu from '../components/files/BackgroundContextMenu'
import BatchOperations from '../components/files/BatchOperations'
import FilePreview from '../components/files/FilePreview'
import Button from '../components/ui/Button'
import ShareDialog from '../components/files/ShareDialog'
import ShareStatsDialog from '../components/files/ShareStatsDialog'
import { useMobile } from '../hooks/useMobile'
import {
  FolderPlusIcon,
  CloudArrowUpIcon,
  ViewColumnsIcon,
  ListBulletIcon,
} from '@heroicons/react/24/outline'

export interface FileItem {
  id: string
  name: string
  type: 'file' | 'folder'
  size?: number
  mimeType?: string
  modifiedAt: Date
  isShared?: boolean
  thumbnailUrl?: string
  publicUrl?: string
  cdnUrl?: string
  thumbnails?: {
    small?: string
    medium?: string
    large?: string
  }
  isImageBedFolder?: boolean
}

export interface FolderPath {
  id: string
  name: string
}

const Files: React.FC = () => {
  const { isMobile } = useMobile()
  const { t, language } = useI18n()
  const navigate = useNavigate()
  const { isAuthenticated, isLoading: authLoading } = useAuthStore()

  // Use Zustand stores for file management
  const fileStore = useFileStore()

  // Keep existing local state for now to avoid breaking changes
  const [files, setFiles] = useState<FileItem[]>([])
  const [currentFolder, setCurrentFolder] = useState<string | null>(null)
  const [folderPath, setFolderPath] = useState<FolderPath[]>([{ id: 'root', name: '/root' }])
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set())
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [searchQuery, setSearchQuery] = useState('')
  const [showCreateFolder, setShowCreateFolder] = useState(false)
  const [showUploadModal, setShowUploadModal] = useState(false)
  const [loading, setLoading] = useState(false)
  const [contextMenu, setContextMenu] = useState<{
    file: FileItem
    position: { x: number; y: number }
  } | null>(null)
  const [backgroundContextMenu, setBackgroundContextMenu] = useState<{
    position: { x: number; y: number }
  } | null>(null)

  // 右键菜单自动消失的定时器
  const contextMenuTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // 清理右键菜单的函数
  const clearContextMenus = useCallback(() => {
    setContextMenu(null)
    setBackgroundContextMenu(null)
    if (contextMenuTimeoutRef.current) {
      clearTimeout(contextMenuTimeoutRef.current)
      contextMenuTimeoutRef.current = null
    }
  }, [])

  // 智能定位右键菜单，避免超出屏幕边界
  const getSmartPosition = useCallback((clientX: number, clientY: number) => {
    const menuWidth = 208 // min-w-52 = 208px
    const menuHeight = 200 // 估算菜单高度
    const padding = 8 // 边距

    let x = clientX
    let y = clientY

    // 检查右边界
    if (x + menuWidth + padding > window.innerWidth) {
      x = clientX - menuWidth
    }

    // 检查下边界
    if (y + menuHeight + padding > window.innerHeight) {
      y = clientY - menuHeight
    }

    // 确保不超出左边界和上边界
    x = Math.max(padding, x)
    y = Math.max(padding, y)

    return { x, y }
  }, [])

  // 监听全局事件来关闭右键菜单
  useEffect(() => {
    const handleGlobalClick = (e: MouseEvent) => {
      // 检查点击是否在右键菜单外
      const target = e.target as HTMLElement
      const isContextMenuClick = target.closest('[data-context-menu]')

      if (!isContextMenuClick && (contextMenu || backgroundContextMenu)) {
        clearContextMenus()
      }
    }

    const handleGlobalContextMenu = (e: MouseEvent) => {
      // 在任何地方右键时，如果不是在文件项上，则清理现有菜单
      const target = e.target as HTMLElement
      const isFileItem = target.closest('[data-file-item]')
      const isContextMenu = target.closest('[data-context-menu]')

      if (!isFileItem && !isContextMenu && (contextMenu || backgroundContextMenu)) {
        clearContextMenus()
      }
    }

    const handleKeyDown = (e: KeyboardEvent) => {
      // ESC键关闭右键菜单
      if (e.key === 'Escape' && (contextMenu || backgroundContextMenu)) {
        clearContextMenus()
      }
    }

    const handleScroll = () => {
      // 滚动时关闭右键菜单
      if (contextMenu || backgroundContextMenu) {
        clearContextMenus()
      }
    }

    const handleResize = () => {
      // 窗口大小改变时关闭右键菜单
      if (contextMenu || backgroundContextMenu) {
        clearContextMenus()
      }
    }

    if (contextMenu || backgroundContextMenu) {
      document.addEventListener('click', handleGlobalClick)
      document.addEventListener('contextmenu', handleGlobalContextMenu)
      document.addEventListener('keydown', handleKeyDown)
      document.addEventListener('scroll', handleScroll, true)
      window.addEventListener('resize', handleResize)

      contextMenuTimeoutRef.current = setTimeout(() => {
        clearContextMenus()
      }, 5000)
    }

    return () => {
      document.removeEventListener('click', handleGlobalClick)
      document.removeEventListener('contextmenu', handleGlobalContextMenu)
      document.removeEventListener('keydown', handleKeyDown)
      document.removeEventListener('scroll', handleScroll, true)
      window.removeEventListener('resize', handleResize)
      if (contextMenuTimeoutRef.current) {
        clearTimeout(contextMenuTimeoutRef.current)
        contextMenuTimeoutRef.current = null
      }
    }
  }, [contextMenu, backgroundContextMenu, clearContextMenus])
  const [previewFile, setPreviewFile] = useState<FileItem | null>(null)
  const [sortBy, setSortBy] = useState<'name' | 'size' | 'modifiedAt' | 'type'>('name')
  const [fileTypeFilter, setFileTypeFilter] = useState<string>('')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  // Removed activeTab state as we no longer need tabs
  const [selectionMode, setSelectionMode] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // State for image bed folder info
  const [imageBedFolderId, setImageBedFolderId] = useState<string | null>(null)
  const [showImageBedUpload, setShowImageBedUpload] = useState(false)
  const [imageBedImages, setImageBedImages] = useState<any[]>([])

  // Check if current folder is image bed folder (based on ID, not name)
  const isImageBedFolder = imageBedFolderId && currentFolder === imageBedFolderId

  // Update folder path names when language changes
  useEffect(() => {
    if (folderPath.length > 0 && imageBedFolderId) {
      const updatedPath = folderPath.map(folder => {
        // Update image bed folder name based on current language (using ID)
        if (folder.id === imageBedFolderId) {
          return { ...folder, name: t('files.imageBedFolder') }
        }
        // Update root folder name
        if (folder.name === '/root' || folder.name === '/根' || folder.name === '我的文件') {
          return { ...folder, name: language === 'zh-CN' ? '/根' : '/root' }
        }
        return folder
      })

      // Only update if there are actual changes
      const hasChanges = updatedPath.some((folder, index) => folder.name !== folderPath[index].name)
      if (hasChanges) {
        setFolderPath(updatedPath)
      }
    }
  }, [language, t, folderPath, imageBedFolderId])
  // Removed showAdvancedSearch state as it's no longer needed
  const [folderManagement, setFolderManagement] = useState<{
    mode: 'rename' | 'move'
    folderId?: string
    fileId?: string
    currentName?: string
  } | null>(null)
  const [hierarchicalMove, setHierarchicalMove] = useState<{
    itemId: string
    itemType: 'file' | 'folder'
    itemName: string
    currentFolderId?: string
  } | null>(null)
  const [showShareDialog, setShowShareDialog] = useState(false)
  const [shareDialogFile, setShareDialogFile] = useState<FileItem | null>(null)
  const [showShareStats, setShowShareStats] = useState(false)

  // Load files from API
  const loadFiles = useCallback(async () => {
    // Only load files if user is authenticated
    if (!isAuthenticated) {
      return
    }

    try {
      setLoading(true)
      setError(null)

      // 如果在根目录，确保图床文件夹存在
      if (!currentFolder) {
        try {
          const imageBedFolder = await imageBedService.getOrCreateImageBedFolder()
          // 检查返回的图床文件夹是否有效
          if (imageBedFolder?.id) {
            // 只有当 imageBedFolderId 未设置或与实际ID不匹配时才更新
            if (!imageBedFolderId || imageBedFolderId !== imageBedFolder.id) {
              setImageBedFolderId(imageBedFolder.id)
            }
          }
        } catch (error) {
          console.error('Failed to ensure image bed folder exists:', error)
          // 不阻止文件加载，继续执行
        }
      }

      // If we're in the image bed folder, use the image bed service
      if (isImageBedFolder) {
        console.log('Loading image bed images...', { currentFolder, folderPath })

        try {
          const result = await imageBedService.listImageBedImages({
            page: 1,
            limit: 50,
            sortBy: sortBy === 'modifiedAt' ? 'createdAt' : (sortBy as 'name' | 'createdAt' | 'accessCount'),
            sortOrder: sortOrder
          })

          console.log('Image bed result:', result)

          // Store image bed images separately for the ImageBedViewer
          setImageBedImages(result.images)

          // Also convert to FileItem format for compatibility
          const imageFiles: FileItem[] = result.images.map(image => ({
            id: image.id,
            name: image.originalName,
            type: 'file' as const,
            size: 0, // Size not provided by image bed service
            mimeType: 'image/*', // Assume image type
            modifiedAt: new Date(image.createdAt),
            isShared: true, // Images in image bed are public
            publicUrl: image.publicUrl,
            cdnUrl: image.cdnUrl,
            thumbnailUrl: image.thumbnails?.small || image.cdnUrl
          }))

          console.log('Converted image files:', imageFiles)
          setFiles(imageFiles)
          return
        } catch (error) {
          console.error('Error loading image bed images:', error)
          // Fall back to regular file listing if image bed fails
        }
      }

      // Regular file listing for non-image-bed folders
      // Map frontend sort fields to backend expected fields
      const mapSortByToAPI = (frontendSortBy: string) => {
        switch (frontendSortBy) {
          case 'modifiedAt':
            return 'modifiedAt'
          case 'name':
            return 'name'
          case 'size':
            return 'size'
          case 'type':
            return 'type'
          default:
            return 'name'
        }
      }

      const pagination = {
        page: 1,
        limit: 50,
        sortBy: mapSortByToAPI(sortBy),
        sortOrder: sortOrder,
        fileType: fileTypeFilter || undefined
      }

      const fileList = await fileService.getFiles(currentFolder || undefined, pagination)

      // Convert API response to FileItem format
      const convertedFiles: FileItem[] = fileList.files.map(file => ({
        id: file.id,
        name: file.originalName || file.filename,
        type: 'file', // Files from API are always files, folders come separately
        size: file.size,
        mimeType: file.mimeType,
        modifiedAt: new Date(file.uploadedAt),
        isShared: file.isPublic,
        thumbnailUrl: undefined // Will be generated by preview API
      }))

      // Add folders to the list
      const folderItems: FileItem[] = fileList.folders.map(folder => {
        // 使用辅助函数判断是否为图床文件夹
        const isImageBed = isImageBedFolderCheck(folder)

        // 如果是图床文件夹，更新imageBedFolderId
        if (isImageBed && (!imageBedFolderId || imageBedFolderId !== folder.id)) {
          setImageBedFolderId(folder.id)
          console.log('Updated image bed folder ID from folder list:', folder.id)
        }

        console.log('Processing folder:', {
          folderId: folder.id,
          folderName: folder.name,
          folderParentId: folder.parentId,
          currentFolder,
          isImageBed,
          imageBedFolderId
        })

        return {
          id: folder.id,
          name: isImageBed ? t('files.imageBedFolder') : folder.name,
          type: 'folder',
          modifiedAt: new Date(folder.modifiedAt),
          isShared: false,
          isImageBedFolder: isImageBed
        }
      })

      setFiles([...folderItems, ...convertedFiles])
    } catch (error) {
      console.error('Failed to load files:', error)
      if (error instanceof ApiError) {
        if (error.status === 401) {
          setError('认证已过期，请重新登录')
          // The auth context should handle automatic logout
        } else {
          setError(error.message)
        }
      } else {
        setError('Failed to load files. Please try again.')
      }
    } finally {
      setLoading(false)
    }
  }, [isAuthenticated, currentFolder, sortBy, sortOrder, fileTypeFilter, isImageBedFolder, t])

  // Initialize image bed folder
  useEffect(() => {
    const initImageBedFolder = async () => {
      try {
        const imageBedFolder = await imageBedService.getOrCreateImageBedFolder()
        setImageBedFolderId(imageBedFolder.id)
        console.log('Image bed folder initialized:', imageBedFolder)
      } catch (error) {
        console.error('Failed to initialize image bed folder:', error)

        // If it's an auth error, try to refresh token
        if (error instanceof Error && error.message.includes('401')) {
          console.log('Auth error detected, user may need to re-login')
          // The auth context should handle this automatically
        }
      }
    }

    if (isAuthenticated) {
      initImageBedFolder()
    }
  }, [isAuthenticated])

  // Helper function to check if a folder is the image bed folder
  const isImageBedFolderCheck = useCallback((folder: any) => {
    // 图床文件夹必须满足：1. 在根目录 2. 名称为"Image Bed"
    return !currentFolder && folder.name === 'Image Bed' && !folder.parentId
  }, [currentFolder])

  useEffect(() => {
    loadFiles()
  }, [loadFiles])

  // Listen for WebSocket file list refresh events
  useEffect(() => {
    const handleFileListRefresh = () => {
      console.log('Refreshing file list due to WebSocket event')
      loadFiles()
    }

    window.addEventListener('file-list-refresh', handleFileListRefresh)

    return () => {
      window.removeEventListener('file-list-refresh', handleFileListRefresh)
    }
  }, [loadFiles])

  // Update folder path name with translation
  useEffect(() => {
    setFolderPath(prev =>
      prev.map((item, index) =>
        index === 0 ? { ...item, name: t('files.rootFolder') } : item
      )
    )
  }, [t])

  const onDrop = useCallback((acceptedFiles: File[]) => {
    // Handle file upload
    console.log('Files dropped:', acceptedFiles)

    // If in image bed folder, only allow image files and use image bed upload
    if (isImageBedFolder) {
      const imageFiles = acceptedFiles.filter(file => file.type.startsWith('image/'))
      if (imageFiles.length === 0) {
        setError(t('files.onlyImagesAllowed'))
        return
      }
      if (imageFiles.length < acceptedFiles.length) {
        setError(t('files.onlyImagesAllowedInImageBed'))
      }
      // Show image bed upload modal
      setShowImageBedUpload(true)
    } else {
      setShowUploadModal(true)
    }
  }, [isImageBedFolder, t])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    noClick: true,
    noKeyboard: true,
  })

  const handleFolderNavigation = (folderId: string, folderName: string) => {
    setCurrentFolder(folderId)

    // If entering image bed folder, use translated name
    const displayName = folderId === imageBedFolderId ? t('files.imageBedFolder') : folderName

    setFolderPath(prev => [...prev, { id: folderId, name: displayName }])
  }

  const handleBreadcrumbNavigation = (index: number) => {
    const newPath = folderPath.slice(0, index + 1)
    setFolderPath(newPath)
    setCurrentFolder(index === 0 ? null : newPath[newPath.length - 1].id)
  }

  const handleFileSelect = (fileId: string, selected: boolean) => {
    // Prevent selecting image bed folder
    const file = files.find(f => f.id === fileId)
    if (file?.isImageBedFolder) {
      return
    }

    // Close context menu when selecting files
    setContextMenu(null)

    setSelectedFiles(prev => {
      const newSet = new Set(prev)
      if (selected) {
        newSet.add(fileId)
      } else {
        newSet.delete(fileId)
      }
      return newSet
    })
  }

  const handleBackgroundClick = (e: React.MouseEvent) => {
    // Only clear selection if clicking on the background, not on file items
    if (e.target === e.currentTarget) {
      setSelectedFiles(new Set())
      setContextMenu(null)
      setBackgroundContextMenu(null)
    }
  }

  const handleBackgroundContextMenu = (e: React.MouseEvent) => {
    // Check if the click is on the main container or its direct children (not file items)
    const target = e.target as HTMLElement
    const currentTarget = e.currentTarget as HTMLElement

    // Allow context menu on the main container and its padding area
    const isMainContainer = target === currentTarget
    const isInPaddingArea = target.closest('[data-file-item]') === null

    if (isMainContainer || isInPaddingArea) {
      e.preventDefault()
      e.stopPropagation()

      // Don't show background context menu if files are selected
      if (selectedFiles.size > 0) {
        return
      }

      // 清理之前的定时器
      if (contextMenuTimeoutRef.current) {
        clearTimeout(contextMenuTimeoutRef.current)
        contextMenuTimeoutRef.current = null
      }

      const smartPosition = getSmartPosition(e.clientX, e.clientY)
      setBackgroundContextMenu({
        position: smartPosition
      })
      setContextMenu(null) // Close file context menu if open
    }
  }

  const handleBackgroundContextAction = (action: string) => {
    setBackgroundContextMenu(null)

    switch (action) {
      case 'createFolder':
        setShowCreateFolder(true)
        break
      case 'uploadFiles':
        if (isImageBedFolder) {
          setShowImageBedUpload(true)
        } else {
          setShowUploadModal(true)
        }
        break
    }
  }

  const handleSelectAll = () => {
    // Exclude image bed folder from selection
    const selectableFiles = files.filter(file => !file.isImageBedFolder)

    if (selectedFiles.size === selectableFiles.length) {
      setSelectedFiles(new Set())
    } else {
      setSelectedFiles(new Set(selectableFiles.map(f => f.id)))
    }
  }

  const handleDeleteSelected = async () => {
    if (selectedFiles.size === 0) return

    setLoading(true)
    try {
      // Get selected items and separate files from folders
      const selectedItems = files.filter(item => selectedFiles.has(item.id))

      const deletePromises = selectedItems.map(item => {
        if (item.type === 'folder') {
          return fileService.deleteFolder(item.id, false) // Soft delete (move to trash)
        } else {
          return fileService.moveFileToTrash(item.id) // Move file to trash
        }
      })

      await Promise.all(deletePromises)

      // Reload files after deletion
      await loadFiles()
      setSelectedFiles(new Set())
    } catch (error) {
      console.error('Move to trash failed:', error)
      setError('Failed to move items to trash. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleContextMenu = (file: FileItem, position?: { x: number; y: number }) => {
    // 清理之前的定时器
    if (contextMenuTimeoutRef.current) {
      clearTimeout(contextMenuTimeoutRef.current)
      contextMenuTimeoutRef.current = null
    }

    if (position) {
      setContextMenu({ file, position })
    } else {
      // For mobile, show context menu at center of screen
      setContextMenu({
        file,
        position: { x: window.innerWidth / 2, y: window.innerHeight / 2 }
      })
    }
    setBackgroundContextMenu(null) // Close background context menu if open
  }

  const handleContextMenuEvent = (e: React.MouseEvent, file: FileItem) => {
    e.preventDefault()
    e.stopPropagation()

    // Prevent context menu for image bed folder
    if (file.isImageBedFolder) {
      return
    }

    // Prevent context menu when batch operations are active (files are selected)
    if (selectedFiles.size > 0) {
      return
    }

    // 清理之前的定时器
    if (contextMenuTimeoutRef.current) {
      clearTimeout(contextMenuTimeoutRef.current)
      contextMenuTimeoutRef.current = null
    }

    const smartPosition = getSmartPosition(e.clientX, e.clientY)
    handleContextMenu(file, smartPosition)
    setBackgroundContextMenu(null) // Close background context menu if open
  }

  // Removed handleAdvancedSearch function as it's no longer needed

  const handleContextAction = (action: string, file: FileItem) => {
    setContextMenu(null) // Close context menu first

    switch (action) {
      case 'download':
        handleDownloadFile(file)
        break
      case 'rename':
        if (file.type === 'folder') {
          setFolderManagement({
            mode: 'rename',
            folderId: file.id,
            currentName: file.name
          })
        } else {
          handleRenameFile(file)
        }
        break
      case 'duplicate':
        handleDuplicateFile(file)
        break
      case 'move':
        setHierarchicalMove({
          itemId: file.id,
          itemType: file.type,
          itemName: file.name,
          currentFolderId: currentFolder || undefined
        })
        break
      case 'share':
        handleShareFile(file)
        break
      case 'info':
        setPreviewFile(file)
        break
      case 'moveToTrash':
        handleMoveToTrash(file)
        break
      case 'restore':
        handleRestoreFromTrash(file)
        break
      case 'delete':
        handleDeleteFile(file)
        break
      case 'moveToImageBed':
        handleMoveToImageBed(file)
        break
    }
  }

  const handleDownloadFile = async (file: FileItem) => {
    try {
      const blob = await fileService.downloadFile(file.id)
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = file.name
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error('Download failed:', error)
      setError('Failed to download file. Please try again.')
    }
  }

  const handleDeleteFile = async (file: FileItem) => {
    setLoading(true)
    try {
      if (file.type === 'folder') {
        await fileService.deleteFolder(file.id, false) // Soft delete (move to trash)
      } else {
        await fileService.moveFileToTrash(file.id) // Move file to trash
      }
      await loadFiles() // Reload files after deletion
    } catch (error) {
      console.error('Move to trash failed:', error)
      setError('Failed to move to trash. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleBatchDownload = async () => {
    const selectedFilesList = files.filter(f => selectedFiles.has(f.id) && f.type === 'file')

    try {
      for (const file of selectedFilesList) {
        await handleDownloadFile(file)
        // Add small delay between downloads to avoid overwhelming the browser
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    } catch (error) {
      console.error('Batch download failed:', error)
      setError('Failed to download files. Please try again.')
    }
  }

  const handleBatchMove = async (targetFolderId?: string) => {
    if (selectedFiles.size === 0) return

    setLoading(true)
    try {
      // Get selected items and separate files from folders
      const selectedItems = files.filter(item => selectedFiles.has(item.id))

      const movePromises = selectedItems.map(item => {
        if (item.type === 'folder') {
          return fileService.moveFolder(item.id, targetFolderId)
        } else {
          return fileService.moveFile(item.id, targetFolderId)
        }
      })

      await Promise.all(movePromises)

      await loadFiles() // Reload files after move
      setSelectedFiles(new Set())
    } catch (error) {
      console.error('Batch move failed:', error)
      setError('Failed to move items. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleBatchCopy = async () => {
    if (selectedFiles.size === 0) return

    setLoading(true)
    try {
      // Get selected items and separate files from folders
      const selectedItems = files.filter(item => selectedFiles.has(item.id))

      // Filter out folders for now since folder copying is not implemented
      const filesToCopy = selectedItems.filter(item => item.type === 'file')
      const foldersSkipped = selectedItems.filter(item => item.type === 'folder')

      if (foldersSkipped.length > 0) {
        console.warn(`Skipping ${foldersSkipped.length} folders - folder copying not yet implemented`)
      }

      if (filesToCopy.length > 0) {
        const copyPromises = filesToCopy.map(item =>
          fileService.duplicateFile(item.id)
        )

        await Promise.all(copyPromises)
      }

      await loadFiles() // Reload files after copy
      setSelectedFiles(new Set())

      if (foldersSkipped.length > 0) {
        setError(`Copied ${filesToCopy.length} files. Folder copying is not yet supported.`)
      }
    } catch (error) {
      console.error('Batch copy failed:', error)
      setError('Failed to copy items. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleFileDrop = async (itemId: string, targetFolderId: string) => {
    try {
      setLoading(true)

      // Find the item being dragged to determine its type
      const draggedItem = files.find(file => file.id === itemId)
      if (!draggedItem) {
        throw new Error('Dragged item not found')
      }

      // Call appropriate API based on item type
      if (draggedItem.type === 'folder') {
        await fileService.moveFolder(itemId, targetFolderId)
      } else {
        await fileService.moveFile(itemId, targetFolderId)
      }

      await loadFiles() // Reload files after move
    } catch (error) {
      console.error('Item move failed:', error)
      setError('Failed to move item. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleBatchShare = () => {
    // For batch share, we'll share the first selected file
    // In a real implementation, you might want to create multiple shares or a combined share
    const firstSelectedFile = filteredAndSortedFiles.find(file => selectedFiles.has(file.id))
    if (firstSelectedFile) {
      handleShareFile(firstSelectedFile)
    }
  }

  const handleRenameFile = async (file: FileItem) => {
    const newName = await showPromptDialog({
      title: t('files.enterNewName'),
      defaultValue: file.name,
      onConfirm: () => {},
      onCancel: () => {}
    })

    if (!newName || newName === file.name) return

    try {
      setLoading(true)
      await fileService.renameFile(file.id, newName)
      await loadFiles()
    } catch (error) {
      console.error('Rename failed:', error)
      setError('Failed to rename file. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleDuplicateFile = async (file: FileItem) => {
    try {
      setLoading(true)
      await fileService.duplicateFile(file.id)
      await loadFiles()
    } catch (error) {
      console.error('Duplicate failed:', error)
      setError('Failed to duplicate file. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleShareFile = async (file: FileItem) => {
    console.log('handleShareFile called with file:', file) // Debug log
    console.log('File ID:', file.id, 'File name:', file.name) // Debug log
    setShareDialogFile(file)
    setShowShareDialog(true)
  }

  const handleMoveToImageBed = async (file: FileItem) => {
    try {
      setLoading(true)
      console.log('Moving image to image bed:', file)

      // Check if file is an image
      if (!file.mimeType?.startsWith('image/')) {
        setError('Only image files can be moved to image bed')
        return
      }

      const result = await imageBedService.moveImageToImageBed(file.id)
      console.log('Image moved to image bed successfully:', result)

      // Show success message with public URL
      setError(`Image moved to image bed successfully! Public URL: ${result.publicUrl}`)

      // Reload files to reflect the change
      await loadFiles()
    } catch (error) {
      console.error('Move to image bed failed:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to move image to image bed'
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleMoveToTrash = async (file: FileItem) => {
    try {
      setLoading(true)
      if (file.type === 'folder') {
        await fileService.deleteFolder(file.id, false) // Soft delete
      } else {
        await fileService.moveFileToTrash(file.id)
      }
      await loadFiles()
    } catch (error) {
      console.error('Move to trash failed:', error)
      setError('Failed to move to trash. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleRestoreFromTrash = async (file: FileItem) => {
    try {
      setLoading(true)
      await fileService.restoreFromTrash(file.id)
      await loadFiles()
    } catch (error) {
      console.error('Restore failed:', error)
      setError('Failed to restore file. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const sortFiles = (files: FileItem[]) => {
    return [...files].sort((a, b) => {
      // Image bed folder always comes first
      if (a.isImageBedFolder && !b.isImageBedFolder) return -1
      if (!a.isImageBedFolder && b.isImageBedFolder) return 1

      let comparison = 0

      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name)
          break
        case 'size':
          comparison = (a.size || 0) - (b.size || 0)
          break
        case 'modifiedAt':
          comparison = a.modifiedAt.getTime() - b.modifiedAt.getTime()
          break
        case 'type':
          if (a.type !== b.type) {
            comparison = a.type === 'folder' ? -1 : 1
          } else {
            comparison = (a.mimeType || '').localeCompare(b.mimeType || '')
          }
          break
      }

      return sortOrder === 'asc' ? comparison : -comparison
    })
  }

  const filteredAndSortedFiles = sortFiles(
    files.filter(file =>
      file.name.toLowerCase().includes(searchQuery.toLowerCase())
    )
  )

  // Show loading while checking authentication
  if (authLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-dracula-comment">Loading...</p>
        </div>
      </div>
    )
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    navigate('/login')
    return null
  }

  return (
    <div
      className="h-full flex flex-col"
      onContextMenu={(e) => e.preventDefault()} // 禁用浏览器默认右键菜单
    >
      {/* Header */}
      <div className="flex-shrink-0 border-b border-current-line bg-secondary">
        <div className="px-4 md:px-6 py-4 md:py-6">
        {/* Header with navigation */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            {/* File Navigation Breadcrumb */}
            <FolderBreadcrumb
              path={folderPath}
              onNavigate={handleBreadcrumbNavigation}
            />
          </div>
        </div>

        {/* Second row with search, filters and additional actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <FileSearch
              value={searchQuery}
              onChange={setSearchQuery}
              placeholder={t('files.searchPlaceholder')}
            />

            <div className="flex items-center space-x-2">
              {!isImageBedFolder && (
                <select
                  value={fileTypeFilter}
                  onChange={(e) => setFileTypeFilter(e.target.value)}
                  className="text-sm form-input"
                >
                  <option value="">{t('files.allTypes') || 'All Types'}</option>
                  <option value="image">{t('files.images') || 'Images'}</option>
                  <option value="video">{t('files.videos') || 'Videos'}</option>
                  <option value="audio">{t('files.audio') || 'Audio'}</option>
                  <option value="document">{t('files.documents')}</option>
                  <option value="archive">{t('files.archives') || 'Archives'}</option>
                </select>
              )}

              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="text-sm form-input"
              >
                <option value="name">{t('files.name')}</option>
                <option value="modifiedAt">{t('files.modified')}</option>
                <option value="size">{t('files.size')}</option>
                <option value="type">{t('files.type')}</option>
              </select>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              >
                {sortOrder === 'asc' ? '↑' : '↓'}
              </Button>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
            >
              {viewMode === 'grid' ? (
                <ListBulletIcon className="w-4 h-4" />
              ) : (
                <ViewColumnsIcon className="w-4 h-4" />
              )}
            </Button>

            {!isImageBedFolder && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowCreateFolder(true)}
              >
                <FolderPlusIcon className="w-4 h-4 mr-2" />
                {t('files.newFolder')}
              </Button>
            )}

            <Button
              variant="primary"
              size="sm"
              onClick={() => {
                if (isImageBedFolder) {
                  setShowImageBedUpload(true)
                } else {
                  setShowUploadModal(true)
                }
              }}
            >
              <CloudArrowUpIcon className="w-4 h-4 mr-2" />
              {isImageBedFolder ? t('files.uploadToImageBed') : t('files.uploadFiles')}
            </Button>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
            <button
              onClick={() => setError(null)}
              className="ml-2 text-red-500 hover:text-red-700"
            >
              ×
            </button>
          </div>
        )}

        {/* Batch Operations */}
        {selectedFiles.size > 0 && !isImageBedFolder && (
          <div className="mt-4">
            <BatchOperations
              selectedCount={selectedFiles.size}
              onDownload={handleBatchDownload}
              onMove={handleBatchMove}
              onCopy={handleBatchCopy}
              onShare={handleBatchShare}
              onDelete={handleDeleteSelected}
              loading={loading}
              isImageBedFolder={!!isImageBedFolder}
            />
          </div>
        )}
        </div>
      </div>

      {/* Main Content */}
      <div
        className="flex-1 p-4 overflow-hidden"
        onContextMenu={handleBackgroundContextMenu}
      >
        <div
          {...getRootProps()}
          className={`h-full flex flex-col border border-current-line rounded-lg ${isDragActive ? 'bg-current-line opacity-50' : 'bg-secondary'
            }`}
        >
        <input {...getInputProps()} />

        {isDragActive && (
          <div className="absolute inset-0 bg-purple-100 border-2 border-dashed border-purple-400 flex items-center justify-center z-10">
            <div className="text-center">
              <CloudArrowUpIcon className="w-16 h-16 accent-purple mx-auto mb-4" />
              <p className="text-lg font-medium accent-purple">
                {t('files.dropToUpload')}
              </p>
            </div>
          </div>
        )}

        {isImageBedFolder ? (
          <ImageBedViewer
            images={imageBedImages}
            onImageClick={(image) => {
              // Convert to FileItem for preview
              const fileItem: FileItem = {
                id: image.id,
                name: image.originalName,
                type: 'file',
                mimeType: 'image/*',
                modifiedAt: new Date(image.createdAt),
                isShared: true,
                publicUrl: image.publicUrl,
                cdnUrl: image.cdnUrl,
                thumbnailUrl: image.thumbnails?.small || image.cdnUrl
              }
              setPreviewFile(fileItem)
            }}
            loading={loading}
          />
        ) : isMobile ? (
          <div
            onClick={handleBackgroundClick}
            className="flex-1 p-2 overflow-hidden"
          >
            <MobileFileList
              files={filteredAndSortedFiles}
              selectedFiles={selectedFiles}
              onFileSelect={handleFileSelect}
              onSelectAll={handleSelectAll}
              onFolderOpen={handleFolderNavigation}
              onContextMenu={handleContextMenu}
              onFilePreview={setPreviewFile}
              loading={loading}
              selectionMode={selectionMode}
              onSelectionModeChange={setSelectionMode}
            />
          </div>
        ) : (
          <div
            onClick={handleBackgroundClick}
            className="flex-1 p-2 overflow-hidden"
          >
            <FileList
              files={filteredAndSortedFiles}
              viewMode={viewMode}
              selectedFiles={selectedFiles}
              onFileSelect={handleFileSelect}
              onSelectAll={handleSelectAll}
              onFolderOpen={handleFolderNavigation}
              onContextMenu={handleContextMenuEvent}
              onFilePreview={setPreviewFile}
              onFileDrop={handleFileDrop}
              loading={loading}
              isImageBedFolder={!!isImageBedFolder}
            />
          </div>
        )}
        </div>
      </div>

      {/* Modals */}
      {showCreateFolder && (
        <CreateFolderModal
          onClose={() => setShowCreateFolder(false)}
          currentFolderId={currentFolder}
          imageBedFolderId={imageBedFolderId}
          onCreateFolder={async (name) => {
            try {
              await fileService.createFolder({
                name,
                parentId: currentFolder || undefined
              })
              await loadFiles() // Reload files after folder creation
              setShowCreateFolder(false)
            } catch (error) {
              console.error('Create folder failed:', error)
              setError('Failed to create folder. Please try again.')
            }
          }}
        />
      )}

      {showUploadModal && (
        <FileUpload
          onClose={() => setShowUploadModal(false)}
          currentFolderId={currentFolder || undefined}
          isImageBedFolder={!!isImageBedFolder}
          onUploadComplete={async () => {
            // Reload files after upload to get the latest data
            await loadFiles()
            setShowUploadModal(false)
          }}
        />
      )}

      {/* Image Bed Upload Modal */}
      {showImageBedUpload && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <ImageBedUpload
            onUploadComplete={async (results) => {
              console.log('Image bed upload completed:', results)
              // Reload files after upload
              await loadFiles()
              setShowImageBedUpload(false)
            }}
            onClose={() => setShowImageBedUpload(false)}
          />
        </div>
      )}

      {/* Context Menu */}
      {contextMenu && (
        <FileContextMenu
          file={contextMenu.file}
          position={contextMenu.position}
          onClose={() => setContextMenu(null)}
          onAction={handleContextAction}
          imageBedFolderId={imageBedFolderId}
        />
      )}

      {/* Background Context Menu */}
      {backgroundContextMenu && (
        <BackgroundContextMenu
          position={backgroundContextMenu.position}
          onClose={() => setBackgroundContextMenu(null)}
          onAction={handleBackgroundContextAction}
          isImageBedFolder={!!isImageBedFolder}
        />
      )}

      {/* Folder Management Modal */}
      {folderManagement && (
        <FolderManagementModal
          mode={folderManagement.mode}
          folderId={folderManagement.folderId}
          fileId={folderManagement.fileId}
          currentName={folderManagement.currentName}
          onClose={() => setFolderManagement(null)}
          onSuccess={async () => {
            await loadFiles()
            setFolderManagement(null)
          }}
        />
      )}

      {/* File Preview */}
      {previewFile && (
        <>
          {isMobile ? (
            <MobileFilePreview
              file={previewFile}
              files={filteredAndSortedFiles.filter(f => f.type === 'file')}
              onClose={() => setPreviewFile(null)}
              onDownload={handleDownloadFile}
              onShare={handleShareFile}
              onNext={() => {
                const filesList = filteredAndSortedFiles.filter(f => f.type === 'file')
                const currentIndex = filesList.findIndex(f => f.id === previewFile.id)
                if (currentIndex < filesList.length - 1) {
                  setPreviewFile(filesList[currentIndex + 1])
                }
              }}
              onPrevious={() => {
                const filesList = filteredAndSortedFiles.filter(f => f.type === 'file')
                const currentIndex = filesList.findIndex(f => f.id === previewFile.id)
                if (currentIndex > 0) {
                  setPreviewFile(filesList[currentIndex - 1])
                }
              }}
            />
          ) : (
            <FilePreview
              file={previewFile}
              files={filteredAndSortedFiles.filter(f => f.type === 'file')}
              onClose={() => setPreviewFile(null)}
              onDownload={handleDownloadFile}
              onShare={handleShareFile}
              onNext={() => {
                const filesList = filteredAndSortedFiles.filter(f => f.type === 'file')
                const currentIndex = filesList.findIndex(f => f.id === previewFile.id)
                if (currentIndex < filesList.length - 1) {
                  setPreviewFile(filesList[currentIndex + 1])
                }
              }}
              onPrevious={() => {
                const filesList = filteredAndSortedFiles.filter(f => f.type === 'file')
                const currentIndex = filesList.findIndex(f => f.id === previewFile.id)
                if (currentIndex > 0) {
                  setPreviewFile(filesList[currentIndex - 1])
                }
              }}
            />
          )}
        </>
      )}

      {/* Hierarchical Move Modal */}
      {hierarchicalMove && (
        <HierarchicalMoveModal
          itemId={hierarchicalMove.itemId}
          itemType={hierarchicalMove.itemType}
          itemName={hierarchicalMove.itemName}
          currentFolderId={hierarchicalMove.currentFolderId}
          onClose={() => setHierarchicalMove(null)}
          onSuccess={async () => {
            await loadFiles()
            setHierarchicalMove(null)
          }}
        />
      )}

      {/* Share Dialog */}
      {showShareDialog && shareDialogFile && (
        <ShareDialog
          isOpen={showShareDialog}
          onClose={() => {
            setShowShareDialog(false)
            setShareDialogFile(null)
          }}
          fileId={shareDialogFile.id}
          fileName={shareDialogFile.name}
        />
      )}

      {/* Share Stats Dialog */}
      {showShareStats && (
        <ShareStatsDialog
          isOpen={showShareStats}
          onClose={() => setShowShareStats(false)}
        />
      )}

    </div>
  )
}

export default Files