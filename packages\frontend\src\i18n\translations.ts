export const translations = {
  'zh-CN': {
    // 通用
    'app.title': '云存储',
    'common.loading': '加载中...',
    'common.save': '保存',
    'common.cancel': '取消',
    'common.confirm': '确认',
    'common.delete': '删除',
    'common.edit': '编辑',
    'common.upload': '上传',
    'common.download': '下载',
    'common.share': '分享',
    'common.search': '搜索',
    'common.settings': '设置',
    'common.logout': '退出登录',

    // 认证
    'auth.login': '登录',
    'auth.register': '注册',
    'auth.email': '邮箱地址',
    'auth.password': '密码',
    'auth.confirmPassword': '确认密码',
    'auth.username': '用户名',
    'auth.rememberMe': '记住我',
    'auth.forgotPassword': '忘记密码？',
    'auth.signIn': '登录',
    'auth.signUp': '注册',
    'auth.createAccount': '创建账户',
    'auth.alreadyHaveAccount': '已有账户？',
    'auth.dontHaveAccount': '没有账户？',
    'auth.signingIn': '登录中...',
    'auth.creatingAccount': '创建账户中...',
    'auth.loginSubtitle': '登录您的账户',
    'auth.registerSubtitle': '创建您的账户',
    'auth.accessFiles': '随时随地访问您的文件',
    'auth.startStoring': '开始安全存储您的文件',
    'auth.chooseUsername': '选择用户名',
    'auth.enterEmail': '输入您的邮箱',
    'auth.createPassword': '创建密码',
    'auth.confirmYourPassword': '确认您的密码',
    'auth.autoLoginRedirect': '您已自动登录，正在跳转到仪表板...',
    'auth.registrationFailed': '注册失败',
    'auth.confirmRevokeAllSessions': '确认撤销所有会话',
    'auth.confirmRevokeAllSessionsMessage': '这将使您从所有设备注销。您需要重新登录。',
    'auth.revokeAll': '撤销全部',
    'auth.allSessionsRevoked': '所有会话已撤销',

    // 导航
    'nav.dashboard': '仪表板',
    'nav.files': '我的文件',
    'nav.shared': '共享文件',
    'nav.recent': '最近文件',
    'nav.trash': '回收站',

    // 仪表板
    'dashboard.title': '仪表板',
    'dashboard.welcome': '欢迎回来，{username}！',
    'dashboard.storageUsage': '存储使用情况',
    'dashboard.used': '已使用',
    'dashboard.myFiles': '我的文件',
    'dashboard.browseFiles': '浏览您的文件',
    'dashboard.imageGallery': '图片库',
    'dashboard.viewImages': '查看您的图片',
    'dashboard.documents': '文档',
    'dashboard.accessDocuments': '访问文档',
    'dashboard.securitySettings': '安全设置',
    'dashboard.twoFactorAuth': '双因素认证',
    'dashboard.enable2FA': '启用双因素认证以增强安全性',
    'dashboard.manage2FA': '管理双因素认证设置',
    'dashboard.sessionManagement': '会话管理',
    'dashboard.manageSessions': '管理您的活跃会话',
    'dashboard.recentFiles': '最近文件',
    'dashboard.modifiedAgo': '{time}前修改',
    'dashboard.enabled': '已启用',
    'dashboard.viewAllFiles': '查看所有文件',

    // 文件管理
    'files.title': '文件',
    'files.fileManagement': '文件管理',
    'files.syncStatus': '同步状态',
    'files.imageBedFolder': '图床',
    'files.newFolder': '新建文件夹',
    'files.uploadFiles': '上传文件',
    'files.selectFiles': '选择文件',
    'files.dragOrClick': '拖拽或点击选择',
    'files.maxSize': '最大',
    'files.supportedTypes': '支持: 图片、文档、视频、音频等',
    'files.cameraUpload': '拍照上传',
    'files.takePhoto': '直接拍摄照片',
    'files.offlineMode': '当前离线模式，文件将保存到本地，联网后自动同步',
    'files.filesToUpload': '待上传文件',
    'files.filesUploadedSuccess': '个文件上传成功',
    'files.filesUploadFailed': '个文件上传失败',
    'files.done': '完成',
    'files.uploadCount': '上传 {count} 个文件',
    'files.pauseUpload': '暂停上传',
    'files.resumeUpload': '继续上传',
    'files.removeFile': '移除文件',
    'files.searchFiles': '搜索文件和文件夹...',
    'files.sortBy': '排序方式',
    'files.name': '名称',
    'files.modified': '修改时间',
    'files.size': '大小',
    'files.type': '类型',
    'files.items': '项',
    'files.item': '项',
    'files.dropToUpload': '拖放文件到此处上传',
    'files.myFiles': '我的文件',
    'files.documents': '文档',
    'files.photos': '照片',
    'files.presentation': '演示文稿',
    'files.vacation': '假期',
    'files.download': '下载',
    'files.rename': '重命名',
    'files.duplicate': '复制',
    'files.move': '移动',
    'files.share': '分享',
    'files.info': '信息',
    'files.delete': '删除',
    'files.selectAll': '全选',
    'files.batchDownload': '批量下载',
    'files.batchMove': '批量移动',
    'files.batchCopy': '批量复制',
    'files.batchShare': '批量分享',
    'files.batchDelete': '批量删除',
    'files.createFolder': '创建文件夹',
    'files.folderName': '文件夹名称',
    'files.enterFolderName': '请输入文件夹名称',
    'files.uploadFile': '上传文件',
    'files.uploadSuccess': '上传成功',
    'files.uploadFailed': '上传失败',
    'files.deleteConfirm': '确认删除',
    'files.deleteMessage': '您确定要删除这些文件吗？此操作无法撤销。',
    'files.loading': '加载中...',
    'files.noFiles': '暂无文件',
    'files.searchPlaceholder': '搜索文件和文件夹...',
    'files.favorite': '收藏',
    'files.edit': '编辑',
    'files.allTypes': '所有类型',
    'files.images': '图片',
    'files.videos': '视频',
    'files.audio': '音频',
    'files.archives': '压缩包',
    'files.selected': '已选择',
    'files.uploadToGetStarted': '上传文件或创建文件夹开始使用',
    'files.folder': '文件夹',
    'files.file': '文件',
    'files.trash': '回收站',
    'files.moveToTrash': '移至回收站',
    'files.restore': '恢复',
    'files.restoreSelected': '恢复选中项',
    'files.restoreSuccess': '恢复成功',
    'files.restoreError': '恢复失败',
    'files.restoreSuccessMessage': '{type} "{name}" 已成功恢复',
    'files.restoreSelectedSuccessMessage': '已成功恢复 {count} 个项目',
    'files.confirmRestoreSelected': '确认恢复选中项',
    'files.confirmRestoreSelectedMessage': '确定要恢复选中的 {count} 个项目吗？',
    'files.emptyTrash': '清空回收站',
    'files.confirmEmptyTrash': '确定要清空回收站吗？此操作无法撤销。',
    'files.confirmEmptyTrashMessage': '确定要清空回收站吗？这将永久删除 {count} 个项目，此操作无法撤销。',
    'files.emptyTrashSuccess': '回收站已清空',
    'files.emptyTrashSuccessMessage': '已永久删除 {count} 个项目',
    'files.emptyTrashError': '清空回收站失败',
    'files.trashEmpty': '回收站为空',
    'files.trashEmptyDescription': '删除的文件和文件夹将出现在这里',
    'files.trashRetentionNotice': '回收站中的文件将在30天后自动删除',
    'files.deletePermanently': '永久删除',
    'files.deleteForever': '永久删除',
    'files.deleteSuccess': '删除成功',
    'files.deleteError': '删除失败',
    'files.permanentDeleteSuccessMessage': '{type} "{name}" 已永久删除',
    'files.permanentDeleteSelectedSuccessMessage': '已永久删除 {count} 个项目',
    'files.advancedSearch': '高级搜索',
    'files.searchFilters': '搜索筛选',
    'files.fileType': '文件类型',
    'files.tags': '标签',
    'files.addTag': '添加标签',
    'files.dateRange': '日期范围',
    'files.from': '从',
    'files.to': '到',
    'files.renameFolder': '重命名文件夹',
    'files.moveItem': '移动项目',
    'files.selectDestination': '选择目标位置',
    'files.rootFolder': '根文件夹',
    'files.noFoldersAvailable': '没有可用的文件夹',
    'files.selectedDestination': '选择的目标',
    'files.enterNewName': '输入新名称',
    'files.moveTo': '移动到...',
    'files.properties': '属性',
    'files.shareLinkCopied': '分享链接已复制到剪贴板',
    'files.moveToImageBed': '移动到图床',
    'files.imageBedDescription': '图床中的图片可以获得公开访问链接',
    'files.publicLinkGenerated': '公开链接已生成',
    'files.copyPublicLink': '复制公开链接',
    'files.onlyImagesAllowed': '只允许上传图片文件',
    'files.onlyImagesAllowedInImageBed': '图床文件夹只允许图片文件，已过滤非图片文件',
    'files.uploadToImageBed': '上传到图床',
    'files.selectImages': '选择图片',
    'files.dropToShare': '拖拽到此处分享',
    'files.dropToTrash': '拖拽到此处删除',
    'common.add': '添加',
    'common.clear': '清除',
    'common.clearAll': '清除全部',
    'common.close': '关闭',
    'common.processing': '处理中...',
    'common.rename': '重命名',
    'common.move': '移动',
    'common.refresh': '刷新',
    'files.trashInfo': '回收站信息',
    'files.backToFiles': '返回文件',
    'files.confirmPermanentDelete': '确定要永久删除此项目吗？此操作无法撤销。',
    'files.confirmPermanentDeleteMessage': '确定要永久删除{type} "{name}" 吗？此操作无法撤销。',
    'files.confirmPermanentDeleteSelected': '确定要永久删除选中的项目吗？此操作无法撤销。',
    'files.confirmPermanentDeleteSelectedMessage': '确定要永久删除选中的 {count} 个项目吗？此操作无法撤销。',
    'files.trashStatistics': '回收站统计',
    'files.totalItems': '总项目',
    'files.files': '文件',
    'files.folders': '文件夹',
    'files.totalSize': '总大小',
    'files.cleanupReminder': '清理提醒',
    'files.cleanupReminderMessage': '您的回收站中有 {count} 个项目。定期清理回收站可以释放存储空间。',
    'files.remindLater': '稍后提醒',
    'files.searchInTrash': '在回收站中搜索...',
    'files.deletedTime': '删除时间',
    'files.allTime': '全部时间',
    'files.today': '今天',
    'files.thisWeek': '本周',
    'files.thisMonth': '本月',
    'files.noSearchResults': '未找到匹配的项目',
    'files.searchResults': '找到 {count} 个结果（共 {total} 个项目）',
    'common.clearFilters': '清除筛选',
    
    // 文件预览
    'preview.title': '文件预览',
    'preview.share': '分享',
    'preview.download': '下载',
    'preview.close': '关闭',
    'preview.previous': '上一个',
    'preview.next': '下一个',
    'preview.loading': '加载中...',
    'preview.error': '预览失败',
    'preview.noPreview': '无法预览此文件',
    'preview.fileInfo': '文件信息',
    'preview.fileName': '文件名',
    'preview.fileSize': '文件大小',
    'preview.fileType': '文件类型',
    'preview.modified': '修改时间',
    'preview.private': '私有',
    'preview.public': '公开',

    // 设置
    'settings.theme': '主题',
    'settings.language': '语言',
    'settings.darkMode': '深色模式',
    'settings.lightMode': '浅色模式',

    // 密码强度
    'password.weak': '弱',
    'password.fair': '一般',
    'password.good': '良好',
    'password.strong': '强',

    // 错误信息
    'error.invalidCredentials': '邮箱或密码错误，请重试。',
    'error.networkError': '网络错误，请检查您的连接。',
    'error.serverError': '服务器错误，请稍后重试。',
    'error.checkConnection': '请检查您的网络连接并重试。',

    // 注册错误信息
    'error.emailAlreadyExists': '该邮箱已被注册，请使用其他邮箱或尝试登录。',
    'error.usernameAlreadyExists': '该用户名已被使用，请选择其他用户名。',
    'error.passwordRequirements': '密码必须至少包含8个字符，一个大写字母，一个小写字母和一个数字。',
    'error.passwordsDoNotMatch': '两次输入的密码不匹配。',
    'error.invalidEmail': '请输入有效的邮箱地址。',
    'error.usernameTooShort': '用户名至少需要3个字符。',
    'error.usernameTooLong': '用户名最多30个字符。',
    'error.usernameInvalidChars': '用户名只能包含字母、数字和下划线。',
    'error.validationError': '请检查您的输入并重试。',
    'error.registrationError': '注册失败，请检查您的信息并重试。',
    'error.loginError': '登录失败，请检查您的凭据。',
    'error.sessionExpired': '会话已过期，请重新登录。',
    'error.passwordTooShort': '密码必须至少8个字符。',
    'error.emailRequired': '邮箱是必填项。',
    'error.usernameRequired': '用户名是必填项。',
    'error.passwordRequired': '密码是必填项。',
    'error.unexpectedError': '发生意外错误，请重试。',
    'error.createFolderFailed': '创建文件夹失败',
    'error.folderNameInvalidChars': '文件夹名称不能包含 / 或 \\',
    'error.imageBedFolderReserved': '"图床"名称为系统图床文件夹保留，请使用其他名称',
    'error.cannotCreateFoldersInImageBed': '图床文件夹内不允许创建子文件夹',
    'error.imageBedOnlyAcceptsImages': '图床文件夹只接受图片文件',
    'error.downloadLimitExceeded': '下载次数已达上限',
    'error.tooManyRequests': '请求过于频繁，请稍后再试',
    'error.notFound': '请求的资源未找到',
    'error.unauthorized': '未授权访问',
    'error.forbidden': '访问被禁止',

    // 成功信息
    'success.loginSuccess': '登录成功！',
    'success.registerSuccess': '注册成功！',
    'success.fileUploaded': '文件上传成功！',

    // 占位符
    'placeholder.searchFiles': '搜索文件...',
    'placeholder.enterEmail': '输入您的邮箱',
    'placeholder.enterPassword': '输入您的密码',
    'placeholder.chooseUsername': '选择用户名',

    // 密码重置
    'passwordReset.title': '重置密码',
    'passwordReset.setNewPassword': '设置新密码',
    'passwordReset.enterEmail': '输入您的邮箱以接收重置链接',
    'passwordReset.enterNewPassword': '在下方输入您的新密码',
    'passwordReset.emailAddress': '邮箱地址',
    'passwordReset.newPassword': '新密码',
    'passwordReset.confirmNewPassword': '确认新密码',
    'passwordReset.sendResetLink': '发送重置链接',
    'passwordReset.updatePassword': '更新密码',
    'passwordReset.backToLogin': '返回登录',
    'passwordReset.passwordsNotMatch': '密码不匹配',
    'passwordReset.passwordTooShort': '密码长度至少8位',
    'passwordReset.enterEmailAddress': '请输入您的邮箱地址',
    'passwordReset.resetSuccess': '密码重置成功。您现在可以使用新密码登录。',
    'passwordReset.emailSent': '如果该邮箱存在账户，我们已向您发送密码重置链接。',
    'passwordReset.error': '发生错误，请重试。',
    'passwordReset.passwordMinLength': '至少8个字符',

    // 双因素认证
    'twoFactor.setup': '设置双因素认证',
    'twoFactor.disabled': '禁用双因素认证',
    'twoFactor.enabled': '双因素认证已启用',
    'twoFactor.scanQR': '扫描二维码',
    'twoFactor.manualEntry': '手动输入',
    'twoFactor.secretKey': '密钥',
    'twoFactor.verificationCode': '验证码',
    'twoFactor.enterCode': '输入6位验证码',
    'twoFactor.verify': '验证',
    'twoFactor.enable': '启用',
    'twoFactor.disable': '禁用',
    'twoFactor.backToDashboard': '返回仪表板',
    'twoFactor.copySecret': '复制密钥',
    'twoFactor.copied': '已复制',
    'twoFactor.enterPassword': '输入密码以禁用双因素认证',
    'twoFactor.confirmDisable': '确认禁用',

    // 文件分享
    'share.title': '分享文件',
    'share.fileSharing': '文件分享',
    'share.createNew': '创建新分享链接',
    'share.editExisting': '编辑分享链接',
    'share.createTitle': '创建分享链接',
    'share.editTitle': '编辑分享链接',
    'share.permissions.title': '权限',
    'share.permissions.view': '仅查看',
    'share.permissions.edit': '可编辑',
    'share.permissions.full': '完全访问',
    'share.expiration': '过期时间（可选）',
    'share.password': '密码保护（可选）',
    'share.passwordPlaceholder': '输入密码',
    'share.passwordHint': '清空此字段可移除密码保护，或输入新密码进行修改',
    'share.downloadLimit': '下载限制（可选）',
    'share.downloadLimitPlaceholder': '最大下载次数',
    'share.downloadLimitHint': '留空表示不限制下载次数',
    'share.create': '创建分享链接',
    'share.update': '更新分享链接',
    'share.updated': '分享链接更新成功',
    'share.updateError': '更新分享链接失败',
    'share.existing': '现有分享链接',
    'share.noShares': '尚未创建分享链接',
    'share.created': '创建时间',
    'share.expires': '过期时间',
    'share.downloads': '下载次数',
    'share.copy': '复制链接',
    'share.revoke': '撤销',
    'share.copied': '分享链接已复制到剪贴板',
    'share.loadError': '加载分享链接失败',
    'share.createError': '创建分享链接失败',
    'share.copyError': '复制链接失败',
    'share.revokeConfirm': '确定要撤销此分享链接吗？',
    'share.revokeConfirmMessage': '确定要撤销此分享链接吗？此操作无法撤销。',
    'share.revoked': '分享链接已撤销',
    'share.revokeError': '撤销分享链接失败',
    'share.stats': '分享统计',

    // 分享统计
    'shareStats.title': '分享统计',
    'shareStats.totalShares': '总分享数',
    'shareStats.activeShares': '活跃分享',
    'shareStats.totalDownloads': '总下载数',
    'shareStats.downloadsChart': '下载趋势',
    'shareStats.recentShares': '最近分享',
    'shareStats.created': '创建时间',
    'shareStats.downloads': '下载',
    'shareStats.noData': '暂无分享数据',
    'shareStats.noDataDesc': '开始分享文件以查看统计信息',
    'shareStats.loadError': '加载分享统计失败',

    // 公开分享页面
    'publicShare.title': '分享文件',
    'publicShare.subtitle': '有人与您分享了这个文件',
    'publicShare.size': '大小',
    'publicShare.type': '类型',
    'publicShare.uploaded': '上传时间',
    'publicShare.downloads': '下载次数',
    'publicShare.downloadLimitReached': '已达到下载限制',
    'publicShare.preview': '预览',
    'publicShare.download': '下载',
    'publicShare.passwordRequired': '需要密码',
    'publicShare.passwordDesc': '此分享文件受密码保护',
    'publicShare.enterPassword': '输入密码',
    'publicShare.unlock': '解锁',
    'publicShare.error': '访问错误',
    'publicShare.notFound': '找不到分享文件',

    // WebSocket状态
    'websocket.connected': '已连接',
    'websocket.disconnected': '连接断开',
    'websocket.connecting': '连接中...',
    'websocket.reconnecting': '重新连接中...',
  },
  'en': {
    // Common
    'app.title': 'Cloud Storage',
    'common.loading': 'Loading...',
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'common.confirm': 'Confirm',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.upload': 'Upload',
    'common.download': 'Download',
    'common.share': 'Share',
    'common.search': 'Search',
    'common.settings': 'Settings',
    'common.logout': 'Logout',

    // Authentication
    'auth.login': 'Login',
    'auth.register': 'Register',
    'auth.email': 'Email address',
    'auth.password': 'Password',
    'auth.confirmPassword': 'Confirm Password',
    'auth.username': 'Username',
    'auth.rememberMe': 'Remember me',
    'auth.forgotPassword': 'Forgot your password?',
    'auth.signIn': 'Sign in',
    'auth.signUp': 'Sign up',
    'auth.createAccount': 'Create Account',
    'auth.alreadyHaveAccount': 'Already have an account?',
    'auth.dontHaveAccount': "Don't have an account?",
    'auth.signingIn': 'Signing in...',
    'auth.creatingAccount': 'Creating account...',
    'auth.loginSubtitle': 'Sign in to your account',
    'auth.registerSubtitle': 'Create your account',
    'auth.accessFiles': 'Access your files from anywhere',
    'auth.startStoring': 'Start storing your files securely',
    'auth.chooseUsername': 'Choose a username',
    'auth.enterEmail': 'Enter your email',
    'auth.createPassword': 'Create a password',
    'auth.confirmYourPassword': 'Confirm your password',
    'auth.autoLoginRedirect': 'You have been automatically logged in. Redirecting to dashboard...',
    'auth.registrationFailed': 'Registration Failed',
    'auth.confirmRevokeAllSessions': 'Confirm Revoke All Sessions',
    'auth.confirmRevokeAllSessionsMessage': 'This will log you out from all devices. You will need to log in again.',
    'auth.revokeAll': 'Revoke All',
    'auth.allSessionsRevoked': 'All sessions revoked',

    // Navigation
    'nav.dashboard': 'Dashboard',
    'nav.files': 'My Files',
    'nav.shared': 'Shared',
    'nav.recent': 'Recent',
    'nav.trash': 'Trash',

    // Dashboard
    'dashboard.title': 'Dashboard',
    'dashboard.welcome': 'Welcome back, {username}!',
    'dashboard.storageUsage': 'Storage Usage',
    'dashboard.used': 'Used',
    'dashboard.myFiles': 'My Files',
    'dashboard.browseFiles': 'Browse your files',
    'dashboard.imageGallery': 'Image Gallery',
    'dashboard.viewImages': 'View your images',
    'dashboard.documents': 'Documents',
    'dashboard.accessDocuments': 'Access documents',
    'dashboard.securitySettings': 'Security Settings',
    'dashboard.twoFactorAuth': 'Two-Factor Authentication',
    'dashboard.enable2FA': 'Enable 2FA for extra security',
    'dashboard.manage2FA': 'Manage 2FA settings',
    'dashboard.sessionManagement': 'Session Management',
    'dashboard.manageSessions': 'Manage your active sessions',
    'dashboard.recentFiles': 'Recent Files',
    'dashboard.modifiedAgo': 'Modified {time} ago',
    'dashboard.enabled': 'Enabled',
    'dashboard.viewAllFiles': 'View All Files',

    // File Management
    'files.title': 'Files',
    'files.fileManagement': 'File Management',
    'files.syncStatus': 'Sync Status',
    'files.imageBedFolder': 'Image Bed',
    'files.newFolder': 'New Folder',
    'files.uploadFiles': 'Upload Files',
    'files.selectFiles': 'Select Files',
    'files.dragOrClick': 'Drag or click to select',
    'files.maxSize': 'Max',
    'files.supportedTypes': 'Supported: Images, Documents, Videos, Audio, etc.',
    'files.cameraUpload': 'Camera Upload',
    'files.takePhoto': 'Take photo directly',
    'files.offlineMode': 'Currently offline, files will be saved locally and synced when online',
    'files.filesToUpload': 'Files to upload',
    'files.filesUploadedSuccess': 'files uploaded successfully',
    'files.filesUploadFailed': 'files failed to upload',
    'files.done': 'Done',
    'files.uploadCount': 'Upload {count} files',
    'files.pauseUpload': 'Pause upload',
    'files.resumeUpload': 'Resume upload',
    'files.removeFile': 'Remove file',
    'files.searchFiles': 'Search files and folders...',
    'files.sortBy': 'Sort by',
    'files.name': 'Name',
    'files.modified': 'Modified',
    'files.size': 'Size',
    'files.type': 'Type',
    'files.items': 'items',
    'files.item': 'item',
    'files.dropToUpload': 'Drop files here to upload',
    'files.myFiles': 'My Files',
    'files.documents': 'Documents',
    'files.photos': 'Photos',
    'files.presentation': 'Presentation',
    'files.vacation': 'Vacation',
    'files.download': 'Download',
    'files.rename': 'Rename',
    'files.duplicate': 'Duplicate',
    'files.move': 'Move',
    'files.share': 'Share',
    'files.info': 'Info',
    'files.delete': 'Delete',
    'files.selectAll': 'Select All',
    'files.batchDownload': 'Batch Download',
    'files.batchMove': 'Batch Move',
    'files.batchCopy': 'Batch Copy',
    'files.batchShare': 'Batch Share',
    'files.batchDelete': 'Batch Delete',
    'files.createFolder': 'Create Folder',
    'files.folderName': 'Folder Name',
    'files.enterFolderName': 'Please enter folder name',
    'files.uploadFile': 'Upload File',
    'files.uploadSuccess': 'Upload Success',
    'files.uploadFailed': 'Upload Failed',
    'files.deleteConfirm': 'Confirm Delete',
    'files.deleteMessage': 'Are you sure you want to delete these files? This action cannot be undone.',
    'files.loading': 'Loading...',
    'files.noFiles': 'No files',
    'files.searchPlaceholder': 'Search files and folders...',
    'files.favorite': 'Favorite',
    'files.edit': 'Edit',
    'files.allTypes': 'All Types',
    'files.images': 'Images',
    'files.videos': 'Videos',
    'files.audio': 'Audio',
    'files.archives': 'Archives',
    'files.selected': 'selected',
    'files.uploadToGetStarted': 'Upload files or create folders to get started',
    'files.folder': 'Folder',
    'files.file': 'File',
    'files.trash': 'Trash',
    'files.moveToTrash': 'Move to Trash',
    'files.restore': 'Restore',
    'files.restoreSelected': 'Restore Selected',
    'files.restoreSuccess': 'Restore Successful',
    'files.restoreError': 'Restore Failed',
    'files.restoreSuccessMessage': '{type} "{name}" has been successfully restored',
    'files.restoreSelectedSuccessMessage': 'Successfully restored {count} items',
    'files.confirmRestoreSelected': 'Confirm Restore Selected',
    'files.confirmRestoreSelectedMessage': 'Are you sure you want to restore the selected {count} items?',
    'files.emptyTrash': 'Empty Trash',
    'files.confirmEmptyTrash': 'Are you sure you want to empty the trash? This action cannot be undone.',
    'files.confirmEmptyTrashMessage': 'Are you sure you want to empty the trash? This will permanently delete {count} items and cannot be undone.',
    'files.emptyTrashSuccess': 'Trash Emptied',
    'files.emptyTrashSuccessMessage': 'Permanently deleted {count} items',
    'files.emptyTrashError': 'Failed to Empty Trash',
    'files.trashEmpty': 'Trash is empty',
    'files.trashEmptyDescription': 'Deleted files and folders will appear here',
    'files.trashRetentionNotice': 'Files in trash will be automatically deleted after 30 days',
    'files.deletePermanently': 'Delete Permanently',
    'files.deleteForever': 'Delete Forever',
    'files.deleteSuccess': 'Delete Successful',
    'files.deleteError': 'Delete Failed',
    'files.permanentDeleteSuccessMessage': '{type} "{name}" has been permanently deleted',
    'files.permanentDeleteSelectedSuccessMessage': 'Permanently deleted {count} items',
    'files.advancedSearch': 'Advanced Search',
    'files.searchFilters': 'Search Filters',
    'files.fileType': 'File Type',
    'files.tags': 'Tags',
    'files.addTag': 'Add Tag',
    'files.dateRange': 'Date Range',
    'files.from': 'From',
    'files.to': 'To',
    'files.renameFolder': 'Rename Folder',
    'files.moveItem': 'Move Item',
    'files.selectDestination': 'Select Destination',
    'files.rootFolder': 'Root Folder',
    'files.noFoldersAvailable': 'No folders available',
    'files.selectedDestination': 'Selected destination',
    'files.enterNewName': 'Enter new name',
    'files.moveTo': 'Move to...',
    'files.properties': 'Properties',
    'files.shareLinkCopied': 'Share link copied to clipboard',
    'files.moveToImageBed': 'Move to Image Bed',
    'files.imageBedDescription': 'Images in the image bed can get public access links',
    'files.publicLinkGenerated': 'Public link generated',
    'files.copyPublicLink': 'Copy Public Link',
    'files.onlyImagesAllowed': 'Only image files are allowed',
    'files.onlyImagesAllowedInImageBed': 'Image bed folder only allows image files, non-image files have been filtered',
    'files.uploadToImageBed': 'Upload to Image Bed',
    'files.selectImages': 'Select Images',
    'files.dropToShare': 'Drop here to share',
    'files.dropToTrash': 'Drop here to delete',
    'common.add': 'Add',
    'common.clear': 'Clear',
    'common.clearAll': 'Clear All',
    'common.close': 'Close',
    'common.processing': 'Processing...',
    'common.rename': 'Rename',
    'common.move': 'Move',
    'common.refresh': 'Refresh',
    'files.trashInfo': 'Trash Information',
    'files.backToFiles': 'Back to Files',
    'files.confirmPermanentDelete': 'Are you sure you want to permanently delete this item? This action cannot be undone.',
    'files.confirmPermanentDeleteMessage': 'Are you sure you want to permanently delete {type} "{name}"? This action cannot be undone.',
    'files.confirmPermanentDeleteSelected': 'Are you sure you want to permanently delete the selected items? This action cannot be undone.',
    'files.confirmPermanentDeleteSelectedMessage': 'Are you sure you want to permanently delete the selected {count} items? This action cannot be undone.',
    'files.trashStatistics': 'Trash Statistics',
    'files.totalItems': 'Total Items',
    'files.files': 'Files',
    'files.folders': 'Folders',
    'files.totalSize': 'Total Size',
    'files.cleanupReminder': 'Cleanup Reminder',
    'files.cleanupReminderMessage': 'You have {count} items in your trash. Regular cleanup helps free up storage space.',
    'files.remindLater': 'Remind Later',
    'files.searchInTrash': 'Search in trash...',
    'files.deletedTime': 'Deleted Time',
    'files.allTime': 'All Time',
    'files.today': 'Today',
    'files.thisWeek': 'This Week',
    'files.thisMonth': 'This Month',
    'files.noSearchResults': 'No matching items found',
    'files.searchResults': 'Found {count} results out of {total} items',
    'common.clearFilters': 'Clear Filters',
    
    // File Preview
    'preview.title': 'File Preview',
    'preview.share': 'Share',
    'preview.download': 'Download',
    'preview.close': 'Close',
    'preview.previous': 'Previous',
    'preview.next': 'Next',
    'preview.loading': 'Loading...',
    'preview.error': 'Preview failed',
    'preview.noPreview': 'Cannot preview this file',
    'preview.fileInfo': 'File Info',
    'preview.fileName': 'File Name',
    'preview.fileSize': 'File Size',
    'preview.fileType': 'File Type',
    'preview.modified': 'Modified',
    'preview.private': 'Private',
    'preview.public': 'Public',

    // Settings
    'settings.theme': 'Theme',
    'settings.language': 'Language',
    'settings.darkMode': 'Dark Mode',
    'settings.lightMode': 'Light Mode',

    // Password strength
    'password.weak': 'Weak',
    'password.fair': 'Fair',
    'password.good': 'Good',
    'password.strong': 'Strong',

    // Error messages
    'error.invalidCredentials': 'Invalid email or password. Please try again.',
    'error.networkError': 'Network error. Please check your connection.',
    'error.serverError': 'Server error. Please try again later.',
    'error.checkConnection': 'Please check your internet connection and try again.',

    // Registration error messages
    'error.emailAlreadyExists': 'An account with this email already exists. Please use a different email or try logging in.',
    'error.usernameAlreadyExists': 'This username is already taken. Please choose a different username.',
    'error.passwordRequirements': 'Password must contain at least 8 characters, one uppercase letter, one lowercase letter, and one number.',
    'error.passwordsDoNotMatch': 'Passwords do not match.',
    'error.invalidEmail': 'Please enter a valid email address.',
    'error.usernameTooShort': 'Username must be at least 3 characters long.',
    'error.usernameTooLong': 'Username must be at most 30 characters long.',
    'error.usernameInvalidChars': 'Username can only contain letters, numbers, and underscores.',
    'error.validationError': 'Please check your input and try again.',
    'error.registrationError': 'Registration failed. Please check your information and try again.',
    'error.loginError': 'Login failed. Please check your credentials.',
    'error.sessionExpired': 'Session expired. Please log in again.',
    'error.passwordTooShort': 'Password must be at least 8 characters long.',
    'error.emailRequired': 'Email is required.',
    'error.usernameRequired': 'Username is required.',
    'error.passwordRequired': 'Password is required.',
    'error.unexpectedError': 'An unexpected error occurred. Please try again.',
    'error.createFolderFailed': 'Failed to create folder',
    'error.folderNameInvalidChars': 'Folder name cannot contain / or \\',
    'error.imageBedFolderReserved': 'The name "Image Bed" is reserved for the system image bed folder',
    'error.cannotCreateFoldersInImageBed': 'Cannot create folders inside the image bed folder',
    'error.imageBedOnlyAcceptsImages': 'Image bed folder only accepts image files',
    'error.downloadLimitExceeded': 'Download limit exceeded',
    'error.tooManyRequests': 'Too many requests, please try again later',
    'error.notFound': 'Requested resource not found',
    'error.unauthorized': 'Unauthorized access',
    'error.forbidden': 'Access forbidden',

    // Success messages
    'success.loginSuccess': 'Login successful!',
    'success.registerSuccess': 'Registration successful!',
    'success.fileUploaded': 'File uploaded successfully!',

    // Placeholders
    'placeholder.searchFiles': 'Search files...',
    'placeholder.enterEmail': 'Enter your email',
    'placeholder.enterPassword': 'Enter your password',
    'placeholder.chooseUsername': 'Choose a username',

    // Password Reset
    'passwordReset.title': 'Reset Password',
    'passwordReset.setNewPassword': 'Set New Password',
    'passwordReset.enterEmail': 'Enter your email to receive a reset link',
    'passwordReset.enterNewPassword': 'Enter your new password below',
    'passwordReset.emailAddress': 'Email address',
    'passwordReset.newPassword': 'New Password',
    'passwordReset.confirmNewPassword': 'Confirm New Password',
    'passwordReset.sendResetLink': 'Send Reset Link',
    'passwordReset.updatePassword': 'Update Password',
    'passwordReset.backToLogin': 'Back to Login',
    'passwordReset.passwordsNotMatch': 'Passwords do not match',
    'passwordReset.passwordTooShort': 'Password must be at least 8 characters long',
    'passwordReset.enterEmailAddress': 'Please enter your email address',
    'passwordReset.resetSuccess': 'Password has been reset successfully. You can now log in with your new password.',
    'passwordReset.emailSent': 'If an account with that email exists, we\'ve sent you a password reset link.',
    'passwordReset.error': 'An error occurred. Please try again.',
    'passwordReset.passwordMinLength': 'Must be at least 8 characters long',

    // Two-Factor Authentication
    'twoFactor.setup': 'Setup Two-Factor Authentication',
    'twoFactor.disabled': 'Disable Two-Factor Authentication',
    'twoFactor.enabled': 'Two-Factor Authentication Enabled',
    'twoFactor.scanQR': 'Scan QR Code',
    'twoFactor.manualEntry': 'Manual Entry',
    'twoFactor.secretKey': 'Secret Key',
    'twoFactor.verificationCode': 'Verification Code',
    'twoFactor.enterCode': 'Enter 6-digit code',
    'twoFactor.verify': 'Verify',
    'twoFactor.enable': 'Enable',
    'twoFactor.disable': 'Disable',
    'twoFactor.backToDashboard': 'Back to Dashboard',
    'twoFactor.copySecret': 'Copy Secret',
    'twoFactor.copied': 'Copied',
    'twoFactor.enterPassword': 'Enter your password to disable two-factor authentication',
    'twoFactor.confirmDisable': 'Confirm Disable',

    // File Sharing
    'share.title': 'Share File',
    'share.fileSharing': 'File Sharing',
    'share.createNew': 'Create New Share Link',
    'share.editExisting': 'Edit Share Link',
    'share.createTitle': 'Create Share Link',
    'share.editTitle': 'Edit Share Link',
    'share.permissions.title': 'Permissions',
    'share.permissions.view': 'View Only',
    'share.permissions.edit': 'Can Edit',
    'share.permissions.full': 'Full Access',
    'share.expiration': 'Expiration (Optional)',
    'share.password': 'Password Protection (Optional)',
    'share.passwordPlaceholder': 'Enter password',
    'share.passwordHint': 'Clear the field to remove password protection, or enter a new password to change it',
    'share.downloadLimit': 'Download Limit (Optional)',
    'share.downloadLimitPlaceholder': 'Max downloads',
    'share.downloadLimitHint': 'Leave empty for unlimited downloads',
    'share.create': 'Create Share Link',
    'share.update': 'Update Share Link',
    'share.updated': 'Share link updated successfully',
    'share.updateError': 'Failed to update share link',
    'share.existing': 'Existing Share Links',
    'share.noShares': 'No share links created yet',
    'share.created': 'Created',
    'share.expires': 'Expires',
    'share.downloads': 'downloads',
    'share.copy': 'Copy Link',
    'share.revoke': 'Revoke',
    'share.copied': 'Share link copied to clipboard',
    'share.loadError': 'Failed to load share links',
    'share.createError': 'Failed to create share link',
    'share.copyError': 'Failed to copy link',
    'share.revokeConfirm': 'Are you sure you want to revoke this share link?',
    'share.revokeConfirmMessage': 'Are you sure you want to revoke this share link? This action cannot be undone.',
    'share.revoked': 'Share link revoked successfully',
    'share.revokeError': 'Failed to revoke share link',
    'share.stats': 'Share Stats',

    // Share Statistics
    'shareStats.title': 'Share Statistics',
    'shareStats.totalShares': 'Total Shares',
    'shareStats.activeShares': 'Active Shares',
    'shareStats.totalDownloads': 'Total Downloads',
    'shareStats.downloadsChart': 'Downloads Over Time',
    'shareStats.recentShares': 'Recent Shares',
    'shareStats.created': 'Created',
    'shareStats.downloads': 'downloads',
    'shareStats.noData': 'No Share Data',
    'shareStats.noDataDesc': 'Start sharing files to see statistics here',
    'shareStats.loadError': 'Failed to load share statistics',

    // Public Share Page
    'publicShare.title': 'Shared File',
    'publicShare.subtitle': 'Someone shared this file with you',
    'publicShare.size': 'Size',
    'publicShare.type': 'Type',
    'publicShare.uploaded': 'Uploaded',
    'publicShare.downloads': 'Downloads',
    'publicShare.downloadLimitReached': 'Download limit reached',
    'publicShare.preview': 'Preview',
    'publicShare.download': 'Download',
    'publicShare.passwordRequired': 'Password Required',
    'publicShare.passwordDesc': 'This shared file is password protected',
    'publicShare.enterPassword': 'Enter password',
    'publicShare.unlock': 'Unlock',
    'publicShare.error': 'Access Error',
    'publicShare.notFound': 'Shared file not found',

    // WebSocket status
    'websocket.connected': 'Connected',
    'websocket.disconnected': 'Disconnected',
    'websocket.connecting': 'Connecting...',
    'websocket.reconnecting': 'Reconnecting...',
  }
}

export type TranslationKey = keyof typeof translations['zh-CN']