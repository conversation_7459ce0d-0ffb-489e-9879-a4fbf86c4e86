import React, { useState, useEffect, useCallback } from 'react'
import { useI18n } from '../contexts/I18nContext'
import { showConfirmDialog, showNotification } from '../contexts/ToastContext'
import { fileService } from '../services/fileService'
import { FileList as FileListType } from '@cloud-storage/shared'
import { useMobile } from '../hooks/useMobile'
import Button from '../components/ui/Button'
import FileList from '../components/files/FileList'
import MobileFileList from '../components/files/MobileFileList'
import FileContextMenu from '../components/files/FileContextMenu'
import TrashOperations from '../components/files/TrashOperations'
import TrashStats from '../components/files/TrashStats'
import TrashCleanupReminder from '../components/files/TrashCleanupReminder'
import TrashSearch, { TrashSearchFilters } from '../components/files/TrashSearch'
import { 
  TrashIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'

export interface FileItem {
  id: string
  name: string
  type: 'file' | 'folder'
  size?: number
  mimeType?: string
  modifiedAt: Date
  isShared?: boolean
  thumbnailUrl?: string
}

const Trash: React.FC = () => {
  const { t } = useI18n()
  const { isMobile } = useMobile()
  const [trashContents, setTrashContents] = useState<FileListType | null>(null)
  const [loading, setLoading] = useState(false)
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set())
  const [error, setError] = useState<string | null>(null)
  const [contextMenu, setContextMenu] = useState<{
    file: FileItem
    position: { x: number; y: number }
  } | null>(null)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list')
  const [searchQuery, setSearchQuery] = useState('')
  const [searchFilters, setSearchFilters] = useState<TrashSearchFilters>({})
  const [filteredItems, setFilteredItems] = useState<FileItem[]>([])
  const [isSearching, setIsSearching] = useState(false)

  const loadTrashContents = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      const contents = await fileService.getTrashContents({ page: 1, limit: 100 })
      setTrashContents(contents)
    } catch (error) {
      console.error('Failed to load trash contents:', error)
      setError('Failed to load trash contents. Please try again.')
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    loadTrashContents()
  }, [loadTrashContents])

  const handleRestore = async (fileId: string) => {
    try {
      setLoading(true)
      setError(null)

      // Find the file to get its name for the notification
      const fileToRestore = trashContents?.files.find(f => f.id === fileId)
      const fileName = fileToRestore?.originalName || 'Unknown file'
      const fileType = fileToRestore?.mimeType?.startsWith('folder') ? 'folder' : 'file'

      await fileService.restoreFromTrash(fileId)
      await loadTrashContents() // Reload trash contents

      setSelectedFiles(prev => {
        const newSet = new Set(prev)
        newSet.delete(fileId)
        return newSet
      })

      // Show success notification
      showNotification(
        'success',
        t('files.restoreSuccess'),
        t('files.restoreSuccessMessage', {
          name: fileName,
          type: fileType === 'folder' ? t('files.folder') : t('files.file')
        })
      )
    } catch (error: any) {
      console.error('Failed to restore file:', error)
      const errorMessage = error?.response?.data?.error || error?.message || t('files.restoreError')
      setError(errorMessage)
      showNotification('error', t('files.restoreError'), errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleRestoreSelected = async () => {
    if (selectedFiles.size === 0) return

    try {
      setLoading(true)
      setError(null)

      const restorePromises = Array.from(selectedFiles).map(fileId =>
        fileService.restoreFromTrash(fileId)
      )
      await Promise.all(restorePromises)
      await loadTrashContents()

      const restoredCount = selectedFiles.size
      setSelectedFiles(new Set())

      // Show success notification
      showNotification(
        'success',
        t('files.restoreSuccess'),
        t('files.restoreSelectedSuccessMessage', { count: restoredCount.toString() })
      )
    } catch (error: any) {
      console.error('Failed to restore files:', error)
      const errorMessage = error?.response?.data?.error || error?.message || t('files.restoreError')
      setError(errorMessage)
      showNotification('error', t('files.restoreError'), errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleDeletePermanently = async (fileId: string) => {
    const fileToDelete = trashContents?.files.find(f => f.id === fileId)
    const fileName = fileToDelete?.originalName || 'Unknown file'
    const fileType = fileToDelete?.mimeType?.startsWith('folder') ? 'folder' : 'file'

    const confirmed = await showConfirmDialog({
      title: t('files.confirmPermanentDelete'),
      message: t('files.confirmPermanentDeleteMessage', {
        name: fileName,
        type: fileType === 'folder' ? t('files.folder') : t('files.file')
      }),
      confirmText: t('files.deleteForever'),
      cancelText: t('common.cancel'),
      type: 'danger',
      onConfirm: () => {},
      onCancel: () => {}
    })

    if (!confirmed) return

    try {
      setLoading(true)
      setError(null)

      // Use the new smart delete function
      await fileService.deleteItemPermanently(fileId, fileType as 'file' | 'folder')
      await loadTrashContents()
      setSelectedFiles(prev => {
        const newSet = new Set(prev)
        newSet.delete(fileId)
        return newSet
      })

      // Show success notification
      showNotification(
        'success',
        t('files.deleteSuccess'),
        t('files.permanentDeleteSuccessMessage', {
          name: fileName,
          type: fileType === 'folder' ? t('files.folder') : t('files.file')
        })
      )
    } catch (error: any) {
      console.error('Failed to delete file permanently:', error)
      const errorMessage = error?.response?.data?.error || error?.message || t('files.deleteError')
      setError(errorMessage)
      showNotification('error', t('files.deleteError'), errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteSelectedPermanently = async () => {
    if (selectedFiles.size === 0) return

    const confirmed = await showConfirmDialog({
      title: t('files.confirmPermanentDeleteSelected'),
      message: t('files.confirmPermanentDeleteSelectedMessage', { count: selectedFiles.size.toString() }),
      confirmText: t('files.deleteForever'),
      cancelText: t('common.cancel'),
      type: 'danger',
      onConfirm: () => {},
      onCancel: () => {}
    })

    if (!confirmed) return

    try {
      setLoading(true)
      setError(null)

      // Smart delete: determine type for each item and use appropriate endpoint
      const deletePromises = Array.from(selectedFiles).map(fileId => {
        const item = trashContents?.files.find(f => f.id === fileId)
        const itemType = item?.mimeType?.startsWith('folder') ? 'folder' : 'file'
        return fileService.deleteItemPermanently(fileId, itemType as 'file' | 'folder')
      })

      await Promise.all(deletePromises)
      await loadTrashContents()

      const deletedCount = selectedFiles.size
      setSelectedFiles(new Set())

      // Show success notification
      showNotification(
        'success',
        t('files.deleteSuccess'),
        t('files.permanentDeleteSelectedSuccessMessage', { count: deletedCount.toString() })
      )
    } catch (error: any) {
      console.error('Failed to delete files permanently:', error)
      const errorMessage = error?.response?.data?.error || error?.message || t('files.deleteError')
      setError(errorMessage)
      showNotification('error', t('files.deleteError'), errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleEmptyTrash = async () => {
    const itemCount = (trashContents?.files.length || 0) + (trashContents?.folders.length || 0)
    if (itemCount === 0) return

    const confirmed = await showConfirmDialog({
      title: t('files.confirmEmptyTrash'),
      message: t('files.confirmEmptyTrashMessage', { count: itemCount.toString() }),
      confirmText: t('files.emptyTrash'),
      cancelText: t('common.cancel'),
      type: 'danger',
      onConfirm: () => {},
      onCancel: () => {}
    })

    if (!confirmed) return

    try {
      setLoading(true)
      setError(null)
      const result = await fileService.emptyTrash()
      await loadTrashContents()
      setSelectedFiles(new Set())

      // Show success notification
      showNotification(
        'success',
        t('files.emptyTrashSuccess'),
        t('files.emptyTrashSuccessMessage', { count: (result.deletedCount || 0).toString() })
      )
    } catch (error: any) {
      console.error('Failed to empty trash:', error)
      const errorMessage = error?.response?.data?.error || error?.message || t('files.emptyTrashError')
      setError(errorMessage)
      showNotification('error', t('files.emptyTrashError'), errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleFileSelect = (fileId: string, selected: boolean) => {
    setSelectedFiles(prev => {
      const newSet = new Set(prev)
      if (selected) {
        newSet.add(fileId)
      } else {
        newSet.delete(fileId)
      }
      return newSet
    })
  }

  const handleSelectAll = () => {
    if (!trashContents) return

    const allFiles = [...trashContents.files, ...trashContents.folders]
    if (selectedFiles.size === allFiles.length) {
      setSelectedFiles(new Set())
    } else {
      setSelectedFiles(new Set(allFiles.map(f => f.id)))
    }
  }

  const handleContextMenu = (file: FileItem, position?: { x: number; y: number }) => {
    if (position) {
      setContextMenu({ file, position })
    } else {
      // For mobile, show context menu at center of screen
      setContextMenu({
        file,
        position: { x: window.innerWidth / 2, y: window.innerHeight / 2 }
      })
    }
  }

  const handleContextMenuEvent = (e: React.MouseEvent, file: FileItem) => {
    e.preventDefault()
    handleContextMenu(file, { x: e.clientX, y: e.clientY })
  }

  const handleContextAction = (action: string, file: FileItem) => {
    setContextMenu(null)
    
    switch (action) {
      case 'restore':
        handleRestore(file.id)
        break
      case 'delete':
        handleDeletePermanently(file.id)
        break
    }
  }

  // Convert trash contents to FileItem format for display
  const trashItems: FileItem[] = trashContents ? [
    ...trashContents.folders.map(folder => ({
      id: folder.id,
      name: folder.name,
      type: 'folder' as const,
      modifiedAt: new Date(folder.deletedAt || folder.modifiedAt),
      isShared: false
    })),
    ...trashContents.files.map(file => ({
      id: file.id,
      name: file.originalName || file.filename,
      type: 'file' as const,
      size: file.size,
      mimeType: file.mimeType,
      modifiedAt: new Date(file.deletedAt || file.modifiedAt),
      isShared: file.isPublic
    }))
  ] : []

  const handleSearch = (query: string, filters: TrashSearchFilters) => {
    setSearchQuery(query)
    setSearchFilters(filters)
    setIsSearching(true)

    let filtered = [...trashItems]

    // Filter by search query
    if (query.trim()) {
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(query.toLowerCase())
      )
    }

    // Filter by file type
    if (filters.fileType) {
      if (filters.fileType === 'folder') {
        filtered = filtered.filter(item => item.type === 'folder')
      } else {
        filtered = filtered.filter(item => {
          if (item.type === 'folder') return false
          if (!item.mimeType) return false
          
          switch (filters.fileType) {
            case 'image':
              return item.mimeType.startsWith('image/')
            case 'video':
              return item.mimeType.startsWith('video/')
            case 'audio':
              return item.mimeType.startsWith('audio/')
            case 'document':
              return item.mimeType.includes('pdf') || 
                     item.mimeType.includes('document') || 
                     item.mimeType.includes('text/')
            case 'archive':
              return item.mimeType.includes('zip') || 
                     item.mimeType.includes('rar') || 
                     item.mimeType.includes('archive')
            default:
              return true
          }
        })
      }
    }

    // Filter by date range
    if (filters.dateRange && filters.dateRange !== 'all') {
      const now = new Date()
      const filterDate = new Date()
      
      switch (filters.dateRange) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0)
          break
        case 'week':
          filterDate.setDate(now.getDate() - 7)
          break
        case 'month':
          filterDate.setMonth(now.getMonth() - 1)
          break
      }
      
      filtered = filtered.filter(item => item.modifiedAt >= filterDate)
    }

    setFilteredItems(filtered)
  }

  const handleClearSearch = () => {
    setSearchQuery('')
    setSearchFilters({})
    setFilteredItems([])
    setIsSearching(false)
  }

  // Use filtered items if searching, otherwise use all trash items
  const displayItems = isSearching ? filteredItems : trashItems

  return (
    <div
      className="h-full flex flex-col"
      onContextMenu={(e) => e.preventDefault()} // 禁用浏览器默认右键菜单
    >
      {/* Header */}
      <div className="flex-shrink-0 border-b border-current-line bg-secondary">
        <div className="px-4 md:px-6 py-4 md:py-6">
          <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4">
            <TrashIcon className="w-10 h-10 text-comment" />
            <div>
              <h1 className="text-2xl font-bold text-primary">{t('files.trash')}</h1>
              {trashContents && (
                <p className="text-sm text-comment">
                  {trashContents.totalCount} {trashContents.totalCount === 1 ? t('files.item') : t('files.items')}
                </p>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {trashItems.length > 0 && (
              <>
                <Button
                  variant="outline"
                  size="md"
                  onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                  className="px-4 py-2"
                >
                  <span className="text-lg">{viewMode === 'grid' ? '☰' : '⊞'}</span>
                </Button>

                <Button
                  variant="danger"
                  size="md"
                  onClick={handleEmptyTrash}
                  disabled={loading}
                  className="px-4 py-2"
                >
                  <TrashIcon className="w-5 h-5 mr-2" />
                  {t('files.emptyTrash')}
                </Button>
              </>
            )}

            <Button
              variant="outline"
              size="md"
              onClick={loadTrashContents}
              disabled={loading}
              title={t('common.refresh')}
              className="px-4 py-2"
            >
              <ArrowPathIcon className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>

        {/* Trash Statistics */}
        <TrashStats trashContents={trashContents} />

        {/* Cleanup Reminder */}
        {trashContents && (
          <TrashCleanupReminder
            itemCount={trashContents.totalCount}
            onEmptyTrash={handleEmptyTrash}
          />
        )}

        {/* Info Banner */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
          <div className="flex items-start space-x-2">
            <InformationCircleIcon className="w-5 h-5 text-blue-500 flex-shrink-0 mt-0.5" />
            <div className="text-sm text-blue-700">
              <p className="font-medium mb-1">{t('files.trashInfo')}</p>
              <p>{t('files.trashRetentionNotice')}</p>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded flex items-center">
            <ExclamationTriangleIcon className="w-5 h-5 mr-2" />
            {error}
            <button
              onClick={() => setError(null)}
              className="ml-auto text-red-500 hover:text-red-700"
            >
              ×
            </button>
          </div>
        )}

        {/* Search */}
        {trashItems.length > 0 && (
          <div className="mb-4">
            <TrashSearch
              onSearch={handleSearch}
              onClear={handleClearSearch}
            />
          </div>
        )}

        {/* Search Results Info */}
        {isSearching && (
          <div className="mb-4 text-sm text-comment">
            {displayItems.length === 0 ? (
              t('files.noSearchResults')
            ) : (
              t('files.searchResults', { 
                count: displayItems.length.toString(),
                total: trashItems.length.toString()
              })
            )}
          </div>
        )}

        {/* Batch Operations */}
        {selectedFiles.size > 0 && (
          <div className="mb-4">
            <TrashOperations
              selectedCount={selectedFiles.size}
              onRestore={handleRestoreSelected}
              onDeletePermanently={handleDeleteSelectedPermanently}
              loading={loading}
            />
          </div>
        )}
        </div>
      </div>

      {/* Main Content */}
      <div
        className="flex-1 p-4 overflow-hidden"
      >
        <div className="h-full flex flex-col border border-current-line rounded-lg bg-secondary">
        {loading && !trashContents ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto mb-4"></div>
              <p className="text-comment">{t('common.loading')}</p>
            </div>
          </div>
        ) : displayItems.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <TrashIcon className="w-24 h-24 text-comment mx-auto mb-6" />
              <h2 className="text-xl font-semibold text-primary mb-2">{t('files.trashEmpty')}</h2>
              <p className="text-comment max-w-md mx-auto">
                {t('files.trashEmptyDescription')}
              </p>
              <div className="mt-6">
                <Button
                  variant="outline"
                  onClick={() => window.history.back()}
                >
                  {t('files.backToFiles')}
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex-1 p-2 overflow-hidden">
            {isMobile ? (
              <MobileFileList
                files={displayItems}
                selectedFiles={selectedFiles}
                onFileSelect={handleFileSelect}
                onSelectAll={handleSelectAll}
                onFolderOpen={() => {}} // Disabled in trash
                onContextMenu={handleContextMenu}
                onFilePreview={() => {}} // Disabled in trash
                loading={loading}
                selectionMode={selectedFiles.size > 0}
                onSelectionModeChange={() => {}}
                showRestoreAction={true}
                onRestore={handleRestore}
              />
            ) : (
              <FileList
                files={displayItems}
                viewMode={viewMode}
                selectedFiles={selectedFiles}
                onFileSelect={handleFileSelect}
                onSelectAll={handleSelectAll}
                onFolderOpen={() => {}} // Disabled in trash
                onContextMenu={handleContextMenuEvent}
                onFilePreview={() => {}} // Disabled in trash
                loading={loading}
                showRestoreAction={true}
                onRestore={handleRestore}
              />
            )}
          </div>
        )}
        </div>
      </div>

      {/* Context Menu */}
      {contextMenu && (
        <FileContextMenu
          file={contextMenu.file}
          position={contextMenu.position}
          onClose={() => setContextMenu(null)}
          onAction={handleContextAction}
          showRestoreAction={true}
        />
      )}
    </div>
  )
}

export default Trash