import React, { useState, useEffect } from 'react'
import { useLocation, Link, useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { useI18n } from '../contexts/I18nContext'
import { useToast, setGlobalToast, showNotification } from '../contexts/ToastContext'
import { Search, Bell, User, Home, Folder, Share2, Clock, Trash2, LogOut } from 'lucide-react'
import MobileNav from './ui/MobileNav'
import BottomNav from './ui/BottomNav'
import MobileLayout from './MobileLayout'
import { SyncStatusIndicator, SyncDashboard } from './sync'
import { useSync } from '../contexts/SyncContext'
import { useMobile } from '../hooks/useMobile'
import { fileService } from '../services/fileService'
import ThemeLanguageToggle from './ThemeLanguageToggle'
// import WebSocketStatus from './WebSocketStatus'

interface LayoutProps {
  children: React.ReactNode
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { t } = useI18n()
  const { user, logout } = useAuth()
  const { conflicts, isOnline } = useSync()
  const { isMobile } = useMobile()
  const { showToast, showConfirm, showPrompt } = useToast()
  const location = useLocation()
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState('home')
  const [showSyncDashboard, setShowSyncDashboard] = useState(false)
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [dragOverItem, setDragOverItem] = useState<string | null>(null)

  // Initialize global dialogs
  useEffect(() => {
    setGlobalToast(showToast, showConfirm, showPrompt)
  }, [showToast, showConfirm, showPrompt])

  // Use mobile layout for mobile devices
  if (isMobile) {
    return <MobileLayout>{children}</MobileLayout>
  }

  const navItems = [
    { icon: Home, label: t('nav.dashboard'), href: '/dashboard', id: 'dashboard' },
    { icon: Folder, label: t('nav.files'), href: '/files', id: 'files' },
    { icon: Share2, label: t('nav.shared'), href: '/shared', id: 'shared' },
    { icon: Clock, label: t('nav.recent'), href: '/recent', id: 'recent' },
    { icon: Trash2, label: t('nav.trash'), href: '/trash', id: 'trash' },
  ]

  const isActiveRoute = (href: string) => {
    if (href === '/dashboard') {
      return location.pathname === '/' || location.pathname === '/dashboard'
    }
    return location.pathname === href
  }

  const handleLogout = async () => {
    try {
      await logout()
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  // Drag and drop handlers for sidebar menu items
  const handleDragOver = (e: React.DragEvent, itemId: string) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
    setDragOverItem(itemId)
  }

  const handleDragLeave = () => {
    setDragOverItem(null)
  }

  const handleDrop = async (e: React.DragEvent, targetAction: string) => {
    e.preventDefault()
    setDragOverItem(null)

    try {
      const draggedData = e.dataTransfer.getData('application/json')
      if (!draggedData) return

      const { id: fileId, type, isImageBedItem } = JSON.parse(draggedData)

      // Check if it's from image bed folder (should be prevented)
      if (isImageBedItem) {
        console.warn('Cannot drag items from image bed folder')
        return
      }

      if (targetAction === 'shared') {
        // Create public share link
        const shareResult = await fileService.shareFile(fileId, {
          permissions: [{ type: 'read', granted: true }],
          expiresAt: undefined, // No expiration for default public links
          maxDownloads: undefined // No download limit
        })



        // Copy link to clipboard
        if (shareResult && shareResult.token) {
          // Construct public URL from token
          const baseUrl = window.location.origin
          const publicUrl = `${baseUrl}/api/files/public/${shareResult.token}`

          await navigator.clipboard.writeText(publicUrl)
          // Show success message
          showNotification('success', t('files.shareLinkCopied') || 'Share link copied to clipboard!')
        } else {
          console.error('Share result structure:', shareResult)
          showNotification('error', '分享链接创建成功，但无法获取链接地址')
        }

      } else if (targetAction === 'trash') {
        // Move to trash
        if (type === 'folder') {
          await fileService.deleteFolder(fileId, false) // Soft delete
        } else {
          await fileService.moveFileToTrash(fileId)
        }

        // Refresh the current page if we're on files page
        if (location.pathname === '/files') {
          window.location.reload()
        }
      }
    } catch (error) {
      console.error('Drop action failed:', error)
    }
  }

  return (
    <div className="h-full bg-primary flex flex-col">
      {/* Header */}
      <header className="bg-current-line border-b border-primary px-4 py-3 fixed top-0 left-0 right-0 z-30">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <MobileNav />
            <h1 className="text-xl font-bold text-primary">{t('app.title')}</h1>
          </div>

          <div className="flex items-center space-x-3 md:space-x-6">
            {/* WebSocket Status Indicator */}
            {/* <div className="hidden sm:block">
              <WebSocketStatus />
            </div> */}

            {/* Sync Status Indicator */}
            <div className="hidden sm:block">
              <div
                onClick={() => setShowSyncDashboard(prev => !prev)}
                className="relative p-2 hover-bg rounded-lg transition-colors cursor-pointer"
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    setShowSyncDashboard(prev => !prev);
                  }
                }}
              >
                <SyncStatusIndicator showDetails={true} />
                {conflicts.length > 0 && (
                  <span className="absolute -top-1 -right-1 w-4 h-4 bg-orange-500 text-white text-xs flex items-center justify-center rounded-full">
                    {conflicts.length}
                  </span>
                )}
                {!isOnline && (
                  <span className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs flex items-center justify-center rounded-full">
                    !
                  </span>
                )}
              </div>
            </div>

            {/* Search - Hidden on mobile, shown on tablet+ */}
            <div className="relative hidden lg:block">
              <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-comment pointer-events-none z-10" />
              <input
                type="text"
                placeholder={t('placeholder.searchFiles')}
                className="form-input pl-10 pr-4 w-48 md:w-64 text-sm"
                style={{ paddingLeft: '2.5rem' }}
              />
            </div>

            {/* Search button for mobile and tablet */}
            <button className="p-2 hover-bg rounded-lg lg:hidden transition-colors">
              <Search className="w-5 h-5 text-primary" />
            </button>

            <button className="p-2 hover-bg rounded-lg transition-colors relative">
              <Bell className="w-5 h-5 text-primary" />
              {/* 可以在这里添加通知数量徽章 */}
            </button>

            {/* 主题和语言切换按钮 */}
            <ThemeLanguageToggle />
            {/* User Menu */}
            <div className="relative">
              <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="p-2 hover-bg rounded-lg flex items-center space-x-2"
              >
                <User className="w-5 h-5 text-primary" />
                {user && <span className="hidden md:block text-primary text-sm">{user.username}</span>}
              </button>

              {showUserMenu && (
                <>
                  {/* 背景遮罩 */}
                  <div
                    className="fixed inset-0 z-40"
                    onClick={() => setShowUserMenu(false)}
                  />
                  {/* 用户菜单 */}
                  <div className="absolute right-0 mt-2 w-48 bg-primary border border-primary rounded-lg shadow-lg z-50">
                    <div className="py-1">
                      <Link
                        to="/session-management"
                        className="block px-4 py-2 text-sm text-primary hover-bg"
                        onClick={() => setShowUserMenu(false)}
                      >
                        {t('dashboard.sessionManagement')}
                      </Link>
                      <Link
                        to="/two-factor-setup"
                        className="block px-4 py-2 text-sm text-primary hover-bg"
                        onClick={() => setShowUserMenu(false)}
                      >
                        {t('dashboard.twoFactorAuth')}
                      </Link>
                      <hr className="my-1 border-primary" />
                      <button
                        onClick={handleLogout}
                        className="w-full text-left px-4 py-2 text-sm text-primary hover-bg flex items-center space-x-2"
                      >
                        <LogOut className="w-4 h-4" />
                        <span>{t('common.logout')}</span>
                      </button>
                    </div>
                  </div>
                </>)}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex flex-1 pt-16 overflow-hidden">
        {/* Desktop Sidebar - Hidden on mobile */}
        <aside className="hidden md:block w-64 bg-current-line min-h-screen p-4 fixed left-0 top-16 bottom-0 overflow-y-auto">
          <nav className="space-y-2">
            {navItems.map((item) => {
              const Icon = item.icon
              const isActive = isActiveRoute(item.href)
              const isDragTarget = item.id === 'shared' || item.id === 'trash'
              const isDragOver = dragOverItem === item.id

              if (isDragTarget) {
                return (
                  <div
                    key={item.href}
                    onDragOver={(e) => handleDragOver(e, item.id)}
                    onDragLeave={handleDragLeave}
                    onDrop={(e) => handleDrop(e, item.id)}
                    className={`rounded-lg transition-all duration-200 ${
                      isDragOver
                        ? 'bg-purple-500/20 border-2 border-purple-500 border-dashed'
                        : 'border-2 border-transparent'
                    }`}
                  >
                    <Link
                      to={item.href}
                      className={`flex items-center space-x-3 px-4 py-2 rounded-lg transition-colors ${isActive
                          ? 'bg-primary text-primary accent-purple font-medium'
                          : 'text-primary hover-bg'
                        } ${isDragOver ? 'pointer-events-none' : ''}`}
                    >
                      <Icon className="w-5 h-5" />
                      <span>{item.label}</span>
                      {isDragOver && (
                        <span className="ml-auto text-xs text-purple-400">
                          {item.id === 'shared' ? t('files.dropToShare') || 'Drop to share' : t('files.dropToTrash') || 'Drop to delete'}
                        </span>
                      )}
                    </Link>
                  </div>
                )
              }

              return (
                <Link
                  key={item.href}
                  to={item.href}
                  className={`flex items-center space-x-3 px-4 py-2 rounded-lg transition-colors ${isActive
                      ? 'bg-primary text-primary accent-purple font-medium'
                      : 'text-primary hover-bg'
                    }`}
                >
                  <Icon className="w-5 h-5" />
                  <span>{item.label}</span>
                </Link>
              )
            })}
          </nav>
        </aside>

        {/* Content Area */}
        <main className="flex-1 p-4 md:p-6 pb-20 md:pb-6 md:ml-64 overflow-auto">
          {children}
        </main>
      </div>

      {/* Mobile Bottom Navigation */}
      <BottomNav activeTab={activeTab} onTabChange={setActiveTab} />

      {/* Sync Dashboard Modal */}
      {showSyncDashboard && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-primary rounded-lg shadow-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center p-4 border-b border-primary">
              <h2 className="text-lg font-bold text-primary">{t('files.syncStatus')}</h2>
              <button
                onClick={() => setShowSyncDashboard(false)}
                className="p-2 hover-bg rounded-lg"
              >
                <svg className="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-4">
              <SyncDashboard />
            </div>
          </div>
        </div>
      )}

    </div>
  )
}

export default Layout
