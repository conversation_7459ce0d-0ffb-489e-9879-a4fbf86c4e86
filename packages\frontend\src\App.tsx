import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { useEffect } from 'react'
import { ThemeProvider } from './contexts/ThemeContext'
import { I18nProvider } from './contexts/I18nContext'
import { ToastProvider } from './contexts/ToastContext'
import { useMobile } from './hooks/useMobile'
import { useAuthStore, useSyncStore, initializeStores } from './stores'
import Layout from './components/Layout'
import MobileLayout from './components/MobileLayout'
import Dashboard from './pages/Dashboard'
import Files from './pages/FilesSimple'
import Trash from './pages/Trash'
import ShareManagement from './pages/ShareManagement'
import Login from './pages/Login'
import Register from './pages/Register'
import PasswordReset from './pages/PasswordReset'
import TwoFactorSetup from './pages/TwoFactorSetup'
import SessionManagement from './pages/SessionManagement'
import PublicShare from './pages/PublicShare'
import './App.css'

// Import debug utilities in development
if (process.env.NODE_ENV === 'development') {
  import('./debug/websocketDebug');
}

// Protected Route Component
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuthStore()

  if (isLoading) {
    return (
      <div className="min-h-screen bg-primary flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-accent-purple"></div>
      </div>
    )
  }

  return isAuthenticated ? <>{children}</> : <Navigate to="/login" replace />
}

// Public Route Component (redirect to dashboard if already authenticated)
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuthStore()

  if (isLoading) {
    return (
      <div className="min-h-screen bg-primary flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-accent-purple"></div>
      </div>
    )
  }

  return isAuthenticated ? <Navigate to="/dashboard" replace /> : <>{children}</>
}

// // WebSocket connection manager
// const WebSocketManager: React.FC = () => {
//   const { isAuthenticated } = useAuth()
  
//   // Only use WebSocket hook when authenticated
//   if (!isAuthenticated) {
//     return null
//   }

//   return <WebSocketConnection />
// }

// // Separate component for WebSocket connection
// const WebSocketConnection: React.FC = () => {
//   const { isConnected } = useWebSocket()
  
//   // Optional: Add connection status indicator in development
//   if (process.env.NODE_ENV === 'development') {
//     console.log('WebSocket connection status:', isConnected)
//   }
  
//   // This component just manages the connection, no UI needed
//   return null
// }

// Responsive layout wrapper
const ResponsiveLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isMobile } = useMobile()

  return isMobile ? (
    <MobileLayout>{children}</MobileLayout>
  ) : (
    <Layout>{children}</Layout>
  )
}

// App Routes Component
const AppRoutes: React.FC = () => {
  return (
    <Routes>
      {/* Public Routes */}
      <Route path="/login" element={<PublicRoute><Login /></PublicRoute>} />
      <Route path="/register" element={<PublicRoute><Register /></PublicRoute>} />
      <Route path="/password-reset" element={<PublicRoute><PasswordReset /></PublicRoute>} />
      <Route path="/share/:token" element={<PublicShare />} />

      {/* Protected Routes */}
      <Route path="/two-factor-setup" element={<ProtectedRoute><TwoFactorSetup /></ProtectedRoute>} />
      <Route path="/session-management" element={<ProtectedRoute><SessionManagement /></ProtectedRoute>} />

      {/* Main App Routes */}
      <Route
        path="/*"
        element={
          <ProtectedRoute>
            <ResponsiveLayout>
              <Routes>
                <Route path="/" element={<Navigate to="/dashboard" replace />} />
                <Route path="/dashboard" element={<Dashboard />} />
                <Route path="/files" element={<Files />} />
                <Route path="/shared" element={<ShareManagement />} />
                <Route path="/recent" element={<div className="text-primary">Recent Files - Coming Soon</div>} />
                <Route path="/trash" element={<Trash />} />
              </Routes>
            </ResponsiveLayout>
          </ProtectedRoute>
        }
      />
    </Routes>
  )
}

// Store initialization component
const StoreInitializer: React.FC = () => {
  useEffect(() => {
    initializeStores()
  }, [])

  return null
}

function App() {
  return (
    <I18nProvider>
      <ThemeProvider>
        <ToastProvider>
          <Router>
            <div className="App">
              <StoreInitializer />
              <AppRoutes />
            </div>
          </Router>
        </ToastProvider>
      </ThemeProvider>
    </I18nProvider>
  )
}

export default App
