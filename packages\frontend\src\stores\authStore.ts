import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { UserInfo, Session, TwoFactorSetup, TwoFactorStatus } from '@cloud-storage/shared'
import { authService } from '../services/authService'
import { ApiError } from '../services/api'

interface User extends UserInfo {
  twoFactorEnabled: boolean
}

interface AuthState {
  // State
  user: User | null
  sessions: Session[]
  isLoading: boolean
  isAuthenticated: boolean
  error: string | null

  // Actions
  login: (email: string, password: string, rememberMe?: boolean, twoFactorCode?: string) => Promise<void>
  register: (username: string, email: string, password: string) => Promise<any>
  logout: () => Promise<void>
  resetPassword: (email: string) => Promise<void>
  confirmPasswordReset: (token: string, newPassword: string) => Promise<void>
  enableTwoFactor: () => Promise<TwoFactorSetup>
  verifyTwoFactor: (code: string) => Promise<void>
  disableTwoFactor: (code: string) => Promise<void>
  getTwoFactorStatus: () => Promise<TwoFactorStatus>
  getSessions: () => Promise<void>
  revokeSession: (sessionId: string) => Promise<void>
  revokeAllSessions: () => Promise<void>
  clearError: () => void
  checkAuthStatus: () => Promise<void>
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      sessions: [],
      isLoading: false,
      isAuthenticated: false,
      error: null,

      // Actions
      login: async (email: string, password: string, rememberMe = false, twoFactorCode?: string) => {
        set({ isLoading: true, error: null })
        try {
          const result = await authService.login({ email, password, rememberMe }, twoFactorCode)
          
          // Fetch actual user profile after successful login
          const userProfile = await authService.getUserProfile()
          set({ 
            user: userProfile, 
            isAuthenticated: true, 
            isLoading: false 
          })
        } catch (error) {
          console.error('Login failed:', error)
          if (error instanceof ApiError) {
            set({ error: error.message, isLoading: false })
          } else {
            set({ error: 'Login failed. Please try again.', isLoading: false })
          }
          throw error
        }
      },

      register: async (username: string, email: string, password: string) => {
        set({ isLoading: true, error: null })
        try {
          const result = await authService.register({ username, email, password })
          
          // Verify registration was successful
          if (result.success && result.user && result.tokens) {
            // Auto-login after successful registration
            set({
              user: {
                ...result.user,
                twoFactorEnabled: false // Will be updated when we fetch user profile
              },
              isAuthenticated: true,
              isLoading: false
            })
            
            console.log('Registration successful, user auto-logged in:', result.user.username)
            return { success: true, user: result.user }
          } else {
            throw new Error('Registration failed - invalid response from server')
          }
        } catch (error) {
          console.error('Registration failed:', error)
          
          // Clear any user state on registration failure
          set({ user: null, isAuthenticated: false })
          
          if (error instanceof ApiError) {
            set({ error: error.message, isLoading: false })
          } else if (error instanceof Error) {
            set({ error: error.message, isLoading: false })
          } else {
            set({ error: 'Registration failed. Please try again.', isLoading: false })
          }
          throw error
        }
      },

      logout: async () => {
        try {
          await authService.logout()
          set({ 
            user: null, 
            sessions: [], 
            isAuthenticated: false, 
            error: null 
          })
        } catch (error) {
          console.error('Logout failed:', error)
          // Clear local state even if API call fails
          set({ 
            user: null, 
            sessions: [], 
            isAuthenticated: false, 
            error: null 
          })
        }
      },

      resetPassword: async (email: string) => {
        try {
          // TODO: Implement actual API call
          console.log('Password reset requested for:', email)
          // Simulate delay
          await new Promise(resolve => setTimeout(resolve, 1000))
        } catch (error) {
          console.error('Password reset failed:', error)
          throw error
        }
      },

      confirmPasswordReset: async (token: string, newPassword: string) => {
        try {
          // TODO: Implement actual API call
          console.log('Password reset confirmation:', { token, newPassword })
          // Simulate delay
          await new Promise(resolve => setTimeout(resolve, 1000))
        } catch (error) {
          console.error('Password reset confirmation failed:', error)
          throw error
        }
      },

      enableTwoFactor: async (): Promise<TwoFactorSetup> => {
        try {
          return await authService.enableTwoFactor()
        } catch (error) {
          console.error('Enable 2FA failed:', error)
          throw error
        }
      },

      verifyTwoFactor: async (code: string) => {
        try {
          await authService.verifyAndEnableTwoFactor(code)
          
          const { user } = get()
          if (user) {
            set({ user: { ...user, twoFactorEnabled: true } })
          }
        } catch (error) {
          console.error('2FA verification failed:', error)
          throw error
        }
      },

      disableTwoFactor: async (code: string) => {
        try {
          await authService.disableTwoFactor(code)
          
          const { user } = get()
          if (user) {
            set({ user: { ...user, twoFactorEnabled: false } })
          }
        } catch (error) {
          console.error('Disable 2FA failed:', error)
          throw error
        }
      },

      getSessions: async () => {
        try {
          const sessions = await authService.getSessions()
          set({ sessions })
        } catch (error) {
          console.error('Get sessions failed:', error)
          throw error
        }
      },

      revokeSession: async (sessionId: string) => {
        try {
          await authService.revokeSession(sessionId)
          const { sessions } = get()
          set({ sessions: sessions.filter(session => session.id !== sessionId) })
        } catch (error) {
          console.error('Revoke session failed:', error)
          throw error
        }
      },

      revokeAllSessions: async () => {
        try {
          await authService.logoutFromAllOtherSessions()
          // Refresh sessions list
          await get().getSessions()
        } catch (error) {
          console.error('Revoke all sessions failed:', error)
          throw error
        }
      },

      getTwoFactorStatus: async (): Promise<TwoFactorStatus> => {
        try {
          return await authService.getTwoFactorStatus()
        } catch (error) {
          console.error('Get 2FA status failed:', error)
          throw error
        }
      },

      clearError: () => {
        set({ error: null })
      },

      checkAuthStatus: async () => {
        set({ isLoading: true })
        try {
          if (authService.isAuthenticated()) {
            // Try to refresh token to verify it's still valid
            await authService.refreshToken()
            // Fetch actual user profile data
            const userProfile = await authService.getUserProfile()
            set({ 
              user: userProfile, 
              isAuthenticated: true, 
              isLoading: false 
            })
          } else {
            set({ isLoading: false })
          }
        } catch (error) {
          console.error('Auth check failed:', error)
          // Clear invalid tokens
          localStorage.removeItem('authToken')
          localStorage.removeItem('refreshToken')
          set({ 
            user: null, 
            isAuthenticated: false, 
            isLoading: false 
          })
        }
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
)
