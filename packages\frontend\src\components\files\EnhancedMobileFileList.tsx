import React, { useState, useRef, useEffect } from 'react'
import { FileItem } from '../../pages/Files'
import { useTouchInteractions } from '../../hooks/useTouchInteractions'
import { useMobile } from '../../hooks/useMobile'
import { cn } from '../../utils/cn'
import TouchButton from '../ui/TouchButton'
import {
  DocumentIcon,
  FolderIcon,
  PhotoIcon,
  VideoCameraIcon,
  MusicalNoteIcon,
  ArchiveBoxIcon,
  EllipsisVerticalIcon,
  CheckIcon,
  ArrowPathIcon,
  MagnifyingGlassIcon,
  XMarkIcon,
  ChevronRightIcon,
  ChevronLeftIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  Squares2X2Icon,
  ListBulletIcon,
  AdjustmentsHorizontalIcon
} from '@heroicons/react/24/outline'
import ImageBedFolderIcon from '../icons/ImageBedFolderIcon'

interface EnhancedMobileFileListProps {
  files: FileItem[]
  selectedFiles: Set<string>
  onFileSelect: (fileId: string, selected: boolean) => void
  onSelectAll: () => void
  onFolderOpen: (folderId: string, folderName: string) => void
  onFilePreview?: (file: FileItem) => void
  onContextMenu?: (file: FileItem, position: { x: number; y: number }) => void
  onRefresh?: () => Promise<void>
  onSearch?: (query: string) => void
  onSort?: (by: 'name' | 'date' | 'size' | 'type', direction: 'asc' | 'desc') => void
  onFilter?: (type: string | null) => void
  loading?: boolean
  selectionMode?: boolean
  onSelectionModeChange?: (enabled: boolean) => void
  currentPath?: string[]
  onNavigateBack?: () => void
}

interface SwipeAction {
  id: string
  label: string
  icon: React.ReactNode
  color: string
  action: (file: FileItem) => void
}

const EnhancedMobileFileList: React.FC<EnhancedMobileFileListProps> = ({
  files,
  selectedFiles,
  onFileSelect,
  onSelectAll,
  onFolderOpen,
  onFilePreview,
  onContextMenu,
  onRefresh,
  onSearch,
  onSort,
  onFilter,
  loading = false,
  selectionMode = false,
  onSelectionModeChange,
  currentPath = [],
  onNavigateBack
}) => {
  const { isMobile } = useMobile()
  const [swipedItem, setSwipedItem] = useState<string | null>(null)
  const [showSearch, setShowSearch] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [showSortOptions, setShowSortOptions] = useState(false)
  const [sortBy, setSortBy] = useState<'name' | 'date' | 'size' | 'type'>('name')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list')
  const [showFilterOptions, setShowFilterOptions] = useState(false)
  const [activeFilter, setActiveFilter] = useState<string | null>(null)
  
  const containerRef = useRef<HTMLDivElement>(null)
  const searchInputRef = useRef<HTMLInputElement>(null)

  // Enhanced touch interactions for pull-to-refresh
  const { pullProgress, isRefreshing } = useTouchInteractions(containerRef, {
    enablePullToRefresh: true,
    pullToRefreshThreshold: 100,
    enableHapticFeedback: true
  }, {
    onRefresh: async () => {
      if (onRefresh) {
        await onRefresh()
      }
      return Promise.resolve()
    }
  })

  // Focus search input when search is shown
  useEffect(() => {
    if (showSearch && searchInputRef.current) {
      searchInputRef.current.focus()
    }
  }, [showSearch])

  // Handle search submit
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (onSearch) {
      onSearch(searchQuery)
    }
  }

  // Handle sort change
  const handleSortChange = (by: 'name' | 'date' | 'size' | 'type', direction: 'asc' | 'desc') => {
    setSortBy(by)
    setSortDirection(direction)
    if (onSort) {
      onSort(by, direction)
    }
    setShowSortOptions(false)
    
    // Haptic feedback
    if ('vibrate' in navigator) {
      navigator.vibrate(20)
    }
  }

  // Handle filter change
  const handleFilterChange = (type: string | null) => {
    setActiveFilter(type)
    if (onFilter) {
      onFilter(type)
    }
    setShowFilterOptions(false)
    
    // Haptic feedback
    if ('vibrate' in navigator) {
      navigator.vibrate(20)
    }
  }

  const getFileIcon = (file: FileItem) => {
    if (file.type === 'folder') {
      if (file.isImageBedFolder) {
        return <ImageBedFolderIcon size="lg" />
      }
      return <FolderIcon className="w-8 h-8 text-dracula-cyan" />
    }

    if (!file.mimeType) {
      return <DocumentIcon className="w-8 h-8 text-dracula-comment" />
    }

    if (file.mimeType.startsWith('image/')) {
      return <PhotoIcon className="w-8 h-8 text-dracula-green" />
    }

    if (file.mimeType.startsWith('video/')) {
      return <VideoCameraIcon className="w-8 h-8 text-dracula-red" />
    }

    if (file.mimeType.startsWith('audio/')) {
      return <MusicalNoteIcon className="w-8 h-8 text-dracula-purple" />
    }

    if (file.mimeType.includes('zip') || file.mimeType.includes('rar') || file.mimeType.includes('tar')) {
      return <ArchiveBoxIcon className="w-8 h-8 text-dracula-orange" />
    }

    return <DocumentIcon className="w-8 h-8 text-dracula-comment" />
  }

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return ''
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
  }

  const formatDate = (date: Date) => {
    const now = new Date()
    const diffTime = now.getTime() - date.getTime()
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 0) {
      return new Intl.DateTimeFormat('en-US', {
        hour: '2-digit',
        minute: '2-digit'
      }).format(date)
    } else if (diffDays < 7) {
      return `${diffDays}d ago`
    } else {
      return new Intl.DateTimeFormat('en-US', {
        month: 'short',
        day: 'numeric'
      }).format(date)
    }
  }

  // Swipe actions for files
  const getSwipeActions = (file: FileItem): SwipeAction[] => [
    {
      id: 'share',
      label: 'Share',
      icon: <span className="text-lg">📤</span>,
      color: 'bg-blue-500',
      action: (file) => {
        // Handle share action
        console.log('Share file:', file.name)
        
        // Haptic feedback
        if ('vibrate' in navigator) {
          navigator.vibrate(30)
        }
      }
    },
    {
      id: 'download',
      label: 'Download',
      icon: <span className="text-lg">⬇️</span>,
      color: 'bg-green-500',
      action: (file) => {
        // Handle download action
        console.log('Download file:', file.name)
        
        // Haptic feedback
        if ('vibrate' in navigator) {
          navigator.vibrate(30)
        }
      }
    },
    {
      id: 'delete',
      label: 'Delete',
      icon: <span className="text-lg">🗑️</span>,
      color: 'bg-red-500',
      action: (file) => {
        // Handle delete action
        console.log('Delete file:', file.name)
        
        // Haptic feedback
        if ('vibrate' in navigator) {
          navigator.vibrate([100, 50, 100])
        }
      }
    }
  ]

  // Pull-to-refresh indicator
  const renderPullToRefreshIndicator = () => {
    if (pullProgress > 0 || isRefreshing) {
      return (
        <div className="absolute top-0 left-0 right-0 flex justify-center z-10 pointer-events-none">
          <div className="bg-dracula-current-line rounded-full p-2 m-2 transform transition-transform" 
               style={{ transform: `translateY(${Math.min(pullProgress / 2, 50)}px)` }}>
            {isRefreshing ? (
              <ArrowPathIcon className="w-6 h-6 text-dracula-purple animate-spin" />
            ) : (
              <ArrowPathIcon 
                className="w-6 h-6 text-dracula-purple transition-transform" 
                style={{ transform: `rotate(${Math.min(pullProgress * 1.8, 180)}deg)` }} 
              />
            )}
          </div>
        </div>
      )
    }
    return null
  }

  const FileListItem: React.FC<{ file: FileItem; index: number }> = ({ file, index }) => {
    const itemRef = useRef<HTMLDivElement>(null)
    const [isPressed, setIsPressed] = useState(false)
    const [showActions, setShowActions] = useState(false)
    const [swipeOffset, setSwipeOffset] = useState(0)
    const [isDragging, setIsDragging] = useState(false)

    const isSelected = selectedFiles.has(file.id)
    const isSwipedOut = swipedItem === file.id

    const swipeActions = getSwipeActions(file)

    // Enhanced touch interactions for swipe actions
    const { attachGestures } = useTouchInteractions(itemRef.current, {
      enableSwipeToClose: false,
      enableHapticFeedback: true
    }, {})

    const handleTouchStart = () => {
      setIsPressed(true)
      // Light haptic feedback on touch
      if ('vibrate' in navigator) {
        navigator.vibrate(10)
      }
    }

    const handleTouchEnd = () => {
      setIsPressed(false)
    }

    const handleTap = () => {
      if (selectionMode) {
        onFileSelect(file.id, !isSelected)
        // Haptic feedback for selection
        if ('vibrate' in navigator) {
          navigator.vibrate(30)
        }
      } else {
        if (file.type === 'folder') {
          onFolderOpen(file.id, file.name)
        } else if (onFilePreview) {
          onFilePreview(file)
        }
      }
    }

    const handleLongPress = () => {
      if (!selectionMode) {
        onSelectionModeChange?.(true)
        onFileSelect(file.id, true)
        // Stronger haptic feedback for selection mode
        if ('vibrate' in navigator) {
          navigator.vibrate([100, 50, 100])
        }
      } else if (onContextMenu) {
        const rect = itemRef.current?.getBoundingClientRect()
        if (rect) {
          onContextMenu(file, { x: rect.right - 20, y: rect.top + rect.height / 2 })
        }
      }
    }

    const handleSwipeLeft = () => {
      if (!selectionMode && !isSwipedOut) {
        setSwipedItem(file.id)
        setShowActions(true)
        // Haptic feedback
        if ('vibrate' in navigator) {
          navigator.vibrate(50)
        }
      }
    }

    const handleSwipeRight = () => {
      if (isSwipedOut) {
        setSwipedItem(null)
        setShowActions(false)
        // Haptic feedback
        if ('vibrate' in navigator) {
          navigator.vibrate(20)
        }
      }
    }

    const handleActionTap = (action: SwipeAction) => {
      action.action(file)
      setSwipedItem(null)
      setShowActions(false)
    }

    // List item rendering
    if (viewMode === 'list') {
      return (
        <div className="relative overflow-hidden">
          {/* Swipe Actions Background */}
          {showActions && (
            <div className="absolute inset-y-0 right-0 flex">
              {swipeActions.map((action, actionIndex) => (
                <TouchButton
                  key={action.id}
                  variant="ghost"
                  onClick={() => handleActionTap(action)}
                  className={cn(
                    'flex flex-col items-center justify-center w-20 text-white text-xs font-medium transition-all duration-200',
                    action.color,
                    isSwipedOut && 'translate-x-0'
                  )}
                  style={{
                    transform: isSwipedOut ? 'translateX(0)' : `translateX(${(actionIndex + 1) * 80}px)`
                  }}
                  haptic="medium"
                >
                  {action.icon}
                  <span className="mt-1">{action.label}</span>
                </TouchButton>
              ))}
            </div>
          )}

          {/* File Item */}
          <div
            ref={itemRef}
            className={cn(
              'flex items-center p-4 bg-dracula-bg border-b border-dracula-current-line transition-all duration-200 touch-manipulation select-none',
              'active:bg-dracula-current-line active:scale-[0.98]',
              isSelected && 'bg-dracula-purple/10 border-dracula-purple/30',
              isSwipedOut && '-translate-x-60',
              isPressed && 'bg-dracula-current-line scale-[0.98]'
            )}
            onClick={handleTap}
            onTouchStart={handleTouchStart}
            onTouchEnd={handleTouchEnd}
            data-swipe-actions="true"
            data-long-press="true"
            data-long-press-action="select"
          >
            {/* Selection Checkbox - 图床文件夹不显示勾选框 */}
            {selectionMode && !file.isImageBedFolder && (
              <div className="mr-3 flex-shrink-0">
                <div
                  className={cn(
                    'w-6 h-6 rounded-full border-2 flex items-center justify-center transition-colors',
                    isSelected
                      ? 'bg-dracula-purple border-dracula-purple'
                      : 'border-dracula-comment'
                  )}
                >
                  {isSelected && <CheckIcon className="w-4 h-4 text-white" />}
                </div>
              </div>
            )}

            {/* File Icon */}
            <div className="mr-4 flex-shrink-0">
              {file.thumbnailUrl && file.type !== 'folder' ? (
                <img
                  src={file.thumbnailUrl}
                  alt={file.name}
                  className="w-12 h-12 object-cover rounded-lg"
                />
              ) : (
                <div className="w-12 h-12 flex items-center justify-center">
                  {getFileIcon(file)}
                </div>
              )}
            </div>

            {/* File Info */}
            <div className="flex-1 min-w-0">
              <h3 className="text-dracula-foreground font-medium truncate">
                {file.name}
              </h3>
              {/* 图床文件夹不显示大小和时间信息 */}
              {!file.isImageBedFolder && (
                <div className="flex items-center space-x-2 mt-1 text-sm text-dracula-comment">
                  {file.size && <span>{formatFileSize(file.size)}</span>}
                  <span>•</span>
                  <span>{formatDate(file.modifiedAt)}</span>
                </div>
              )}
            </div>

            {/* More Options */}
            {!selectionMode && (
              <TouchButton
                variant="ghost"
                size="icon"
                onClick={(e) => {
                  e.stopPropagation()
                  if (onContextMenu) {
                    const rect = e.currentTarget.getBoundingClientRect()
                    onContextMenu(file, {
                      x: rect.right,
                      y: rect.top + rect.height / 2
                    })
                  }
                }}
                className="text-dracula-comment"
                haptic="light"
              >
                <EllipsisVerticalIcon className="w-5 h-5" />
              </TouchButton>
            )}
          </div>
        </div>
      )
    } else {
      // Grid view rendering
      return (
        <div
          ref={itemRef}
          className={cn(
            'p-2 touch-manipulation select-none',
            isSelected && 'bg-dracula-purple/10 rounded-lg'
          )}
          onClick={handleTap}
          onTouchStart={handleTouchStart}
          onTouchEnd={handleTouchEnd}
          data-long-press="true"
          data-long-press-action="select"
        >
          <div className={cn(
            'aspect-square rounded-lg overflow-hidden bg-dracula-current-line flex items-center justify-center mb-2',
            isPressed && 'scale-[0.95] transition-transform'
          )}>
            {file.thumbnailUrl && file.type !== 'folder' ? (
              <img
                src={file.thumbnailUrl}
                alt={file.name}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-12 h-12 flex items-center justify-center">
                {getFileIcon(file)}
              </div>
            )}
            
            {/* Selection indicator - 图床文件夹不显示勾选框 */}
            {selectionMode && !file.isImageBedFolder && (
              <div className="absolute top-2 right-2">
                <div
                  className={cn(
                    'w-6 h-6 rounded-full border-2 flex items-center justify-center transition-colors',
                    isSelected
                      ? 'bg-dracula-purple border-dracula-purple'
                      : 'bg-black/50 border-white/50'
                  )}
                >
                  {isSelected && <CheckIcon className="w-4 h-4 text-white" />}
                </div>
              </div>
            )}
          </div>
          
          <h3 className="text-sm text-dracula-foreground font-medium truncate">
            {file.name}
          </h3>
          {/* 图床文件夹不显示大小和时间信息 */}
          {!file.isImageBedFolder && (
            <p className="text-xs text-dracula-comment truncate">
              {file.size ? formatFileSize(file.size) : formatDate(file.modifiedAt)}
            </p>
          )}
        </div>
      )
    }
  }

  // Render breadcrumb navigation
  const renderBreadcrumbs = () => {
    if (currentPath.length === 0) return null
    
    return (
      <div className="flex items-center overflow-x-auto whitespace-nowrap px-4 py-2 bg-dracula-bg border-b border-dracula-current-line">
        {onNavigateBack && (
          <TouchButton
            variant="ghost"
            size="icon"
            onClick={onNavigateBack}
            className="mr-2 text-dracula-purple"
            haptic="medium"
          >
            <ChevronLeftIcon className="w-5 h-5" />
          </TouchButton>
        )}
        
        {currentPath.map((path, index) => (
          <React.Fragment key={index}>
            {index > 0 && (
              <ChevronRightIcon className="w-4 h-4 mx-1 text-dracula-comment" />
            )}
            <span className={cn(
              "text-sm px-1 py-0.5 rounded",
              index === currentPath.length - 1 
                ? "text-dracula-foreground font-medium" 
                : "text-dracula-comment"
            )}>
              {path}
            </span>
          </React.Fragment>
        ))}
      </div>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-dracula-purple"></div>
      </div>
    )
  }

  return (
    <div ref={containerRef} className="flex flex-col h-full relative">
      {/* Pull-to-refresh indicator */}
      {renderPullToRefreshIndicator()}
      
      {/* Search Bar */}
      {showSearch ? (
        <div className="bg-dracula-current-line p-2 border-b border-dracula-comment">
          <form onSubmit={handleSearchSubmit} className="flex items-center">
            <div className="flex-1 relative">
              <MagnifyingGlassIcon className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-dracula-comment" />
              <input
                ref={searchInputRef}
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search files and folders..."
                className="w-full pl-10 pr-4 py-2 bg-dracula-bg border border-dracula-comment rounded-lg text-dracula-foreground placeholder-dracula-comment focus:outline-none focus:border-dracula-purple"
              />
              {searchQuery && (
                <TouchButton
                  variant="ghost"
                  size="icon"
                  onClick={() => setSearchQuery('')}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-dracula-comment"
                  haptic="light"
                >
                  <XMarkIcon className="w-5 h-5" />
                </TouchButton>
              )}
            </div>
            <TouchButton
              variant="ghost"
              onClick={() => setShowSearch(false)}
              className="ml-2 text-dracula-purple font-medium"
              haptic="light"
            >
              Cancel
            </TouchButton>
          </form>
          
          {/* Search filters */}
          <div className="flex items-center space-x-2 mt-2 overflow-x-auto pb-1">
            <TouchButton
              variant={activeFilter === null ? "primary" : "outline"}
              size="sm"
              onClick={() => handleFilterChange(null)}
              className={activeFilter === null ? "bg-dracula-purple" : ""}
              haptic="light"
            >
              All
            </TouchButton>
            <TouchButton
              variant={activeFilter === 'folder' ? "primary" : "outline"}
              size="sm"
              onClick={() => handleFilterChange('folder')}
              className={activeFilter === 'folder' ? "bg-dracula-cyan" : ""}
              haptic="light"
            >
              Folders
            </TouchButton>
            <TouchButton
              variant={activeFilter === 'image' ? "primary" : "outline"}
              size="sm"
              onClick={() => handleFilterChange('image')}
              className={activeFilter === 'image' ? "bg-dracula-green" : ""}
              haptic="light"
            >
              Images
            </TouchButton>
            <TouchButton
              variant={activeFilter === 'document' ? "primary" : "outline"}
              size="sm"
              onClick={() => handleFilterChange('document')}
              className={activeFilter === 'document' ? "bg-dracula-orange" : ""}
              haptic="light"
            >
              Documents
            </TouchButton>
            <TouchButton
              variant={activeFilter === 'media' ? "primary" : "outline"}
              size="sm"
              onClick={() => handleFilterChange('media')}
              className={activeFilter === 'media' ? "bg-dracula-red" : ""}
              haptic="light"
            >
              Media
            </TouchButton>
          </div>
        </div>
      ) : (
        <>
          {/* Breadcrumb navigation */}
          {renderBreadcrumbs()}
          
          {/* Action Bar */}
          <div className="flex items-center justify-between p-2 bg-dracula-current-line border-b border-dracula-comment">
            <div className="flex items-center space-x-2">
              <TouchButton
                variant="ghost"
                size="icon"
                onClick={() => setShowSearch(true)}
                className="text-dracula-foreground"
                haptic="light"
              >
                <MagnifyingGlassIcon className="w-5 h-5" />
              </TouchButton>
              
              <TouchButton
                variant="ghost"
                size="icon"
                onClick={() => setShowSortOptions(!showSortOptions)}
                className={cn(
                  "text-dracula-foreground",
                  showSortOptions && "bg-dracula-bg"
                )}
                haptic="light"
              >
                {sortDirection === 'asc' ? (
                  <ArrowUpIcon className="w-5 h-5" />
                ) : (
                  <ArrowDownIcon className="w-5 h-5" />
                )}
              </TouchButton>
              
              <TouchButton
                variant="ghost"
                size="icon"
                onClick={() => setShowFilterOptions(!showFilterOptions)}
                className={cn(
                  "text-dracula-foreground",
                  showFilterOptions && "bg-dracula-bg",
                  activeFilter && "text-dracula-purple"
                )}
                haptic="light"
              >
                <AdjustmentsHorizontalIcon className="w-5 h-5" />
              </TouchButton>
            </div>
            
            <div className="flex items-center space-x-2">
              <TouchButton
                variant="ghost"
                size="icon"
                onClick={() => setViewMode(viewMode === 'list' ? 'grid' : 'list')}
                className="text-dracula-foreground"
                haptic="medium"
              >
                {viewMode === 'list' ? (
                  <Squares2X2Icon className="w-5 h-5" />
                ) : (
                  <ListBulletIcon className="w-5 h-5" />
                )}
              </TouchButton>
              
              {!selectionMode && (
                <TouchButton
                  variant="ghost"
                  onClick={() => onSelectionModeChange?.(true)}
                  className="text-dracula-purple font-medium"
                  haptic="medium"
                >
                  Select
                </TouchButton>
              )}
            </div>
          </div>
        </>
      )}

      {/* Sort Options Dropdown */}
      {showSortOptions && (
        <div className="absolute top-12 left-12 z-20 bg-dracula-current-line rounded-lg shadow-lg overflow-hidden">
          <div className="p-2 border-b border-dracula-comment text-sm font-medium text-dracula-foreground">
            Sort by
          </div>
          <TouchButton
            variant="ghost"
            onClick={() => handleSortChange('name', sortBy === 'name' ? (sortDirection === 'asc' ? 'desc' : 'asc') : 'asc')}
            className={cn(
              "w-full text-left px-4 py-2 text-sm",
              sortBy === 'name' ? "text-dracula-purple" : "text-dracula-foreground"
            )}
            haptic="light"
          >
            Name {sortBy === 'name' && (sortDirection === 'asc' ? '↑' : '↓')}
          </TouchButton>
          <TouchButton
            variant="ghost"
            onClick={() => handleSortChange('date', sortBy === 'date' ? (sortDirection === 'asc' ? 'desc' : 'asc') : 'desc')}
            className={cn(
              "w-full text-left px-4 py-2 text-sm",
              sortBy === 'date' ? "text-dracula-purple" : "text-dracula-foreground"
            )}
            haptic="light"
          >
            Date {sortBy === 'date' && (sortDirection === 'asc' ? '↑' : '↓')}
          </TouchButton>
          <TouchButton
            variant="ghost"
            onClick={() => handleSortChange('size', sortBy === 'size' ? (sortDirection === 'asc' ? 'desc' : 'asc') : 'desc')}
            className={cn(
              "w-full text-left px-4 py-2 text-sm",
              sortBy === 'size' ? "text-dracula-purple" : "text-dracula-foreground"
            )}
            haptic="light"
          >
            Size {sortBy === 'size' && (sortDirection === 'asc' ? '↑' : '↓')}
          </TouchButton>
          <TouchButton
            variant="ghost"
            onClick={() => handleSortChange('type', sortBy === 'type' ? (sortDirection === 'asc' ? 'desc' : 'asc') : 'asc')}
            className={cn(
              "w-full text-left px-4 py-2 text-sm",
              sortBy === 'type' ? "text-dracula-purple" : "text-dracula-foreground"
            )}
            haptic="light"
          >
            Type {sortBy === 'type' && (sortDirection === 'asc' ? '↑' : '↓')}
          </TouchButton>
        </div>
      )}

      {/* Filter Options Dropdown */}
      {showFilterOptions && (
        <div className="absolute top-12 left-20 z-20 bg-dracula-current-line rounded-lg shadow-lg overflow-hidden">
          <div className="p-2 border-b border-dracula-comment text-sm font-medium text-dracula-foreground">
            Filter by
          </div>
          <TouchButton
            variant="ghost"
            onClick={() => handleFilterChange(null)}
            className={cn(
              "w-full text-left px-4 py-2 text-sm",
              activeFilter === null ? "text-dracula-purple" : "text-dracula-foreground"
            )}
            haptic="light"
          >
            All files
          </TouchButton>
          <TouchButton
            variant="ghost"
            onClick={() => handleFilterChange('folder')}
            className={cn(
              "w-full text-left px-4 py-2 text-sm",
              activeFilter === 'folder' ? "text-dracula-purple" : "text-dracula-foreground"
            )}
            haptic="light"
          >
            Folders
          </TouchButton>
          <TouchButton
            variant="ghost"
            onClick={() => handleFilterChange('image')}
            className={cn(
              "w-full text-left px-4 py-2 text-sm",
              activeFilter === 'image' ? "text-dracula-purple" : "text-dracula-foreground"
            )}
            haptic="light"
          >
            Images
          </TouchButton>
          <TouchButton
            variant="ghost"
            onClick={() => handleFilterChange('document')}
            className={cn(
              "w-full text-left px-4 py-2 text-sm",
              activeFilter === 'document' ? "text-dracula-purple" : "text-dracula-foreground"
            )}
            haptic="light"
          >
            Documents
          </TouchButton>
          <TouchButton
            variant="ghost"
            onClick={() => handleFilterChange('media')}
            className={cn(
              "w-full text-left px-4 py-2 text-sm",
              activeFilter === 'media' ? "text-dracula-purple" : "text-dracula-foreground"
            )}
            haptic="light"
          >
            Media files
          </TouchButton>
        </div>
      )}

      {/* Selection Header */}
      {selectionMode && (
        <div className="flex items-center justify-between p-4 bg-dracula-current-line border-b border-dracula-comment">
          <div className="flex items-center space-x-4">
            <TouchButton
              variant="ghost"
              onClick={() => onSelectionModeChange?.(false)}
              className="text-dracula-purple font-medium"
              haptic="medium"
            >
              Cancel
            </TouchButton>
            <span className="text-dracula-foreground">
              {selectedFiles.size} selected
            </span>
          </div>
          <TouchButton
            variant="ghost"
            onClick={onSelectAll}
            className="text-dracula-purple font-medium"
            haptic="medium"
          >
            Select All
          </TouchButton>
        </div>
      )}

      {/* File List */}
      <div className="flex-1 overflow-y-auto">
        {files.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64 text-dracula-comment p-8">
            <FolderIcon className="w-16 h-16 mb-4" />
            <p className="text-lg font-medium">No files found</p>
            <p className="text-sm text-center">Upload files or create folders to get started</p>
          </div>
        ) : viewMode === 'list' ? (
          files.map((file, index) => (
            <FileListItem key={file.id} file={file} index={index} />
          ))
        ) : (
          <div className="grid grid-cols-3 gap-2 p-2">
            {files.map((file, index) => (
              <FileListItem key={file.id} file={file} index={index} />
            ))}
          </div>
        )}
      </div>

      {/* Gesture Hints */}
      {isMobile && !selectionMode && files.length > 0 && (
        <div className="p-2 text-center text-xs text-dracula-comment bg-dracula-current-line">
          Pull down to refresh • Swipe left for actions • Long press to select
        </div>
      )}
    </div>
  )
}

export default EnhancedMobileFileList