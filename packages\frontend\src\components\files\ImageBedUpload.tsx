import React, { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { useI18n } from '../../contexts/I18nContext'
import { showNotification } from '../../contexts/ToastContext'
import { imageBedService } from '../../services/imageBedService'
import Button from '../ui/Button'
import {
  CloudArrowUpIcon,
  PhotoIcon,
  XMarkIcon,
  LinkIcon,
  CheckIcon
} from '@heroicons/react/24/outline'

interface ImageBedUploadProps {
  onUploadComplete?: (results: UploadResult[]) => void
  onClose?: () => void
}

interface UploadResult {
  id: string
  originalName: string
  publicUrl: string
  success: boolean
  error?: string
}

interface UploadFile {
  file: File
  id: string
  progress: number
  status: 'pending' | 'uploading' | 'completed' | 'error'
  publicUrl?: string
  error?: string
}

const ImageBedUpload: React.FC<ImageBedUploadProps> = ({
  onUploadComplete,
  onClose
}) => {
  const { t } = useI18n()
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([])
  const [uploading, setUploading] = useState(false)

  const onDrop = useCallback((acceptedFiles: File[]) => {
    // Filter only image files
    const imageFiles = acceptedFiles.filter(file => file.type.startsWith('image/'))

    if (imageFiles.length === 0) {
      showNotification('warning', t('files.onlyImagesAllowed'))
      return
    }

    const newUploadFiles: UploadFile[] = imageFiles.map(file => ({
      file,
      id: Math.random().toString(36).substring(2, 11),
      progress: 0,
      status: 'pending'
    }))

    setUploadFiles(prev => [...prev, ...newUploadFiles])
  }, [t])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.bmp', '.svg']
    },
    multiple: true
  })

  const removeFile = (fileId: string) => {
    setUploadFiles(prev => prev.filter(f => f.id !== fileId))
  }

  const uploadToImageBed = async () => {
    if (uploadFiles.length === 0) return

    setUploading(true)
    const results: UploadResult[] = []

    try {
      // Get image bed folder first
      for (const uploadFile of uploadFiles) {
        let publicImageInfo: any = null

        try {
          // Update status to uploading
          setUploadFiles(prev => prev.map(f =>
            f.id === uploadFile.id
              ? { ...f, status: 'uploading', progress: 0 }
              : f
          ))

          // Simulate progress updates
          const progressInterval = setInterval(() => {
            setUploadFiles(prev => prev.map(f =>
              f.id === uploadFile.id && f.status === 'uploading'
                ? { ...f, progress: Math.min(f.progress + 10, 90) }
                : f
            ))
          }, 100)

          try {
            // Upload file directly to image bed
            const formData = new FormData()
            formData.append('file', uploadFile.file)
            publicImageInfo = await imageBedService.uploadToImageBed(formData)

            // Note: Verification step removed as it's not necessary for basic functionality

            // Clear progress interval
            clearInterval(progressInterval)

            // Update status to completed
            setUploadFiles(prev => prev.map(f =>
              f.id === uploadFile.id
                ? {
                  ...f,
                  status: 'completed',
                  progress: 100,
                  publicUrl: publicImageInfo.publicUrl
                }
                : f
            ))
          } catch (uploadError) {
            clearInterval(progressInterval)
            throw uploadError
          }

          results.push({
            id: publicImageInfo.id,
            originalName: uploadFile.file.name,
            publicUrl: publicImageInfo.publicUrl,
            success: true
          })

        } catch (error) {
          console.error('Upload failed for file:', uploadFile.file.name, error)

          const errorMessage = error instanceof Error ? error.message : 'Upload failed'

          // Update status to error
          setUploadFiles(prev => prev.map(f =>
            f.id === uploadFile.id
              ? { ...f, status: 'error', error: errorMessage }
              : f
          ))

          results.push({
            id: uploadFile.id,
            originalName: uploadFile.file.name,
            publicUrl: '',
            success: false,
            error: errorMessage
          })
        }
      }

      if (onUploadComplete) {
        onUploadComplete(results)
      }

    } catch (error) {
      console.error('Image bed upload failed:', error)
    } finally {
      setUploading(false)
    }
  }

  const copyPublicLink = async (publicUrl: string) => {
    try {
      await navigator.clipboard.writeText(publicUrl)
      // Show success feedback
      showNotification('success', t('files.linkCopied'))
    } catch (error) {
      console.error('Failed to copy link:', error)
      // Fallback for older browsers or when clipboard API is not available
      try {
        const textArea = document.createElement('textarea')
        textArea.value = publicUrl
        textArea.style.position = 'fixed'
        textArea.style.left = '-999999px'
        textArea.style.top = '-999999px'
        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()
        const successful = document.execCommand('copy')
        document.body.removeChild(textArea)
        if (successful) {
          showNotification('success', t('files.linkCopied'))
        } else {
          showNotification('error', t('files.copyFailed'))
        }
      } catch (fallbackError) {
        console.error('Fallback copy failed:', fallbackError)
        showNotification('error', t('files.copyFailed'))
      }
    }
  }

  const formatFileSize = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
  }

  return (
    <div className="bg-secondary rounded-lg p-6 max-w-2xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-primary">
          {t('files.uploadToImageBed')}
        </h2>
        {onClose && (
          <button
            onClick={onClose}
            className="text-comment hover:text-primary"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        )}
      </div>

      {/* Upload Area */}
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${isDragActive
          ? 'border-purple-500 bg-purple-500/10'
          : 'border-comment hover:border-purple-500'
          }`}
      >
        <input {...getInputProps()} />
        <PhotoIcon className="w-12 h-12 text-comment mx-auto mb-4" />
        <p className="text-primary font-medium mb-2">
          {isDragActive
            ? t('files.dropToUpload')
            : t('files.selectImages')
          }
        </p>
        <p className="text-comment text-sm">
          {t('files.supportedTypes')}: PNG, JPG, GIF, WebP, SVG
        </p>
      </div>

      {/* File List */}
      {uploadFiles.length > 0 && (
        <div className="mt-6">
          <h3 className="text-lg font-medium text-primary mb-4">
            {t('files.filesToUpload')} ({uploadFiles.length})
          </h3>

          <div className="space-y-3 max-h-64 overflow-y-auto">
            {uploadFiles.map((uploadFile) => (
              <div
                key={uploadFile.id}
                className="flex items-center space-x-3 p-3 bg-current-line rounded-lg"
              >
                {/* File Icon */}
                <div className="flex-shrink-0">
                  {uploadFile.file.type.startsWith('image/') ? (
                    <PhotoIcon className="w-8 h-8 text-green-500" />
                  ) : (
                    <PhotoIcon className="w-8 h-8 text-comment" />
                  )}
                </div>

                {/* File Info */}
                <div className="flex-1 min-w-0">
                  <p className="text-primary font-medium truncate">
                    {uploadFile.file.name}
                  </p>
                  <p className="text-comment text-sm">
                    {formatFileSize(uploadFile.file.size)}
                  </p>

                  {/* Progress Bar */}
                  {uploadFile.status === 'uploading' && (
                    <div className="mt-2">
                      <div className="bg-comment/20 rounded-full h-2">
                        <div
                          className="bg-purple-500 h-2 rounded-full transition-all"
                          style={{ width: `${uploadFile.progress}%` }}
                        />
                      </div>
                      <p className="text-xs text-comment mt-1">
                        {uploadFile.progress}%
                      </p>
                    </div>
                  )}

                  {/* Success State */}
                  {uploadFile.status === 'completed' && uploadFile.publicUrl && (
                    <div className="mt-2 flex items-center space-x-2">
                      <CheckIcon className="w-4 h-4 text-green-500" />
                      <span className="text-green-500 text-sm">
                        {t('files.uploadSuccess')}
                      </span>
                      <button
                        onClick={() => copyPublicLink(uploadFile.publicUrl!)}
                        className="text-purple-500 hover:text-purple-400 text-sm flex items-center space-x-1"
                      >
                        <LinkIcon className="w-4 h-4" />
                        <span>{t('files.copyPublicLink')}</span>
                      </button>
                    </div>
                  )}

                  {/* Error State */}
                  {uploadFile.status === 'error' && (
                    <div className="mt-2 flex items-center space-x-2">
                      <XMarkIcon className="w-4 h-4 text-red-500" />
                      <span className="text-red-500 text-sm">
                        {uploadFile.error || t('files.uploadFailed')}
                      </span>
                    </div>
                  )}
                </div>

                {/* Remove Button */}
                {uploadFile.status === 'pending' && (
                  <button
                    onClick={() => removeFile(uploadFile.id)}
                    className="text-comment hover:text-red-500"
                  >
                    <XMarkIcon className="w-5 h-5" />
                  </button>
                )}
              </div>
            ))}
          </div>

          {/* Upload Button */}
          <div className="mt-6 flex justify-end space-x-3">
            {onClose && (
              <Button
                variant="outline"
                onClick={onClose}
                disabled={uploading}
              >
                {t('common.cancel')}
              </Button>
            )}
            <Button
              variant="primary"
              onClick={uploadToImageBed}
              disabled={uploading || uploadFiles.length === 0}
              className="flex items-center space-x-2"
            >
              <CloudArrowUpIcon className="w-5 h-5" />
              <span>
                {uploading
                  ? t('common.processing')
                  : t('files.uploadToImageBed')
                }
              </span>
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}

export default ImageBedUpload