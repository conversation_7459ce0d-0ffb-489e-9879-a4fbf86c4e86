"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.imageBedController = exports.ImageBedController = void 0;
const image_bed_service_1 = require("../services/image-bed.service");
const logger_1 = __importDefault(require("../utils/logger"));
class ImageBedController {
    /**
     * Get or create image bed folder
     */
    async getOrCreateImageBedFolder(req, res) {
        try {
            const userId = req.user?.id;
            if (!userId) {
                res.status(401).json({ error: 'Unauthorized' });
                return;
            }
            const imageBedFolder = await image_bed_service_1.imageBedService.getOrCreateImageBedFolder(userId);
            res.json({
                success: true,
                data: imageBedFolder
            });
        }
        catch (error) {
            logger_1.default.error('Error getting or creating image bed folder:', error);
            res.status(500).json({
                error: 'Failed to get or create image bed folder'
            });
        }
    }
    /**
     * Upload image directly to image bed
     */
    async uploadToImageBed(req, res) {
        try {
            const userId = req.user?.id;
            if (!userId) {
                res.status(401).json({ error: 'Unauthorized' });
                return;
            }
            // Check if file was uploaded
            if (!req.file) {
                res.status(400).json({ error: 'No file uploaded' });
                return;
            }
            // Verify it's an image file
            if (!req.file.mimetype.startsWith('image/')) {
                res.status(400).json({ error: 'Only image files are allowed' });
                return;
            }
            const result = await image_bed_service_1.imageBedService.uploadToImageBed(req, userId);
            res.json({
                success: true,
                data: result
            });
        }
        catch (error) {
            logger_1.default.error('Error uploading to image bed:', error);
            if (error instanceof Error) {
                if (error.message.includes('not an image')) {
                    res.status(400).json({ error: error.message });
                    return;
                }
                if (error.message.includes('File too large')) {
                    res.status(413).json({ error: 'File too large. Maximum size is 10MB' });
                    return;
                }
            }
            res.status(500).json({
                error: 'Failed to upload to image bed'
            });
        }
    }
    /**
     * Move image to image bed
     */
    async moveImageToImageBed(req, res) {
        try {
            const userId = req.user?.id;
            if (!userId) {
                res.status(401).json({ error: 'Unauthorized' });
                return;
            }
            const { fileId } = req.params;
            if (!fileId) {
                res.status(400).json({ error: 'File ID is required' });
                return;
            }
            const language = req.headers['accept-language']?.split(',')[0] || 'zh-CN';
            const publicImageInfo = await image_bed_service_1.imageBedService.moveImageToImageBed(fileId, userId);
            // Notify other clients via WebSocket
            if (req.wsService) {
                req.wsService.sendToUser(userId, {
                    type: 'image_moved_to_bed',
                    payload: {
                        imageId: fileId,
                        publicUrl: publicImageInfo.publicUrl
                    },
                    timestamp: new Date()
                });
            }
            res.json({
                success: true,
                data: publicImageInfo
            });
        }
        catch (error) {
            logger_1.default.error('Error moving image to image bed:', error);
            if (error instanceof Error) {
                if (error.message.includes('not found') || error.message.includes('access denied')) {
                    res.status(404).json({ error: error.message });
                    return;
                }
                if (error.message.includes('not an image')) {
                    res.status(400).json({ error: error.message });
                    return;
                }
            }
            res.status(500).json({
                error: 'Failed to move image to image bed'
            });
        }
    }
    /**
     * Get public image info
     */
    async getPublicImageInfo(req, res) {
        try {
            const { imageId } = req.params;
            if (!imageId) {
                res.status(400).json({ error: 'Image ID is required' });
                return;
            }
            const publicImageInfo = await image_bed_service_1.imageBedService.getPublicImageInfo(imageId);
            res.json({
                success: true,
                data: publicImageInfo
            });
        }
        catch (error) {
            logger_1.default.error('Error getting public image info:', error);
            if (error instanceof Error && error.message.includes('not found')) {
                res.status(404).json({ error: 'Image not found or not public' });
                return;
            }
            res.status(500).json({
                error: 'Failed to get public image info'
            });
        }
    }
    /**
     * List image bed images
     */
    async listImageBedImages(req, res) {
        try {
            const userId = req.user?.id;
            if (!userId) {
                res.status(401).json({ error: 'Unauthorized' });
                return;
            }
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 20;
            const sortBy = req.query.sortBy || 'createdAt';
            const sortOrder = req.query.sortOrder || 'desc';
            const result = await image_bed_service_1.imageBedService.listImageBedImages(userId, {
                page,
                limit,
                sortBy,
                sortOrder
            });
            res.json({
                success: true,
                data: result
            });
        }
        catch (error) {
            logger_1.default.error('Error listing image bed images:', error);
            res.status(500).json({
                error: 'Failed to list image bed images'
            });
        }
    }
    /**
     * Remove image from image bed
     */
    async removeFromImageBed(req, res) {
        try {
            const userId = req.user?.id;
            if (!userId) {
                res.status(401).json({ error: 'Unauthorized' });
                return;
            }
            const { imageId } = req.params;
            const { targetFolderId } = req.body;
            if (!imageId) {
                res.status(400).json({ error: 'Image ID is required' });
                return;
            }
            await image_bed_service_1.imageBedService.removeFromImageBed(imageId, userId, targetFolderId);
            // Notify other clients via WebSocket
            if (req.wsService) {
                req.wsService.sendToUser(userId, {
                    type: 'image_removed_from_bed',
                    payload: {
                        imageId,
                        targetFolderId
                    },
                    timestamp: new Date()
                });
            }
            res.json({
                success: true,
                message: 'Image removed from image bed'
            });
        }
        catch (error) {
            logger_1.default.error('Error removing image from image bed:', error);
            if (error instanceof Error && error.message.includes('not found')) {
                res.status(404).json({ error: 'Image not found or access denied' });
                return;
            }
            res.status(500).json({
                error: 'Failed to remove image from image bed'
            });
        }
    }
    /**
     * Generate or get public link for an image
     */
    async generatePublicLink(req, res) {
        try {
            const userId = req.user?.id;
            if (!userId) {
                res.status(401).json({ error: 'Unauthorized' });
                return;
            }
            const { imageId } = req.params;
            if (!imageId) {
                res.status(400).json({ error: 'Image ID is required' });
                return;
            }
            const publicImageInfo = await image_bed_service_1.imageBedService.getPublicImageInfo(imageId);
            res.json({
                success: true,
                data: {
                    publicUrl: publicImageInfo.publicUrl
                }
            });
        }
        catch (error) {
            logger_1.default.error('Error generating public link:', error);
            if (error instanceof Error && error.message.includes('not found')) {
                res.status(404).json({ error: 'Image not found or not public' });
                return;
            }
            res.status(500).json({
                error: 'Failed to generate public link'
            });
        }
    }
    /**
     * Serve public image (for public access without authentication)
     */
    async servePublicImage(req, res) {
        try {
            const { imageId } = req.params;
            const size = req.query.size;
            if (!imageId) {
                res.status(400).json({ error: 'Image ID is required' });
                return;
            }
            logger_1.default.info(`Serving public image: ${imageId}, size: ${size}`);
            // Get public image info to verify it's public
            const publicImageInfo = await image_bed_service_1.imageBedService.getPublicImageInfo(imageId);
            logger_1.default.info(`Public image info retrieved: ${publicImageInfo.id}, public: true`);
            // Get file data directly from file service
            const { fileDao } = require('../dao/file.dao');
            const { storageService } = require('../services/storage.service');
            // Get file metadata
            const fileMetadata = await fileDao.getFileById(imageId);
            if (!fileMetadata) {
                logger_1.default.error(`File metadata not found for imageId: ${imageId}`);
                res.status(404).json({ error: 'File not found' });
                return;
            }
            logger_1.default.info(`File metadata found: ${fileMetadata.id}, mimeType: ${fileMetadata.mimeType}`);
            // Get file chunks
            const fileChunks = await fileDao.getFileChunks(imageId);
            if (!fileChunks) {
                logger_1.default.error(`File chunks not found for imageId: ${imageId}`);
                res.status(404).json({ error: 'File chunks not found' });
                return;
            }
            logger_1.default.info(`File chunks found for imageId: ${imageId}`);
            // Download file from storage
            const fileBuffer = await storageService.downloadFileChunked(fileChunks);
            logger_1.default.info(`File downloaded from storage, size: ${fileBuffer.length} bytes`);
            // Set appropriate headers
            res.setHeader('Content-Type', fileMetadata.mimeType);
            res.setHeader('Content-Length', fileBuffer.length);
            res.setHeader('Cache-Control', 'public, max-age=31536000'); // Cache for 1 year
            res.setHeader('Content-Disposition', `inline; filename="${fileMetadata.originalName}"`);
            logger_1.default.info(`Serving public image: ${imageId}, size: ${fileBuffer.length} bytes`);
            // Send the file content
            res.send(fileBuffer);
        }
        catch (error) {
            logger_1.default.error('Error serving public image:', error);
            if (error instanceof Error && error.message.includes('not found')) {
                res.status(404).json({ error: 'Image not found or not public' });
                return;
            }
            res.status(500).json({
                error: 'Failed to serve public image'
            });
        }
    }
    /**
     * Clean up duplicate image bed folders
     */
    async cleanupImageBedFolders(req, res) {
        try {
            const userId = req.user?.id;
            if (!userId) {
                res.status(401).json({ error: 'Unauthorized' });
                return;
            }
            await image_bed_service_1.imageBedService.forceCleanupImageBedFolders(userId);
            res.json({
                success: true,
                message: 'Image bed folders cleaned up successfully'
            });
        }
        catch (error) {
            logger_1.default.error('Error cleaning up image bed folders:', error);
            res.status(500).json({
                error: 'Failed to clean up image bed folders'
            });
        }
    }
}
exports.ImageBedController = ImageBedController;
exports.imageBedController = new ImageBedController();
//# sourceMappingURL=image-bed.controller.js.map