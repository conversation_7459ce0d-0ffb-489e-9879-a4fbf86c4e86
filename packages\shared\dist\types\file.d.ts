export interface FileMetadata {
    id: string;
    userId: string;
    filename: string;
    originalName: string;
    mimeType: string;
    size: number;
    checksum: string;
    folderId?: string;
    isPublic: boolean;
    uploadedAt: Date;
    modifiedAt: Date;
    tags: string[];
    storageNodes: StorageNode[];
    isDeleted?: boolean;
    deletedAt?: Date;
}
export interface StorageNode {
    id: string;
    url: string;
    region: string;
    isHealthy: boolean;
}
export interface Folder {
    id: string;
    userId: string;
    name: string;
    parentId?: string;
    path: string;
    createdAt: Date;
    modifiedAt: Date;
    isDeleted?: boolean;
    deletedAt?: Date;
}
export interface FileUpload {
    file: File | Buffer;
    filename: string;
    mimeType: string;
    folderId?: string;
    tags?: string[];
}
export interface FileInfo {
    id: string;
    filename: string;
    size: number;
    mimeType: string;
    uploadedAt: Date;
    downloadUrl: string;
}
export interface FileList {
    files: FileMetadata[];
    folders: Folder[];
    totalCount: number;
    hasMore: boolean;
}
export interface ShareLink {
    id: string;
    fileId: string;
    userId: string;
    token: string;
    permissions: Permission[];
    expiresAt?: Date;
    password?: string;
    downloadCount: number;
    maxDownloads?: number;
    createdAt: Date;
}
export interface Permission {
    type: 'read' | 'write' | 'delete';
    granted: boolean;
}
export interface ShareOptions {
    permissions: Permission[];
    expiresAt?: Date;
    password?: string;
    maxDownloads?: number;
}
export interface SearchQuery {
    query: string;
    fileType?: string;
    dateRange?: {
        from: Date;
        to: Date;
    };
    tags?: string[];
    folderId?: string;
}
export interface FolderData {
    name: string;
    parentId?: string;
}
export interface Pagination {
    page: number;
    limit: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    fileType?: string;
}
//# sourceMappingURL=file.d.ts.map