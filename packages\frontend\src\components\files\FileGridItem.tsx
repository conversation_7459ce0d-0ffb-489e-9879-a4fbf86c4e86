import React from 'react'
import { FileItem } from '../../pages/Files'
import { cn } from '../../utils/cn'
import { CheckIcon } from '@heroicons/react/24/outline'
import ImageBedFolderIcon from '../icons/ImageBedFolderIcon'

interface FileGridItemProps {
  file: FileItem
  selected: boolean
  onSelect: (selected: boolean) => void
  onOpen: () => void
  icon: React.ReactNode
  formattedSize: string
}

const FileGridItem: React.FC<FileGridItemProps> = ({
  file,
  selected,
  onSelect,
  onOpen,
  icon,
  formattedSize
}) => {
  const handleClick = (e: React.MouseEvent) => {
    if (e.ctrlKey || e.metaKey) {
      onSelect(!selected)
    } else {
      onOpen()
    }
  }

  const handleCheckboxClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    // Prevent selecting image bed folder
    if (file.isImageBedFolder) {
      return
    }
    onSelect(!selected)
  }

  return (
    <div
      data-file-item="true"
      className={cn(
        'relative rounded-lg border-2 transition-all cursor-pointer hover:shadow-md overflow-hidden',
        'flex flex-col h-full min-h-[140px]',
        selected
          ? 'border-purple-500 bg-purple-500/10'
          : 'border-primary hover:border-comment'
      )}
      onClick={handleClick}
    >
      {/* Selection Checkbox - 图床文件夹不显示勾选框 */}
      {!file.isImageBedFolder && (
        <div
          className={cn(
            'absolute top-2 right-2 w-5 h-5 rounded-full border-2 flex items-center justify-center transition-all z-10',
            selected
              ? 'bg-purple-500 border-purple-500'
              : 'border-comment hover:border-purple-500 cursor-pointer bg-white/80 backdrop-blur-sm'
          )}
          onClick={handleCheckboxClick}
        >
          {selected && <CheckIcon className="w-3 h-3 text-white" />}
        </div>
      )}

      {/* Content Area - 占满大部分空间 */}
      <div className="flex-1 flex items-center justify-center p-2 min-h-0">
        {file.thumbnailUrl && file.type !== 'folder' ? (
          <img
            src={file.thumbnailUrl}
            alt={file.name}
            className="w-full h-full object-cover rounded max-h-24"
          />
        ) : file.isImageBedFolder ? (
          <div className="w-full h-full flex items-center justify-center min-h-[80px]">
            <ImageBedFolderIcon className="w-16 h-16" />
          </div>
        ) : (
          <div className="w-full h-full flex items-center justify-center min-h-[80px]">
            {React.cloneElement(icon as React.ReactElement, {
              className: 'w-16 h-16'
            })}
          </div>
        )}
      </div>

      {/* File Name - 居中显示，固定高度 */}
      <div className="px-2 pb-2 flex-shrink-0">
        <h3
          className="text-sm font-medium text-primary text-center leading-tight"
          title={file.name}
        >
          <span className="block truncate">
            {file.name}
          </span>
        </h3>

        {/* File Size - 图床文件夹不显示大小信息 */}
        {formattedSize && !file.isImageBedFolder && (
          <p className="text-xs text-comment text-center mt-1 truncate">
            {formattedSize}
          </p>
        )}
      </div>
    </div>
  )
}

export default FileGridItem