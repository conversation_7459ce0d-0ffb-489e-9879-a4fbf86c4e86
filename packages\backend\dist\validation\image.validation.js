"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateImageListQuery = exports.validateImageConvert = exports.validateImageUpload = exports.validateImageFiles = exports.validateImageFile = exports.imageListQuerySchema = exports.imageConvertSchema = exports.imageUploadSchema = void 0;
const joi_1 = __importDefault(require("joi"));
const image_model_1 = require("../models/image.model");
// Image upload validation schema
exports.imageUploadSchema = joi_1.default.object({
    folderId: joi_1.default.alternatives().try(joi_1.default.string().uuid(), joi_1.default.string().regex(/^[0-9a-fA-F]{24}$/) // MongoDB ObjectId format
    ).optional(),
    isPublic: joi_1.default.boolean().optional().default(false),
    tags: joi_1.default.string().optional().allow(''),
    quality: joi_1.default.number().integer().min(1).max(100).optional(),
    format: joi_1.default.string().valid(...Object.values(image_model_1.ImageFormat)).optional(),
    width: joi_1.default.number().integer().min(1).max(4000).optional(),
    height: joi_1.default.number().integer().min(1).max(4000).optional(),
    fit: joi_1.default.string().valid('cover', 'contain', 'fill', 'inside', 'outside').optional().default('cover'),
    generateThumbnails: joi_1.default.string().valid('true', 'false').optional().default('true')
});
// Image conversion validation schema
exports.imageConvertSchema = joi_1.default.object({
    format: joi_1.default.string().valid(...Object.values(image_model_1.ImageFormat)).required()
});
// Image list query validation schema
exports.imageListQuerySchema = joi_1.default.object({
    page: joi_1.default.number().integer().min(1).optional().default(1),
    limit: joi_1.default.number().integer().min(1).max(100).optional().default(20),
    isPublic: joi_1.default.string().valid('true', 'false').optional()
});
// File validation for multer
const validateImageFile = (file) => {
    // Check file size (max 10MB)
    const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    if (file.size > MAX_FILE_SIZE) {
        return 'File size exceeds 10MB limit';
    }
    // Check MIME type
    const allowedMimeTypes = [
        'image/jpeg',
        'image/png',
        'image/webp',
        'image/gif',
        'image/bmp',
        'image/tiff'
    ];
    if (!allowedMimeTypes.includes(file.mimetype)) {
        return `Unsupported file type: ${file.mimetype}. Allowed types: ${allowedMimeTypes.join(', ')}`;
    }
    // Check file extension
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif', '.bmp', '.tiff', '.tif'];
    const fileExtension = file.originalname.toLowerCase().substring(file.originalname.lastIndexOf('.'));
    if (!allowedExtensions.includes(fileExtension)) {
        return `Unsupported file extension: ${fileExtension}. Allowed extensions: ${allowedExtensions.join(', ')}`;
    }
    return null; // No validation errors
};
exports.validateImageFile = validateImageFile;
// Validate multiple image files
const validateImageFiles = (files) => {
    const errors = [];
    // Check total number of files
    const MAX_FILES = 20;
    if (files.length > MAX_FILES) {
        errors.push(`Too many files. Maximum ${MAX_FILES} files allowed per upload.`);
        return errors;
    }
    // Validate each file
    files.forEach((file, index) => {
        const error = (0, exports.validateImageFile)(file);
        if (error) {
            errors.push(`File ${index + 1} (${file.originalname}): ${error}`);
        }
    });
    // Check total size
    const totalSize = files.reduce((sum, file) => sum + file.size, 0);
    const MAX_TOTAL_SIZE = 50 * 1024 * 1024; // 50MB total
    if (totalSize > MAX_TOTAL_SIZE) {
        errors.push(`Total file size exceeds 50MB limit. Current total: ${(totalSize / 1024 / 1024).toFixed(2)}MB`);
    }
    return errors;
};
exports.validateImageFiles = validateImageFiles;
// Middleware for validating image upload request body
const validateImageUpload = (req, res, next) => {
    const { error } = exports.imageUploadSchema.validate(req.body);
    if (error) {
        return res.status(400).json({
            error: 'Validation error',
            details: error.details.map(detail => detail.message)
        });
    }
    next();
};
exports.validateImageUpload = validateImageUpload;
// Middleware for validating image conversion request body
const validateImageConvert = (req, res, next) => {
    const { error } = exports.imageConvertSchema.validate(req.body);
    if (error) {
        return res.status(400).json({
            error: 'Validation error',
            details: error.details.map(detail => detail.message)
        });
    }
    next();
};
exports.validateImageConvert = validateImageConvert;
// Middleware for validating image list query parameters
const validateImageListQuery = (req, res, next) => {
    const { error, value } = exports.imageListQuerySchema.validate(req.query);
    if (error) {
        return res.status(400).json({
            error: 'Validation error',
            details: error.details.map(detail => detail.message)
        });
    }
    // Set validated values back to req.query
    req.query = value;
    next();
};
exports.validateImageListQuery = validateImageListQuery;
//# sourceMappingURL=image.validation.js.map