import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  Monitor,
  Smartphone,
  Tablet,
  MapPin,
  Clock,
  Shield,
  Trash2,
  AlertTriangle,
  ArrowLeft,
  RefreshCw
} from 'lucide-react'
import { <PERSON><PERSON>, Card } from '../components/ui'
import { useAuthStore } from '../stores'
import ThemeLanguageToggle from '@/components/ThemeLanguageToggle'

const SessionManagement: React.FC = () => {
  const navigate = useNavigate()
  const { user, sessions, getSessions, revokeSession, revokeAllSessions, isLoading } = useAuthStore()

  const [loadingSessions, setLoadingSessions] = useState(false)
  const [revokingSession, setRevokingSession] = useState<string | null>(null)
  const [showRevokeAllConfirm, setShowRevokeAllConfirm] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  useEffect(() => {
    if (!user) {
      navigate('/login')
      return
    }

    loadSessions()
  }, [user, navigate])

  const loadSessions = async () => {
    try {
      setLoadingSessions(true)
      setError('')
      await getSessions()
    } catch (err) {
      setError('Failed to load sessions. Please try again.')
      console.error('Load sessions error:', err)
    } finally {
      setLoadingSessions(false)
    }
  }

  const handleRevokeSession = async (sessionId: string) => {
    try {
      setRevokingSession(sessionId)
      setError('')
      await revokeSession(sessionId)
      setSuccess('Session revoked successfully')
      setTimeout(() => setSuccess(''), 3000)
    } catch (err) {
      setError('Failed to revoke session. Please try again.')
      console.error('Revoke session error:', err)
    } finally {
      setRevokingSession(null)
    }
  }

  const handleRevokeAllSessions = async () => {
    try {
      setError('')
      await revokeAllSessions()
      setSuccess('All other sessions have been revoked')
      setShowRevokeAllConfirm(false)
      setTimeout(() => setSuccess(''), 3000)
    } catch (err) {
      setError('Failed to revoke sessions. Please try again.')
      console.error('Revoke all sessions error:', err)
    }
  }

  const getDeviceIcon = (device: string) => {
    const deviceLower = device.toLowerCase()
    if (deviceLower.includes('phone') || deviceLower.includes('mobile')) {
      return <Smartphone className="w-5 h-5" />
    }
    if (deviceLower.includes('tablet') || deviceLower.includes('ipad')) {
      return <Tablet className="w-5 h-5" />
    }
    return <Monitor className="w-5 h-5" />
  }

  const formatLastActive = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins} minutes ago`
    if (diffHours < 24) return `${diffHours} hours ago`
    if (diffDays < 7) return `${diffDays} days ago`
    return date.toLocaleDateString()
  }

  const activeSessions = sessions.filter(session => !session.isCurrent)
  const currentSession = sessions.find(session => session.isCurrent)

  return (
    <div className="min-h-screen bg-dracula-bg py-8 px-4">
      {/* 主题和语言切换按钮 */}
      <ThemeLanguageToggle className="absolute top-4 right-4" />
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="text-center">
          <h1 className="text-3xl md:text-4xl font-bold text-dracula-foreground mb-2">Session Management</h1>
          <p className="text-dracula-comment">
            Manage your active sessions and secure your account
          </p>
        </div>

        {error && (
          <div className="p-4 bg-dracula-red/10 border border-dracula-red/20 rounded-lg">
            <p className="text-dracula-red text-sm">{error}</p>
          </div>
        )}

        {success && (
          <div className="p-4 bg-dracula-green/10 border border-dracula-green/20 rounded-lg">
            <p className="text-dracula-green text-sm">{success}</p>
          </div>
        )}

        {/* Current Session */}
        {currentSession && (
          <Card>
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-4">
                <div className="text-dracula-green">
                  {getDeviceIcon(currentSession.deviceInfo.device)}
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <h3 className="font-semibold text-dracula-foreground">
                      {currentSession.deviceInfo.browser} on {currentSession.deviceInfo.os}
                    </h3>
                    <span className="px-2 py-1 bg-dracula-green/20 text-dracula-green text-xs rounded-full">
                      Current Session
                    </span>
                  </div>
                  <div className="space-y-1 text-sm text-dracula-comment">
                    <div className="flex items-center space-x-1">
                      <MapPin className="w-3 h-3" />
                      <span>{currentSession.location || currentSession.ipAddress}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="w-3 h-3" />
                      <span>Active now</span>
                    </div>
                  </div>
                </div>
              </div>
              <Shield className="w-5 h-5 text-dracula-green" />
            </div>
          </Card>
        )}

        {/* Active Sessions */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-dracula-foreground">
              Other Active Sessions ({activeSessions.length})
            </h2>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={loadSessions}
                loading={loadingSessions}
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh
              </Button>
              {activeSessions.length > 0 && (
                <Button
                  variant="danger"
                  size="sm"
                  onClick={() => setShowRevokeAllConfirm(true)}
                  loading={isLoading}
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Revoke All
                </Button>
              )}
            </div>
          </div>

          {activeSessions.length === 0 ? (
            <Card>
              <div className="text-center py-8">
                <Shield className="w-12 h-12 text-dracula-comment mx-auto mb-4" />
                <h3 className="font-medium text-dracula-foreground mb-2">No Other Active Sessions</h3>
                <p className="text-dracula-comment">
                  You're only signed in on this device. Great job keeping your account secure!
                </p>
              </div>
            </Card>
          ) : (
            <div className="space-y-3">
              {activeSessions.map((session) => (
                <Card key={session.id} hover>
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4">
                      <div className="text-dracula-comment">
                        {getDeviceIcon(session.deviceInfo.device)}
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-dracula-foreground mb-1">
                          {session.deviceInfo.browser} on {session.deviceInfo.os}
                        </h3>
                        <div className="space-y-1 text-sm text-dracula-comment">
                          <div className="flex items-center space-x-1">
                            <MapPin className="w-3 h-3" />
                            <span>{session.location || session.ipAddress}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Clock className="w-3 h-3" />
                            <span>Last active {formatLastActive(session.lastActive)}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRevokeSession(session.id)}
                      loading={revokingSession === session.id}
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Revoke
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </div>

        {/* Security Tips */}
        <Card>
          <div className="space-y-4">
            <h3 className="font-semibold text-dracula-foreground flex items-center">
              <Shield className="w-5 h-5 mr-2" />
              Security Tips
            </h3>
            <div className="space-y-2 text-sm text-dracula-comment">
              <p>• Regularly review your active sessions and revoke any you don't recognize</p>
              <p>• Always sign out when using shared or public computers</p>
              <p>• Enable two-factor authentication for additional security</p>
              <p>• Use strong, unique passwords for your account</p>
            </div>
          </div>
        </Card>

        {/* Revoke All Confirmation Modal */}
        {showRevokeAllConfirm && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
            <Card className="max-w-md w-full">
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <AlertTriangle className="w-6 h-6 text-dracula-orange" />
                  <h3 className="font-semibold text-dracula-foreground">Confirm Action</h3>
                </div>
                <p className="text-dracula-comment">
                  Are you sure you want to revoke all other active sessions? This will sign you out
                  from all other devices and you'll need to sign in again on those devices.
                </p>
                <div className="flex space-x-3">
                  <Button
                    variant="danger"
                    onClick={handleRevokeAllSessions}
                    loading={isLoading}
                    className="flex-1"
                  >
                    Yes, Revoke All
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setShowRevokeAllConfirm(false)}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        )}

        {/* Back Button */}
        <div className="text-center">
          <button
            onClick={() => navigate('/dashboard')}
            className="inline-flex items-center space-x-2 text-dracula-purple hover:text-dracula-pink"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Dashboard</span>
          </button>
        </div>
      </div>
    </div>
  )
}

export default SessionManagement