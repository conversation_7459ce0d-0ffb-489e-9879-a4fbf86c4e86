import { Router } from 'express';
import { fileController, uploadSingle, uploadMultiple, uploadChunk } from '../controllers/file.controller';
import { authMiddleware } from '../middleware/auth.middleware';
import { validateRequest } from '../middleware/validation.middleware';
import { fileValidation } from '../validation/file.validation';

const router = Router();

// Public share access routes (no auth required) - must be before authMiddleware
router.get(
  '/public/:token',
  validateRequest(fileValidation.accessPublicShare),
  fileController.accessPublicShare.bind(fileController)
);

// Public share access with password (no auth required)
router.post(
  '/public/:token',
  validateRequest(fileValidation.accessPublicShareWithPassword),
  fileController.accessPublicShareWithPassword.bind(fileController)
);

router.post(
  '/public/:token/download',
  validateRequest(fileValidation.downloadPublicShare),
  fileController.downloadPublicShare.bind(fileController)
);

router.get(
  '/public/:token/preview',
  validateRequest(fileValidation.previewPublicShare),
  fileController.previewPublicShare.bind(fileController)
);

// Debug endpoint (no auth required for debugging)
router.get(
  '/debug/data',
  fileController.debugFileData.bind(fileController)
);

// Apply authentication middleware to all other routes
router.use(authMiddleware);

// File upload routes
router.post(
  '/upload',
  uploadSingle,
  validateRequest(fileValidation.uploadFile),
  fileController.uploadFile.bind(fileController)
);

router.post(
  '/upload/batch',
  uploadMultiple,
  validateRequest(fileValidation.uploadFiles),
  fileController.uploadFiles.bind(fileController)
);

// Chunked upload routes
router.post(
  '/upload/chunked/init',
  validateRequest(fileValidation.initChunkedUpload),
  fileController.initChunkedUpload.bind(fileController)
);

router.post(
  '/upload/chunked/:uploadId/chunk',
  uploadChunk,
  validateRequest(fileValidation.uploadChunk),
  fileController.uploadChunk.bind(fileController)
);

router.post(
  '/upload/chunked/:uploadId/complete',
  validateRequest(fileValidation.completeChunkedUpload),
  fileController.completeChunkedUpload.bind(fileController)
);

router.get(
  '/upload/chunked/:uploadId/progress',
  validateRequest(fileValidation.getUploadProgress),
  fileController.getUploadProgress.bind(fileController)
);

// File management routes
router.get(
  '/list',
  validateRequest(fileValidation.listFiles),
  fileController.listFiles.bind(fileController)
);

router.get(
  '/search',
  validateRequest(fileValidation.searchFiles),
  fileController.searchFiles.bind(fileController)
);

router.get(
  '/:fileId/download',
  validateRequest(fileValidation.downloadFile),
  fileController.downloadFile.bind(fileController)
);

router.get(
  '/:fileId/preview',
  validateRequest(fileValidation.previewFile),
  fileController.previewFile.bind(fileController)
);

router.get(
  '/:fileId/info',
  validateRequest(fileValidation.getFileInfo),
  fileController.getFileInfo.bind(fileController)
);

router.put(
  '/:fileId/move',
  validateRequest(fileValidation.moveFile),
  fileController.moveFile.bind(fileController)
);

router.delete(
  '/:fileId',
  validateRequest(fileValidation.deleteFile),
  fileController.deleteFile.bind(fileController)
);

// Folder management routes
router.post(
  '/folders',
  validateRequest(fileValidation.createFolder),
  fileController.createFolder.bind(fileController)
);

router.put(
  '/folders/:folderId/rename',
  validateRequest(fileValidation.renameFolder),
  fileController.renameFolder.bind(fileController)
);

router.get(
  '/folders/:folderId',
  validateRequest(fileValidation.getFolderById),
  fileController.getFolderById.bind(fileController)
);

router.get(
  '/folders/:folderId/info',
  validateRequest(fileValidation.getFolderInfo),
  fileController.getFolderInfo.bind(fileController)
);

router.put(
  '/folders/:folderId/move',
  validateRequest(fileValidation.moveFolder),
  fileController.moveFolder.bind(fileController)
);

router.delete(
  '/folders/:folderId',
  validateRequest(fileValidation.deleteFolder),
  fileController.deleteFolder.bind(fileController)
);

// File rename and trash operations
router.put(
  '/:fileId/rename',
  validateRequest(fileValidation.renameFile),
  fileController.renameFile.bind(fileController)
);

router.post(
  '/:fileId/duplicate',
  validateRequest(fileValidation.duplicateFile),
  fileController.duplicateFile.bind(fileController)
);

router.post(
  '/:fileId/copy',
  validateRequest(fileValidation.copyFile),
  fileController.copyFile.bind(fileController)
);

router.put(
  '/:fileId/trash',
  validateRequest(fileValidation.moveFileToTrash),
  fileController.moveFileToTrash.bind(fileController)
);

// Trash/Recycle bin operations
router.get(
  '/trash',
  validateRequest(fileValidation.getTrashContents),
  fileController.getTrashContents.bind(fileController)
);

router.put(
  '/:fileId/restore',
  validateRequest(fileValidation.restoreFromTrash),
  fileController.restoreFromTrash.bind(fileController)
);

router.delete(
  '/trash/empty',
  fileController.emptyTrash.bind(fileController)
);

// File sharing routes
router.post(
  '/:fileId/share',
  validateRequest(fileValidation.shareFile),
  fileController.shareFile.bind(fileController)
);

router.get(
  '/:fileId/shares',
  validateRequest(fileValidation.getFileShares),
  fileController.getFileShares.bind(fileController)
);

router.put(
  '/shares/:shareId',
  validateRequest(fileValidation.updateShareLink),
  fileController.updateShareLink.bind(fileController)
);

router.delete(
  '/shares/:shareId',
  validateRequest(fileValidation.revokeShareLink),
  fileController.revokeShareLink.bind(fileController)
);

router.get(
  '/shares/stats',
  fileController.getShareStats.bind(fileController)
);

router.get(
  '/shares/stats/comprehensive',
  fileController.getComprehensiveShareStats.bind(fileController)
);

router.get(
  '/shares',
  fileController.getUserShares.bind(fileController)
);

// Storage statistics
router.get(
  '/stats',
  fileController.getStorageStats.bind(fileController)
);

export default router;