"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.imageBedService = void 0;
const file_dao_1 = require("../dao/file.dao");
const logger_1 = __importDefault(require("../utils/logger"));
const crypto_1 = __importDefault(require("crypto"));
const storage_service_1 = require("./storage.service");
class ImageBedService {
    constructor() {
        this.IMAGE_BED_FOLDER_NAME = 'Image Bed';
    }
    /**
     * Get or create the image bed folder for a user
     * Ensures only one image bed folder exists per user
     */
    async getOrCreateImageBedFolder(userId) {
        try {
            // First try to find existing image bed folder
            const existingFolder = await this.findImageBedFolder(userId);
            if (existingFolder) {
                logger_1.default.info(`Found existing image bed folder for user ${userId}: ${existingFolder.id}`);
                return this.mapFolderToImageBedFolder(existingFolder);
            }
            // Create new image bed folder
            const newFolder = await this.createImageBedFolder(userId);
            logger_1.default.info(`Created new image bed folder for user ${userId}: ${newFolder.id}`);
            return newFolder;
        }
        catch (error) {
            logger_1.default.error('Error getting or creating image bed folder:', error);
            throw new Error('Failed to get or create image bed folder');
        }
    }
    /**
     * Find existing image bed folder (only in root directory)
     */
    async findImageBedFolder(userId) {
        const folders = await file_dao_1.fileDao.getFoldersByUserId(userId, undefined); // Root level folders only
        // Look for folder with exact image bed name in root directory only
        for (const folder of folders) {
            if (folder.name === this.IMAGE_BED_FOLDER_NAME && !folder.parentId) {
                return folder;
            }
        }
        return null;
    }
    /**
     * Clean up duplicate image bed folders in ROOT directory only
     * Note: "Image Bed" folders in subdirectories are allowed as regular folders
     */
    async cleanupDuplicateImageBedFolders(userId) {
        try {
            logger_1.default.info(`Checking for duplicate image bed folders in root directory for user ${userId}`);
            const rootFolders = await file_dao_1.fileDao.getFoldersByUserId(userId, undefined);
            const rootImageBedFolders = rootFolders.filter(folder => folder.name === this.IMAGE_BED_FOLDER_NAME && !folder.parentId);
            // If there are multiple image bed folders in ROOT directory, keep only the first one
            if (rootImageBedFolders.length > 1) {
                logger_1.default.warn(`Found ${rootImageBedFolders.length} image bed folders in root directory, cleaning up duplicates`);
                const keepFolder = rootImageBedFolders[0];
                for (let i = 1; i < rootImageBedFolders.length; i++) {
                    const duplicateFolder = rootImageBedFolders[i];
                    logger_1.default.info(`Merging duplicate root image bed folder ${duplicateFolder.id} into ${keepFolder.id}`);
                    // Move files from duplicate to the kept folder
                    const files = await file_dao_1.fileDao.getFilesByUserId(userId, duplicateFolder.id);
                    for (const file of files) {
                        if (file.mimeType.startsWith('image/')) {
                            await file_dao_1.fileDao.updateFile(file.id, {
                                folderId: keepFolder.id,
                                isPublic: true,
                                modifiedAt: new Date()
                            });
                            logger_1.default.info(`Moved image ${file.id} from duplicate folder to main image bed`);
                        }
                    }
                    // Delete the duplicate folder (only in root directory)
                    await file_dao_1.fileDao.deleteFolder(duplicateFolder.id);
                    logger_1.default.info(`Deleted duplicate root image bed folder: ${duplicateFolder.id}`);
                }
            }
            logger_1.default.info(`Image bed folder cleanup completed. Root directory has ${rootImageBedFolders.length > 0 ? 1 : 0} image bed folder(s)`);
        }
        catch (error) {
            logger_1.default.error('Error cleaning up duplicate image bed folders:', error);
            // Don't throw error, just log it as this is cleanup
        }
    }
    /**
     * Create image bed folder (strictly in root directory only)
     */
    async createImageBedFolder(userId) {
        // Double-check that no image bed folder exists in root
        const existingFolder = await this.findImageBedFolder(userId);
        if (existingFolder) {
            logger_1.default.warn(`Image bed folder already exists for user ${userId}, returning existing folder`);
            return this.mapFolderToImageBedFolder(existingFolder);
        }
        const folderName = this.IMAGE_BED_FOLDER_NAME;
        const folder = {
            id: crypto_1.default.randomUUID(),
            userId,
            name: folderName,
            parentId: undefined, // Explicitly set to undefined for root level
            path: `/${folderName}`,
            createdAt: new Date(),
            modifiedAt: new Date()
        };
        try {
            const createdFolder = await file_dao_1.fileDao.createFolder(folder);
            logger_1.default.info(`Image bed folder created in root directory for user ${userId}: ${createdFolder.id}`);
            // Verify the created folder is indeed in root
            if (createdFolder.parentId) {
                logger_1.default.error(`Created image bed folder has parentId: ${createdFolder.parentId}, this should not happen`);
                throw new Error('Image bed folder must be created in root directory');
            }
            return this.mapFolderToImageBedFolder(createdFolder);
        }
        catch (error) {
            logger_1.default.error('Error creating image bed folder:', error);
            // If folder already exists, try to find it
            const existingFolder = await this.findImageBedFolder(userId);
            if (existingFolder) {
                logger_1.default.info(`Found existing image bed folder after creation error for user ${userId}: ${existingFolder.id}`);
                return this.mapFolderToImageBedFolder(existingFolder);
            }
            throw new Error('Failed to create image bed folder');
        }
    }
    /**
     * Upload image directly to image bed
     */
    async uploadToImageBed(req, userId) {
        try {
            // Get or create image bed folder first
            const imageBedFolder = await this.getOrCreateImageBedFolder(userId);
            // Get the uploaded file from multer
            const uploadedFile = req.file;
            if (!uploadedFile) {
                throw new Error('No file uploaded');
            }
            // Verify it's an image
            if (!uploadedFile.mimetype.startsWith('image/')) {
                throw new Error('Only image files are allowed in image bed');
            }
            // Services are now imported at the top of the file
            // Generate unique file ID and metadata
            const fileId = crypto_1.default.randomUUID();
            const fileBuffer = uploadedFile.buffer;
            const originalName = uploadedFile.originalname;
            const mimeType = uploadedFile.mimetype;
            // Generate unique filename
            const fileExtension = originalName.split('.').pop() || 'jpg';
            const uniqueFilename = `${fileId}.${fileExtension}`;
            // Upload file chunks to distributed storage
            const fileChunks = await storage_service_1.storageService.uploadFileChunked(fileId, fileBuffer, {
                originalName,
                mimeType,
                isPublic: true
            });
            // Create file metadata directly for image bed
            const fileMetadata = {
                id: fileId,
                userId,
                filename: uniqueFilename,
                originalName,
                mimeType,
                size: fileBuffer.length,
                checksum: fileChunks.checksum,
                folderId: imageBedFolder.id, // Assign to image bed folder
                isPublic: true, // Images in image bed are always public
                uploadedAt: new Date(),
                modifiedAt: new Date(),
                tags: [],
                storageNodes: fileChunks.chunks.map(chunk => ({
                    id: chunk.storageNodes[0], // Use first storage node
                    url: '', // Will be filled by storage service
                    region: 'default',
                    isHealthy: true
                }))
            };
            // Save metadata to database
            await file_dao_1.fileDao.createFile(fileMetadata);
            // Store chunk information for retrieval
            await file_dao_1.fileDao.saveFileChunks(fileId, fileChunks);
            logger_1.default.info(`Image uploaded to image bed: ${fileId}, folder: ${imageBedFolder.id}, isPublic: true`);
            // Generate public URL
            const publicUrl = await this.generatePublicUrl(fileId);
            return {
                id: fileId,
                originalName,
                publicUrl,
                cdnUrl: undefined, // Not using CDN for image bed
                thumbnails: undefined, // Not generating thumbnails for image bed
                accessCount: 0,
                createdAt: fileMetadata.uploadedAt
            };
        }
        catch (error) {
            logger_1.default.error('Error uploading to image bed:', error);
            throw error;
        }
    }
    /**
     * Move an image file to the image bed
     */
    async moveImageToImageBed(fileId, userId) {
        try {
            // Get the file
            const file = await file_dao_1.fileDao.getFileById(fileId);
            if (!file || file.userId !== userId) {
                throw new Error('File not found or access denied');
            }
            // Check if it's an image
            if (!file.mimeType.startsWith('image/')) {
                throw new Error('File is not an image');
            }
            // Get or create image bed folder
            const imageBedFolder = await this.getOrCreateImageBedFolder(userId);
            // Move the file to image bed folder
            await file_dao_1.fileDao.updateFile(fileId, {
                folderId: imageBedFolder.id,
                modifiedAt: new Date()
            });
            // Update file to be public
            await file_dao_1.fileDao.updateFile(fileId, {
                isPublic: true,
                modifiedAt: new Date()
            });
            // Generate public URL
            const publicUrl = await this.generatePublicUrl(fileId);
            return {
                id: file.id,
                originalName: file.originalName,
                publicUrl,
                cdnUrl: undefined, // Not using image service
                thumbnails: undefined, // Not using image service
                accessCount: 0, // Default to 0 if not available
                createdAt: file.uploadedAt
            };
        }
        catch (error) {
            logger_1.default.error('Error moving image to image bed:', error);
            throw error;
        }
    }
    /**
     * Generate public URL for an image
     */
    async generatePublicUrl(imageId) {
        const baseUrl = process.env.API_BASE_URL || 'http://localhost:3001';
        return `${baseUrl}/api/image-bed/public/${imageId}`;
    }
    /**
     * Get public image information
     */
    async getPublicImageInfo(imageId) {
        try {
            const file = await file_dao_1.fileDao.getFileById(imageId);
            logger_1.default.info(`Getting public image info for ${imageId}: found=${!!file}, isPublic=${file?.isPublic}`);
            if (!file || !file.isPublic) {
                throw new Error('Image not found or not public');
            }
            const publicUrl = await this.generatePublicUrl(imageId);
            return {
                id: file.id,
                originalName: file.originalName,
                publicUrl,
                cdnUrl: undefined, // Not using image service
                thumbnails: undefined, // Not using image service
                accessCount: 0, // Default to 0 if not available
                createdAt: file.uploadedAt
            };
        }
        catch (error) {
            logger_1.default.error('Error getting public image info:', error);
            throw error;
        }
    }
    /**
     * List images in the image bed
     */
    async listImageBedImages(userId, options) {
        try {
            const imageBedFolder = await this.findImageBedFolder(userId);
            if (!imageBedFolder) {
                return {
                    images: [],
                    total: 0,
                    page: options?.page || 1,
                    limit: options?.limit || 20
                };
            }
            const files = await file_dao_1.fileDao.getFilesByUserId(userId, imageBedFolder.id);
            const images = [];
            for (const file of files) {
                if (file.mimeType.startsWith('image/') && file.isPublic) {
                    try {
                        const publicUrl = await this.generatePublicUrl(file.id);
                        images.push({
                            id: file.id,
                            originalName: file.originalName,
                            publicUrl,
                            cdnUrl: undefined, // Not using image service
                            thumbnails: undefined, // Not using image service
                            accessCount: 0, // Default to 0 if not available
                            createdAt: file.uploadedAt
                        });
                    }
                    catch (error) {
                        logger_1.default.warn(`Failed to generate public URL for ${file.id}:`, error);
                    }
                }
            }
            return {
                images,
                total: images.length,
                page: options?.page || 1,
                limit: options?.limit || 20
            };
        }
        catch (error) {
            logger_1.default.error('Error listing image bed images:', error);
            throw error;
        }
    }
    /**
     * Remove image from image bed
     */
    async removeFromImageBed(imageId, userId, targetFolderId) {
        try {
            const file = await file_dao_1.fileDao.getFileById(imageId);
            if (!file || file.userId !== userId) {
                throw new Error('File not found or access denied');
            }
            // Move file to target folder or root
            await file_dao_1.fileDao.updateFile(imageId, {
                folderId: targetFolderId || undefined,
                modifiedAt: new Date()
            });
            // Make file private
            await file_dao_1.fileDao.updateFile(imageId, {
                isPublic: false,
                modifiedAt: new Date()
            });
            logger_1.default.info(`Image removed from image bed: ${imageId}`);
        }
        catch (error) {
            logger_1.default.error('Error removing image from image bed:', error);
            throw error;
        }
    }
    /**
     * Validate that a folder is a valid image bed folder
     */
    validateImageBedFolder(folder) {
        return (folder.name === this.IMAGE_BED_FOLDER_NAME &&
            !folder.parentId && // Must be in root directory
            !!folder.userId // Must have a valid user ID
        );
    }
    /**
     * Map folder to image bed folder with validation
     */
    mapFolderToImageBedFolder(folder) {
        if (!this.validateImageBedFolder(folder)) {
            logger_1.default.error(`Invalid image bed folder: ${folder.id}, name: ${folder.name}, parentId: ${folder.parentId}`);
            throw new Error('Invalid image bed folder: must be named "Image Bed" and located in root directory');
        }
        return {
            id: folder.id,
            name: folder.name,
            isImageBed: true,
            userId: folder.userId,
            createdAt: folder.createdAt,
            modifiedAt: folder.modifiedAt
        };
    }
    /**
     * Public method to clean up duplicate image bed folders
     */
    async forceCleanupImageBedFolders(userId) {
        await this.cleanupDuplicateImageBedFolders(userId);
    }
}
exports.imageBedService = new ImageBedService();
//# sourceMappingURL=image-bed.service.js.map