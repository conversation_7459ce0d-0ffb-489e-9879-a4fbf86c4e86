"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createImageBedRoutes = createImageBedRoutes;
const express_1 = require("express");
const multer_1 = __importDefault(require("multer"));
const image_bed_controller_1 = require("../controllers/image-bed.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
// Configure multer for image bed uploads
const upload = (0, multer_1.default)({
    storage: multer_1.default.memoryStorage(),
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB per file
        files: 10 // Max 10 files at once
    },
    fileFilter: (req, file, cb) => {
        // Only allow image files
        if (file.mimetype.startsWith('image/')) {
            cb(null, true);
        }
        else {
            cb(new Error('Only image files are allowed'));
        }
    }
});
function createImageBedRoutes() {
    const router = (0, express_1.Router)();
    // Get or create image bed folder
    router.get('/folder', auth_middleware_1.authMiddleware, image_bed_controller_1.imageBedController.getOrCreateImageBedFolder);
    // Upload image directly to image bed
    router.post('/upload', auth_middleware_1.authMiddleware, upload.single('file'), image_bed_controller_1.imageBedController.uploadToImageBed);
    // Move image to image bed
    router.post('/move/:fileId', auth_middleware_1.authMiddleware, image_bed_controller_1.imageBedController.moveImageToImageBed);
    // List images in image bed
    router.get('/images', auth_middleware_1.authMiddleware, image_bed_controller_1.imageBedController.listImageBedImages);
    // Remove image from image bed
    router.post('/remove/:imageId', auth_middleware_1.authMiddleware, image_bed_controller_1.imageBedController.removeFromImageBed);
    // Generate public link for an image
    router.post('/public-link/:imageId', auth_middleware_1.authMiddleware, image_bed_controller_1.imageBedController.generatePublicLink);
    // Get public image info (authenticated)
    router.get('/info/:imageId', auth_middleware_1.authMiddleware, image_bed_controller_1.imageBedController.getPublicImageInfo);
    // Get public image info (no authentication required)
    router.get('/public/:imageId/info', image_bed_controller_1.imageBedController.getPublicImageInfo);
    // Serve public image (no authentication required)
    router.get('/public/:imageId', image_bed_controller_1.imageBedController.servePublicImage);
    // Clean up duplicate image bed folders
    router.post('/cleanup', auth_middleware_1.authMiddleware, image_bed_controller_1.imageBedController.cleanupImageBedFolders);
    // Health check for image bed service
    router.get('/health/check', (req, res) => {
        res.json({
            service: 'image-bed-service',
            status: 'healthy',
            timestamp: new Date().toISOString()
        });
    });
    return router;
}
//# sourceMappingURL=image-bed.routes.js.map