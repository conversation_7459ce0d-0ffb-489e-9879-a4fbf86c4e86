import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { <PERSON>, Co<PERSON>, Check, Eye, EyeOff, ArrowLeft } from 'lucide-react'
import { useAuthStore } from '../stores'
import { useI18n } from '../contexts/I18nContext'
import { Button, Card, Input } from '@/components/ui'
import ThemeLanguageToggle from '@/components/ThemeLanguageToggle'

const TwoFactorSetup: React.FC = () => {
  const navigate = useNavigate()
  const { user, enableTwoFactor, verifyTwoFactor, disableTwoFactor, isLoading } = useAuthStore()
  const { t } = useI18n()

  const [step, setStep] = useState<'setup' | 'verify' | 'disable'>('setup')
  const [qrData, setQrData] = useState<{ secret: string; qrCode: string } | null>(null)
  const [verificationCode, setVerificationCode] = useState('')
  const [disablePassword, setDisablePassword] = useState('')
  const [showSecret, setShowSecret] = useState(false)
  const [copied, setCopied] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  useEffect(() => {
    if (!user) {
      navigate('/login')
      return
    }

    if (user.twoFactorEnabled) {
      setStep('disable')
    }
  }, [user, navigate])

  const handleEnableTwoFactor = async () => {
    try {
      setError('')
      const data = await enableTwoFactor()
      setQrData(data)
      setStep('verify')
    } catch (err) {
      setError('Failed to generate two-factor authentication setup. Please try again.')
      console.error('Enable 2FA error:', err)
    }
  }

  const handleVerifyCode = async (e: React.FormEvent) => {
    e.preventDefault()

    if (verificationCode.length !== 6) {
      setError('Please enter a 6-digit verification code')
      return
    }

    try {
      setError('')
      await verifyTwoFactor(verificationCode)
      setSuccess('Two-factor authentication has been enabled successfully!')
      setTimeout(() => {
        navigate('/dashboard')
      }, 2000)
    } catch (err) {
      setError('Invalid verification code. Please try again.')
      console.error('Verify 2FA error:', err)
    }
  }

  const handleDisableTwoFactor = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!disablePassword) {
      setError('Please enter your password to disable two-factor authentication')
      return
    }

    try {
      setError('')
      await disableTwoFactor(disablePassword)
      setSuccess('Two-factor authentication has been disabled.')
      setTimeout(() => {
        navigate('/dashboard')
      }, 2000)
    } catch (err) {
      setError('Invalid password. Please try again.')
      console.error('Disable 2FA error:', err)
    }
  }

  const copySecret = async () => {
    if (qrData?.secret) {
      try {
        await navigator.clipboard.writeText(qrData.secret)
        setCopied(true)
        setTimeout(() => setCopied(false), 2000)
      } catch (err) {
        console.error('Failed to copy secret:', err)
      }
    }
  }

  const renderSetupStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <Shield className="w-16 h-16 text-dracula-purple mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-dracula-foreground mb-2">
          Enable Two-Factor Authentication
        </h3>
        <p className="text-dracula-comment">
          Add an extra layer of security to your account by enabling two-factor authentication.
        </p>
      </div>

      <div className="bg-dracula-current-line/50 p-4 rounded-lg">
        <h4 className="font-medium text-dracula-foreground mb-2">What you'll need:</h4>
        <ul className="text-sm text-dracula-comment space-y-1">
          <li>• An authenticator app (Google Authenticator, Authy, etc.)</li>
          <li>• Your smartphone or tablet</li>
          <li>• A few minutes to complete the setup</li>
        </ul>
      </div>

      <Button
        onClick={handleEnableTwoFactor}
        className="w-full"
        loading={isLoading}
      >
        <Shield className="w-4 h-4 mr-2" />
        Set Up Two-Factor Authentication
      </Button>
    </div>
  )

  const renderVerifyStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold text-dracula-foreground mb-2">
          Scan QR Code
        </h3>
        <p className="text-dracula-comment">
          Scan this QR code with your authenticator app, then enter the 6-digit code below.
        </p>
      </div>

      {qrData && (
        <div className="space-y-4">
          <div className="flex justify-center">
            <div className="bg-white p-4 rounded-lg">
              <img
                src={qrData.qrCode}
                alt="QR Code for 2FA setup"
                className="w-48 h-48"
              />
            </div>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-dracula-foreground">
              Manual Entry Key (if you can't scan):
            </label>
            <div className="flex items-center space-x-2">
              <Input
                type={showSecret ? 'text' : 'password'}
                value={qrData.secret}
                readOnly
                className="font-mono text-sm"
              />
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setShowSecret(!showSecret)}
              >
                {showSecret ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={copySecret}
              >
                {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
              </Button>
            </div>
          </div>
        </div>
      )}

      <form onSubmit={handleVerifyCode} className="space-y-4">
        <Input
          id="verificationCode"
          name="verificationCode"
          type="text"
          required
          label="Verification Code"
          placeholder="Enter 6-digit code"
          value={verificationCode}
          onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
          maxLength={6}
          className="text-center text-lg tracking-widest"
        />

        <Button
          type="submit"
          className="w-full"
          loading={isLoading}
        >
          Verify and Enable
        </Button>
      </form>
    </div>
  )

  const renderDisableStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <Shield className="w-16 h-16 text-dracula-red mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-dracula-foreground mb-2">
          Disable Two-Factor Authentication
        </h3>
        <p className="text-dracula-comment">
          Enter your password to disable two-factor authentication for your account.
        </p>
      </div>

      <div className="bg-dracula-red/10 border border-dracula-red/20 p-4 rounded-lg">
        <p className="text-dracula-red text-sm">
          <strong>Warning:</strong> Disabling two-factor authentication will make your account less secure.
        </p>
      </div>

      <form onSubmit={handleDisableTwoFactor} className="space-y-4">
        <Input
          id="disablePassword"
          name="disablePassword"
          type="password"
          required
          label="Current Password"
          placeholder="Enter your password"
          value={disablePassword}
          onChange={(e) => setDisablePassword(e.target.value)}
        />

        <Button
          type="submit"
          variant="danger"
          className="w-full"
          loading={isLoading}
        >
          Disable Two-Factor Authentication
        </Button>
      </form>
    </div>
  )

  return (
    <div className="min-h-screen bg-dracula-bg flex items-center justify-center px-4">
      {/* 主题和语言切换按钮 */}
      <ThemeLanguageToggle className="absolute top-4 right-4" />
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h1 className="text-3xl md:text-4xl font-bold text-dracula-foreground mb-2">Cloud Storage</h1>
          <h2 className="text-xl md:text-2xl font-semibold text-dracula-foreground">Security Settings</h2>
        </div>

        <Card>
          {error && (
            <div className="mb-6 p-4 bg-dracula-red/10 border border-dracula-red/20 rounded-lg">
              <p className="text-dracula-red text-sm">{error}</p>
            </div>
          )}

          {success && (
            <div className="mb-6 p-4 bg-dracula-green/10 border border-dracula-green/20 rounded-lg">
              <p className="text-dracula-green text-sm">{success}</p>
            </div>
          )}

          {step === 'setup' && renderSetupStep()}
          {step === 'verify' && renderVerifyStep()}
          {step === 'disable' && renderDisableStep()}

          <div className="mt-6 pt-6 border-t border-dracula-comment/20">
            <button
              onClick={() => navigate('/dashboard')}
              className="inline-flex items-center space-x-2 text-dracula-purple hover:text-dracula-pink"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Dashboard</span>
            </button>
          </div>
        </Card>
      </div>
    </div>
  )
}

export default TwoFactorSetup