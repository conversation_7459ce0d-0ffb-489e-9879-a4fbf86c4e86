import React, { useState, useEffect } from 'react'
import { Link, useNavigate, useLocation } from 'react-router-dom'
import { Eye, EyeOff, LogIn, AlertCircle, CheckCircle } from 'lucide-react'
import { useAuthStore } from '../stores'
import { useI18n } from '../contexts/I18nContext'
import ThemeLanguageToggle from '../components/ThemeLanguageToggle'
import { validateField, getErrorMessage, extractFieldErrors, setTranslationFunction } from '../utils/validation'
import '../styles/auth.css'

const Login: React.FC = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const { login, isLoading, error: authError, clearError } = useAuthStore()
  const { t } = useI18n()

  // Get redirect URL from location state or default to dashboard
  const from = location.state?.from?.pathname || '/dashboard'

  const [showPassword, setShowPassword] = useState(false)
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false,
    twoFactorCode: '',
  })
  const [fieldErrors, setFieldErrors] = useState<Record<string, string[]>>({})
  const [touched, setTouched] = useState<Record<string, boolean>>({})
  const [isFormValid, setIsFormValid] = useState(false)
  const [requiresTwoFactor, setRequiresTwoFactor] = useState(false)
  const [loginSuccess, setLoginSuccess] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Clear auth error when component mounts or form data changes
  useEffect(() => {
    if (authError) {
      clearError()
    }
  }, [formData, clearError])

  // Validate form whenever form data changes or language changes
  useEffect(() => {
    // Set the translation function for validation
    setTranslationFunction(t)

    // Basic validation to enable the button - just check if all required fields have values
    const hasRequiredValues = formData.email.trim() !== '' && formData.password !== '';
    const hasTwoFactorIfRequired = !requiresTwoFactor || (requiresTwoFactor && formData.twoFactorCode.trim() !== '');
    
    setIsFormValid(hasRequiredValues && hasTwoFactorIfRequired);

    // Update field-specific errors
    const newFieldErrors: Record<string, string[]> = {}

    // Validate each field individually to get specific errors
    const emailValidation = validateField('email', formData.email, formData)
    const passwordValidation = validateField('password', formData.password, formData)

    if (emailValidation.errors.length > 0) newFieldErrors.email = emailValidation.errors
    if (passwordValidation.errors.length > 0) newFieldErrors.password = passwordValidation.errors

    // Only validate 2FA code if it's required
    if (requiresTwoFactor && formData.twoFactorCode) {
      const twoFactorValidation = validateField('twoFactorCode', formData.twoFactorCode, formData)
      if (twoFactorValidation.errors.length > 0) newFieldErrors.twoFactorCode = twoFactorValidation.errors
    }

    setFieldErrors(newFieldErrors)
  }, [formData, t, requiresTwoFactor])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (isSubmitting) return // Prevent double submission

    // Mark all fields as touched
    const fieldsToTouch: Record<string, boolean> = {
      email: true,
      password: true,
    }

    if (requiresTwoFactor) {
      fieldsToTouch.twoFactorCode = true
    }

    setTouched(fieldsToTouch)

    // Basic validation - just check if all required fields have values
    if (!formData.email.trim() || !formData.password) {
      // Focus on first empty field
      if (!formData.email.trim()) {
        document.getElementById('email')?.focus();
      } else if (!formData.password) {
        document.getElementById('password')?.focus();
      }
      return;
    }
    
    // Check 2FA code if required
    if (requiresTwoFactor && !formData.twoFactorCode.trim()) {
      document.getElementById('twoFactorCode')?.focus();
      return;
    }

    setIsSubmitting(true)

    // Save form data before login attempt
    const loginFormData = { ...formData }

    try {
      // Clear any previous errors
      clearError()

      await login(
        formData.email.trim(),
        formData.password,
        formData.rememberMe,
        requiresTwoFactor ? formData.twoFactorCode : undefined
      )

      // Login successful
      setLoginSuccess(true)

      // Track login success event
      try {
        // Analytics tracking could be added here
        console.log('Login successful event tracked')
      } catch (analyticsError) {
        console.error('Failed to track login event:', analyticsError)
      }

      // Redirect to the page user was trying to access or dashboard
      setTimeout(() => {
        navigate(from, { replace: true })
      }, 1000) // 1 second to show success message
    } catch (err: any) {
      console.error('Login error:', err)

      // Check if 2FA is required
      if (err.requiresTwoFactor) {
        setRequiresTwoFactor(true)
        setIsSubmitting(false)
        // 恢复表单数据
        setFormData(loginFormData);
        return
      }

      // Handle field-specific validation errors
      const apiFieldErrors = extractFieldErrors(err)
      if (Object.keys(apiFieldErrors).length > 0) {
        setFieldErrors(prev => ({
          ...prev,
          ...apiFieldErrors
        }))

        // Focus on first error field
        const firstErrorField = Object.keys(apiFieldErrors)[0]
        if (firstErrorField) {
          const element = document.getElementById(firstErrorField)
          element?.focus()
        }
      }

      // Error is also handled by AuthContext and displayed via authError
      // Scroll to top to show error message
      window.scrollTo({ top: 0, behavior: 'smooth' })
        
        // Restore form data on error
        setFormData(loginFormData);
      } finally {
        setIsSubmitting(false)
      }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }))
  }

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const { name } = e.target
    setTouched(prev => ({
      ...prev,
      [name]: true
    }))
  }

  const getFieldError = (fieldName: string): string | null => {
    if (!touched[fieldName] || !fieldErrors[fieldName]) return null
    return fieldErrors[fieldName][0] // Return first error
  }

  const isFieldValid = (fieldName: string): boolean => {
    if (!touched[fieldName]) return true
    return !fieldErrors[fieldName] || fieldErrors[fieldName].length === 0
  }

  return (
    <div className="min-h-screen bg-primary flex items-center justify-center px-4 relative">
      {/* Theme and language toggle */}
      <ThemeLanguageToggle className="absolute top-4 right-4" />

      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h1 className="text-3xl md:text-4xl font-bold text-primary mb-2">{t('app.title')}</h1>
          <h2 className="text-xl md:text-2xl font-semibold text-primary">{t('auth.loginSubtitle')}</h2>
        </div>

        <div className="bg-secondary p-6 rounded-lg shadow-lg border border-primary">
          <form className="space-y-6" onSubmit={handleSubmit}>
            {loginSuccess && (
              <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg flex items-start space-x-2">
                <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                <div className="flex-1">
                  <p className="text-green-700 dark:text-green-300 text-sm font-medium">{t('success.loginSuccess')}</p>
                  <p className="text-green-600 dark:text-green-400 text-sm mt-1">{t('auth.redirecting')}</p>
                </div>
              </div>
            )}

            {authError && !loginSuccess && (
              <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg flex items-start space-x-2">
                <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
                <div className="flex-1">
                  <p className="text-red-700 dark:text-red-300 text-sm font-medium">{t('auth.loginFailed')}</p>
                  <p className="text-red-600 dark:text-red-400 text-sm mt-1">{getErrorMessage(authError)}</p>
                  {authError && typeof authError === 'object' && 'code' in (authError as any) && (authError as any).code === 'NETWORK_ERROR' && (
                    <p className="text-red-600 dark:text-red-400 text-sm mt-1">
                      {t('error.checkConnection')}
                    </p>
                  )}
                </div>
              </div>
            )}

            <div className="space-y-5">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-primary mb-2">
                  {t('auth.email')}
                </label>
                <div className="relative">
                  <input
                    id="email"
                    name="email"
                    type="email"
                    required
                    placeholder={t('placeholder.enterEmail')}
                    value={formData.email}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    className={`auth-input pr-10 ${touched.email
                      ? isFieldValid('email')
                        ? 'border-green-500 focus:border-green-500'
                        : 'border-red-500 focus:border-red-500'
                      : ''
                      }`}
                  />
                  {touched.email && (
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                      {isFieldValid('email') ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <AlertCircle className="h-5 w-5 text-red-500" />
                      )}
                    </div>
                  )}
                </div>
                {getFieldError('email') && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{getFieldError('email')}</p>
                )}
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-primary mb-2">
                  {t('auth.password')}
                </label>
                <div className="relative">
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    required
                    placeholder={t('placeholder.enterPassword')}
                    value={formData.password}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    className={`auth-input pr-10 ${touched.password
                      ? isFieldValid('password')
                        ? 'border-green-500 focus:border-green-500'
                        : 'border-red-500 focus:border-red-500'
                      : ''
                      }`}
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center">
                    {touched.password && (
                      <div className="pr-2">
                        {isFieldValid('password') ? (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        ) : (
                          <AlertCircle className="h-5 w-5 text-red-500" />
                        )}
                      </div>
                    )}
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="pr-3 flex items-center text-comment hover:text-primary transition-colors"
                    >
                      {showPassword ? (
                        <EyeOff className="h-5 w-5" />
                      ) : (
                        <Eye className="h-5 w-5" />
                      )}
                    </button>
                  </div>
                </div>
                {getFieldError('password') && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{getFieldError('password')}</p>
                )}
              </div>

              {requiresTwoFactor && (
                <div>
                  <label htmlFor="twoFactorCode" className="block text-sm font-medium text-primary mb-2">
                    {t('auth.twoFactorCode')}
                  </label>
                  <div className="relative">
                    <input
                      id="twoFactorCode"
                      name="twoFactorCode"
                      type="text"
                      required
                      placeholder={t('placeholder.enterTwoFactorCode')}
                      value={formData.twoFactorCode}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      className={`auth-input pr-10 ${touched.twoFactorCode
                        ? isFieldValid('twoFactorCode')
                          ? 'border-green-500 focus:border-green-500'
                          : 'border-red-500 focus:border-red-500'
                        : ''
                        }`}
                    />
                    {touched.twoFactorCode && (
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                        {isFieldValid('twoFactorCode') ? (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        ) : (
                          <AlertCircle className="h-5 w-5 text-red-500" />
                        )}
                      </div>
                    )}
                  </div>
                  {getFieldError('twoFactorCode') && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{getFieldError('twoFactorCode')}</p>
                  )}
                  <p className="mt-2 text-sm text-comment">
                    {t('auth.twoFactorHelp')}
                  </p>
                </div>
              )}

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <input
                    id="rememberMe"
                    name="rememberMe"
                    type="checkbox"
                    checked={formData.rememberMe}
                    onChange={handleChange}
                    className="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                  />
                  <label htmlFor="rememberMe" className="ml-2 block text-sm text-primary">
                    {t('auth.rememberMe')}
                  </label>
                </div>

                <div className="text-sm">
                  <Link to="/password-reset" className="auth-link">
                    {t('auth.forgotPassword')}
                  </Link>
                </div>
              </div>
            </div>

            <button
              type="submit"
              disabled={isLoading || isSubmitting || loginSuccess}
              className="auth-button"
            >
              {isLoading || isSubmitting ? (
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
              ) : loginSuccess ? (
                <CheckCircle className="w-5 h-5 mr-2" />
              ) : (
                <LogIn className="w-5 h-5 mr-2" />
              )}
              <span>
                {isLoading || isSubmitting
                  ? t('auth.signingIn')
                  : loginSuccess
                    ? t('success.loginSuccess')
                    : requiresTwoFactor
                      ? t('auth.verifyAndLogin')
                      : t('auth.signIn')
                }
              </span>
            </button>

            <div className="text-center">
              <span className="text-comment">{t('auth.dontHaveAccount')} </span>
              <Link to="/register" className="auth-link">
                {t('auth.createAccount')}
              </Link>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export default Login