import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import { vi } from 'vitest'
import ShareManagement from '../pages/ShareManagement'
import { I18nProvider } from '../contexts/I18nContext'
import { ThemeProvider } from '../contexts/ThemeContext'
import { fileService } from '../services/fileService'

// Mock the file service
vi.mock('../services/fileService', () => ({
  fileService: {
    getUserShares: vi.fn(),
    getComprehensiveShareStats: vi.fn(),
    revokeShareLink: vi.fn()
  }
}))

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn().mockResolvedValue(undefined)
  }
})

const renderShareManagement = () => {
  return render(
    <BrowserRouter>
      <I18nProvider>
        <ThemeProvider>
          <ShareManagement />
        </ThemeProvider>
      </I18nProvider>
    </BrowserRouter>
  )
}

describe('ShareManagement', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders share management page', async () => {
    // Mock API responses
    vi.mocked(fileService.getUserShares).mockResolvedValue([])
    vi.mocked(fileService.getComprehensiveShareStats).mockResolvedValue({
      totalShares: 0,
      activeShares: 0,
      totalDownloads: 0,
      recentShares: [],
      downloadsByDay: []
    })

    renderShareManagement()

    expect(screen.getByText('分享管理')).toBeInTheDocument()
    expect(screen.getByText('管理您的文件分享链接')).toBeInTheDocument()
  })

  it('displays share statistics', async () => {
    // Mock API responses
    vi.mocked(fileService.getUserShares).mockResolvedValue([])
    vi.mocked(fileService.getComprehensiveShareStats).mockResolvedValue({
      totalShares: 5,
      activeShares: 3,
      totalDownloads: 25,
      recentShares: [],
      downloadsByDay: []
    })

    renderShareManagement()

    await waitFor(() => {
      expect(screen.getByText('5')).toBeInTheDocument() // Total shares
      expect(screen.getByText('3')).toBeInTheDocument() // Active shares
      expect(screen.getByText('25')).toBeInTheDocument() // Total downloads
    })
  })

  it('displays user shares', async () => {
    const mockShares = [
      {
        id: '1',
        fileId: 'file1',
        fileName: 'test-document.pdf',
        fileSize: 1024000,
        fileMimeType: 'application/pdf',
        token: 'abc123',
        permissions: [{ type: 'read' as const, granted: true }],
        downloadCount: 5,
        maxDownloads: 10,
        createdAt: new Date('2024-01-15'),
        expiresAt: new Date('2024-02-15')
      }
    ]

    vi.mocked(fileService.getUserShares).mockResolvedValue(mockShares)
    vi.mocked(fileService.getComprehensiveShareStats).mockResolvedValue({
      totalShares: 1,
      activeShares: 1,
      totalDownloads: 5,
      recentShares: [],
      downloadsByDay: []
    })

    renderShareManagement()

    await waitFor(() => {
      expect(screen.getByText('test-document.pdf')).toBeInTheDocument()
      expect(screen.getByText('下载 5 次')).toBeInTheDocument()
      expect(screen.getByText('限制 10 次')).toBeInTheDocument()
    })
  })

  it('copies share link to clipboard', async () => {
    const mockShares = [
      {
        id: '1',
        fileId: 'file1',
        fileName: 'test-document.pdf',
        fileSize: 1024000,
        fileMimeType: 'application/pdf',
        token: 'abc123',
        permissions: [{ type: 'read' as const, granted: true }],
        downloadCount: 5,
        createdAt: new Date('2024-01-15')
      }
    ]

    vi.mocked(fileService.getUserShares).mockResolvedValue(mockShares)
    vi.mocked(fileService.getComprehensiveShareStats).mockResolvedValue({
      totalShares: 1,
      activeShares: 1,
      totalDownloads: 5,
      recentShares: [],
      downloadsByDay: []
    })

    renderShareManagement()

    await waitFor(() => {
      const copyButton = screen.getByText('复制链接')
      fireEvent.click(copyButton)
    })

    expect(navigator.clipboard.writeText).toHaveBeenCalledWith(
      `${window.location.origin}/share/abc123`
    )
  })

  it('revokes share link', async () => {
    const mockShares = [
      {
        id: '1',
        fileId: 'file1',
        fileName: 'test-document.pdf',
        fileSize: 1024000,
        fileMimeType: 'application/pdf',
        token: 'abc123',
        permissions: [{ type: 'read' as const, granted: true }],
        downloadCount: 5,
        createdAt: new Date('2024-01-15')
      }
    ]

    vi.mocked(fileService.getUserShares).mockResolvedValue(mockShares)
    vi.mocked(fileService.getComprehensiveShareStats).mockResolvedValue({
      totalShares: 1,
      activeShares: 1,
      totalDownloads: 5,
      recentShares: [],
      downloadsByDay: []
    })

    // Mock window.confirm
    vi.stubGlobal('confirm', vi.fn().mockReturnValue(true))

    renderShareManagement()

    await waitFor(() => {
      const revokeButtons = screen.getAllByTitle('撤销分享')
      fireEvent.click(revokeButtons[0])
    })

    expect(fileService.revokeShareLink).toHaveBeenCalledWith('1')
  })

  it('shows empty state when no shares exist', async () => {
    vi.mocked(fileService.getUserShares).mockResolvedValue([])
    vi.mocked(fileService.getComprehensiveShareStats).mockResolvedValue({
      totalShares: 0,
      activeShares: 0,
      totalDownloads: 0,
      recentShares: [],
      downloadsByDay: []
    })

    renderShareManagement()

    await waitFor(() => {
      expect(screen.getByText('暂无分享文件')).toBeInTheDocument()
    })
  })

  it('handles loading state', () => {
    vi.mocked(fileService.getUserShares).mockImplementation(
      () => new Promise(() => {}) // Never resolves
    )
    vi.mocked(fileService.getComprehensiveShareStats).mockImplementation(
      () => new Promise(() => {}) // Never resolves
    )

    renderShareManagement()

    expect(screen.getByRole('generic', { name: /loading/i })).toBeInTheDocument()
  })
})