import Joi from 'joi';
import { ImageFormat } from '../models/image.model';

// Image upload validation schema
export const imageUploadSchema = Joi.object({
  folderId: Joi.alternatives().try(
    Joi.string().uuid(),
    Joi.string().regex(/^[0-9a-fA-F]{24}$/) // MongoDB ObjectId format
  ).optional(),
  isPublic: Joi.boolean().optional().default(false),
  tags: Joi.string().optional().allow(''),
  quality: Joi.number().integer().min(1).max(100).optional(),
  format: Joi.string().valid(...Object.values(ImageFormat)).optional(),
  width: Joi.number().integer().min(1).max(4000).optional(),
  height: Joi.number().integer().min(1).max(4000).optional(),
  fit: Joi.string().valid('cover', 'contain', 'fill', 'inside', 'outside').optional().default('cover'),
  generateThumbnails: Joi.string().valid('true', 'false').optional().default('true')
});

// Image conversion validation schema
export const imageConvertSchema = Joi.object({
  format: Joi.string().valid(...Object.values(ImageFormat)).required()
});

// Image list query validation schema
export const imageListQuerySchema = Joi.object({
  page: Joi.number().integer().min(1).optional().default(1),
  limit: Joi.number().integer().min(1).max(100).optional().default(20),
  isPublic: Joi.string().valid('true', 'false').optional()
});

// File validation for multer
export const validateImageFile = (file: Express.Multer.File): string | null => {
  // Check file size (max 10MB)
  const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
  if (file.size > MAX_FILE_SIZE) {
    return 'File size exceeds 10MB limit';
  }

  // Check MIME type
  const allowedMimeTypes = [
    'image/jpeg',
    'image/png',
    'image/webp',
    'image/gif',
    'image/bmp',
    'image/tiff'
  ];

  if (!allowedMimeTypes.includes(file.mimetype)) {
    return `Unsupported file type: ${file.mimetype}. Allowed types: ${allowedMimeTypes.join(', ')}`;
  }

  // Check file extension
  const allowedExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif', '.bmp', '.tiff', '.tif'];
  const fileExtension = file.originalname.toLowerCase().substring(file.originalname.lastIndexOf('.'));
  
  if (!allowedExtensions.includes(fileExtension)) {
    return `Unsupported file extension: ${fileExtension}. Allowed extensions: ${allowedExtensions.join(', ')}`;
  }

  return null; // No validation errors
};

// Validate multiple image files
export const validateImageFiles = (files: Express.Multer.File[]): string[] => {
  const errors: string[] = [];
  
  // Check total number of files
  const MAX_FILES = 20;
  if (files.length > MAX_FILES) {
    errors.push(`Too many files. Maximum ${MAX_FILES} files allowed per upload.`);
    return errors;
  }

  // Validate each file
  files.forEach((file, index) => {
    const error = validateImageFile(file);
    if (error) {
      errors.push(`File ${index + 1} (${file.originalname}): ${error}`);
    }
  });

  // Check total size
  const totalSize = files.reduce((sum, file) => sum + file.size, 0);
  const MAX_TOTAL_SIZE = 50 * 1024 * 1024; // 50MB total
  if (totalSize > MAX_TOTAL_SIZE) {
    errors.push(`Total file size exceeds 50MB limit. Current total: ${(totalSize / 1024 / 1024).toFixed(2)}MB`);
  }

  return errors;
};

// Middleware for validating image upload request body
export const validateImageUpload = (req: any, res: any, next: any) => {
  const { error } = imageUploadSchema.validate(req.body);
  
  if (error) {
    return res.status(400).json({
      error: 'Validation error',
      details: error.details.map(detail => detail.message)
    });
  }

  next();
};

// Middleware for validating image conversion request body
export const validateImageConvert = (req: any, res: any, next: any) => {
  const { error } = imageConvertSchema.validate(req.body);
  
  if (error) {
    return res.status(400).json({
      error: 'Validation error',
      details: error.details.map(detail => detail.message)
    });
  }

  next();
};

// Middleware for validating image list query parameters
export const validateImageListQuery = (req: any, res: any, next: any) => {
  const { error, value } = imageListQuerySchema.validate(req.query);
  
  if (error) {
    return res.status(400).json({
      error: 'Validation error',
      details: error.details.map(detail => detail.message)
    });
  }

  // Set validated values back to req.query
  req.query = value;
  next();
};