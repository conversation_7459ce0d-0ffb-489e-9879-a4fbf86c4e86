{"version": "0.34.6", "results": [[":src/__tests__/App.test.tsx", {"duration": 0, "failed": true}], [":src/__tests__/ui-framework.test.tsx", {"duration": 415, "failed": true}], [":src/__tests__/basic-ui.test.tsx", {"duration": 214, "failed": false}], [":src/__tests__/auth-ux.test.tsx", {"duration": 0, "failed": true}], [":src/__tests__/auth-simple.test.tsx", {"duration": 0, "failed": true}], [":src/__tests__/auth-integration.test.tsx", {"duration": 0, "failed": true}], [":src/__tests__/file-management.test.tsx", {"duration": 0, "failed": true}], [":src/__tests__/sync-status.test.tsx", {"duration": 17721, "failed": true}], [":src/__tests__/sync-status-display.test.tsx", {"duration": 435, "failed": true}], [":src/__tests__/mobile-interface.test.tsx", {"duration": 0, "failed": true}], [":src/__tests__/MobileFeatures.test.tsx", {"duration": 17429, "failed": true}], [":src/__tests__/registration-integration.test.tsx", {"duration": 0, "failed": true}], [":src/__tests__/register-simple.test.tsx", {"duration": 0, "failed": true}], [":src/__tests__/file-upload-integration.test.tsx", {"duration": 5689, "failed": true}], [":src/__tests__/mobile-gestures.test.tsx", {"duration": 392, "failed": true}], [":src/__tests__/mobile-compatibility.test.tsx", {"duration": 0, "failed": true}], [":src/__tests__/registration.test.ts", {"duration": 69, "failed": true}], [":src/__tests__/validation-i18n.test.ts", {"duration": 13, "failed": false}], [":src/__tests__/MobileOptimizations.test.tsx", {"duration": 240, "failed": true}], [":src/__tests__/registration-redirect.test.ts", {"duration": 0, "failed": true}], [":src/__tests__/input-components.test.tsx", {"duration": 233, "failed": false}], [":src/__tests__/state-management-integration.test.tsx", {"duration": 200, "failed": false}]]}