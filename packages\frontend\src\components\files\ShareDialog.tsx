import React, { useState, useEffect } from 'react'
import { 
  XMarkIcon, 
  ClipboardDocumentIcon, 
  EyeIcon, 
  PencilIcon, 
  TrashIcon,
  CalendarIcon,
  LockClosedIcon,
  ChartBarIcon,
  ShareIcon
} from '@heroicons/react/24/outline'
import { useI18n } from '../../contexts/I18nContext'
import { showConfirmDialog, showNotification } from '../../contexts/ToastContext'
import { fileService } from '../../services/fileService'
import Button from '../ui/Button'
import Input from '../ui/Input'
import Modal from '../ui/Modal'
import { ShareLink, Permission } from '@cloud-storage/shared'

interface ShareDialogProps {
  isOpen: boolean
  onClose: () => void
  fileId: string
  fileName: string
}

interface ShareFormData {
  permissions: Permission[]
  expiresAt?: Date
  password?: string
  maxDownloads?: number
}

const ShareDialog: React.FC<ShareDialogProps> = ({
  isOpen,
  onClose,
  fileId,
  fileName
}) => {
  const { t } = useI18n()
  const [shares, setShares] = useState<ShareLink[]>([])
  const [loading, setLoading] = useState(false)
  const [creating, setCreating] = useState(false)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editingShare, setEditingShare] = useState<ShareLink | null>(null)
  const [formData, setFormData] = useState<ShareFormData>({
    permissions: [{ type: 'read', granted: true }]
  })
  const [error, setError] = useState<string>('')
  const [success, setSuccess] = useState<string>('')

  // Load existing shares when dialog opens
  useEffect(() => {
    if (isOpen && fileId) {
      loadShares()
    }
  }, [isOpen, fileId])

  const loadShares = async () => {
    try {
      setLoading(true)
      const shareLinks = await fileService.getFileShares(fileId)
      setShares(shareLinks)
    } catch (error) {
      console.error('Failed to load shares:', error)
      setError(t('share.loadError') || 'Failed to load share links')
    } finally {
      setLoading(false)
    }
  }

  const createShareLink = async () => {
    try {
      setCreating(true)
      setError('')

      const shareLink = await fileService.shareFile(fileId, formData)

      // Refresh shares list
      await loadShares()

      // Reset form
      setFormData({
        permissions: [{ type: 'read', granted: true }]
      })
      setShowCreateForm(false)
      setSuccess(t('share.created') || 'Share link created successfully')

      // Auto-hide success message
      setTimeout(() => setSuccess(''), 3000)
    } catch (error) {
      console.error('Failed to create share:', error)
      setError(t('share.createError') || 'Failed to create share link')
    } finally {
      setCreating(false)
    }
  }

  const updateShareLink = async () => {
    if (!editingShare) return

    try {
      setCreating(true)
      setError('')

      // Prepare update data, handling special fields
      const updateData = { ...formData }

      // Handle password field
      if (updateData.password === '••••••••') {
        // User didn't change the password, don't include it in update
        delete updateData.password
      } else if (updateData.password === null || updateData.password === '') {
        // User wants to remove password protection
        updateData.password = null
      }

      // Handle maxDownloads field
      if (updateData.maxDownloads === null) {
        // User wants to remove download limit
        updateData.maxDownloads = null
      }

      await fileService.updateShareLink(editingShare.id, updateData)

      // Refresh shares list
      await loadShares()

      // Reset form
      setFormData({
        permissions: [{ type: 'read', granted: true }]
      })
      setEditingShare(null)
      setShowCreateForm(false)
      setSuccess(t('share.updated') || 'Share link updated successfully')
      setTimeout(() => setSuccess(''), 3000)
    } catch (error) {
      console.error('Failed to update share link:', error)
      setError(t('share.updateError') || 'Failed to update share link')
    } finally {
      setCreating(false)
    }
  }

  const startEditShare = (share: ShareLink) => {
    setEditingShare(share)
    setFormData({
      permissions: share.permissions,
      expiresAt: share.expiresAt ? new Date(share.expiresAt) : undefined,
      password: share.password ? '••••••••' : undefined,
      maxDownloads: share.maxDownloads || undefined
    })
    setShowCreateForm(true)
  }

  const cancelEdit = () => {
    setEditingShare(null)
    setShowCreateForm(false)
    setFormData({
      permissions: [{ type: 'read', granted: true }]
    })
  }

  const copyToClipboard = async (token: string) => {
    try {
      const shareUrl = `${window.location.origin}/share/${token}`
      await navigator.clipboard.writeText(shareUrl)
      setSuccess(t('share.copied') || 'Share link copied to clipboard')
      setTimeout(() => setSuccess(''), 3000)
    } catch (error) {
      setError(t('share.copyError') || 'Failed to copy link')
    }
  }

  const revokeShare = async (shareId: string) => {
    const confirmed = await showConfirmDialog({
      title: t('share.revokeConfirm'),
      message: t('share.revokeConfirmMessage') || 'Are you sure you want to revoke this share link? This action cannot be undone.',
      confirmText: t('share.revoke'),
      cancelText: t('common.cancel'),
      type: 'warning',
      onConfirm: () => {},
      onCancel: () => {}
    })

    if (!confirmed) return

    try {
      await fileService.revokeShareLink(shareId)
      await loadShares()
      showNotification('success', t('share.revoked') || 'Share link revoked successfully')
    } catch (error: any) {
      console.error('Failed to revoke share:', error)
      setError(t('share.revokeError') || 'Failed to revoke share link')
    }
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date))
  }

  const getPermissionIcon = (permissions: Permission[]) => {
    const hasWrite = permissions.some(p => p.type === 'write' && p.granted)
    const hasDelete = permissions.some(p => p.type === 'delete' && p.granted)
    
    if (hasDelete) return <TrashIcon className="w-4 h-4 text-red-500" />
    if (hasWrite) return <PencilIcon className="w-4 h-4 text-yellow-500" />
    return <EyeIcon className="w-4 h-4 text-green-500" />
  }

  const getPermissionText = (permissions: Permission[]) => {
    const hasWrite = permissions.some(p => p.type === 'write' && p.granted)
    const hasDelete = permissions.some(p => p.type === 'delete' && p.granted)
    
    if (hasDelete) return t('share.permissions.full') || 'Full Access'
    if (hasWrite) return t('share.permissions.edit') || 'Can Edit'
    return t('share.permissions.view') || 'View Only'
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={t('share.title') || 'Share File'}>
      <div className="space-y-6">
        {/* File Info */}
        <div className="flex items-center space-x-3 p-3 bg-secondary rounded-lg">
          <ShareIcon className="w-5 h-5 text-purple-500" />
          <div>
            <p className="font-medium text-primary">{fileName}</p>
            <p className="text-sm text-comment">{t('share.fileSharing') || 'File Sharing'}</p>
          </div>
        </div>

        {/* Error/Success Messages */}
        {error && (
          <div className="p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
            <p className="text-red-400 text-sm">{error}</p>
          </div>
        )}
        
        {success && (
          <div className="p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
            <p className="text-green-400 text-sm">{success}</p>
          </div>
        )}

        {/* Create New Share Button or Edit Existing */}
        {!showCreateForm && (
          shares.length === 0 ? (
            <Button
              onClick={() => setShowCreateForm(true)}
              className="w-full"
              variant="primary"
            >
              <ShareIcon className="w-4 h-4 mr-2" />
              {t('share.createNew') || 'Create New Share Link'}
            </Button>
          ) : (
            <Button
              onClick={() => startEditShare(shares[0])}
              className="w-full"
              variant="secondary"
            >
              <PencilIcon className="w-4 h-4 mr-2" />
              {t('share.editExisting') || 'Edit Share Link'}
            </Button>
          )
        )}

        {/* Create/Edit Share Form */}
        {showCreateForm && (
          <div className="space-y-4 p-4 bg-secondary rounded-lg">
            <h3 className="font-medium text-primary">
              {editingShare
                ? (t('share.editTitle') || 'Edit Share Link')
                : (t('share.createTitle') || 'Create Share Link')
              }
            </h3>

            {/* Permissions */}
            <div>
              <label className="block text-sm font-medium text-primary mb-2">
                {t('share.permissions.title') || 'Permissions'}
              </label>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="permission"
                    checked={formData.permissions.length === 1 && formData.permissions[0].type === 'read'}
                    onChange={() => setFormData({
                      ...formData,
                      permissions: [{ type: 'read', granted: true }]
                    })}
                    className="mr-2"
                  />
                  <EyeIcon className="w-4 h-4 mr-2 text-green-500" />
                  {t('share.permissions.view') || 'View Only'}
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="permission"
                    checked={formData.permissions.some(p => p.type === 'write' && p.granted)}
                    onChange={() => setFormData({
                      ...formData,
                      permissions: [
                        { type: 'read', granted: true },
                        { type: 'write', granted: true }
                      ]
                    })}
                    className="mr-2"
                  />
                  <PencilIcon className="w-4 h-4 mr-2 text-yellow-500" />
                  {t('share.permissions.edit') || 'Can Edit'}
                </label>
              </div>
            </div>

            {/* Expiration Date */}
            <div>
              <label className="block text-sm font-medium text-primary mb-2">
                {t('share.expiration') || 'Expiration (Optional)'}
              </label>
              <Input
                type="datetime-local"
                value={formData.expiresAt ? formData.expiresAt.toISOString().slice(0, 16) : ''}
                onChange={(e) => setFormData({
                  ...formData,
                  expiresAt: e.target.value ? new Date(e.target.value) : undefined
                })}
                icon={<CalendarIcon className="w-4 h-4" />}
              />
            </div>

            {/* Password Protection */}
            <div>
              <label className="block text-sm font-medium text-primary mb-2">
                {t('share.password') || 'Password Protection (Optional)'}
              </label>
              <Input
                type="password"
                placeholder={t('share.passwordPlaceholder') || 'Enter password'}
                value={formData.password || ''}
                onChange={(e) => setFormData({
                  ...formData,
                  password: e.target.value.trim() === '' ? null : e.target.value
                })}
                icon={<LockClosedIcon className="w-4 h-4" />}
              />
              {editingShare && formData.password === '••••••••' && (
                <p className="text-xs text-comment mt-1">
                  {t('share.passwordHint') || 'Clear the field to remove password protection, or enter a new password to change it'}
                </p>
              )}
            </div>

            {/* Download Limit */}
            <div>
              <label className="block text-sm font-medium text-primary mb-2">
                {t('share.downloadLimit') || 'Download Limit (Optional)'}
              </label>
              <Input
                type="number"
                min="1"
                placeholder={t('share.downloadLimitPlaceholder') || 'Max downloads'}
                value={formData.maxDownloads || ''}
                onChange={(e) => setFormData({
                  ...formData,
                  maxDownloads: e.target.value.trim() === '' ? null : parseInt(e.target.value) || null
                })}
              />
              <p className="text-xs text-comment mt-1">
                {t('share.downloadLimitHint') || 'Leave empty for unlimited downloads'}
              </p>
            </div>

            {/* Form Actions */}
            <div className="flex space-x-3">
              <Button
                onClick={editingShare ? updateShareLink : createShareLink}
                loading={creating}
                variant="primary"
                className="flex-1"
              >
                {editingShare
                  ? (t('share.update') || 'Update Share Link')
                  : (t('share.create') || 'Create Share Link')
                }
              </Button>
              <Button
                onClick={editingShare ? cancelEdit : () => setShowCreateForm(false)}
                variant="secondary"
              >
                {t('common.cancel') || 'Cancel'}
              </Button>
            </div>
          </div>
        )}

        {/* Existing Shares */}
        <div>
          <h3 className="font-medium text-primary mb-3">
            {t('share.existing') || 'Existing Share Links'}
          </h3>
          
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto"></div>
              <p className="text-comment mt-2">{t('common.loading') || 'Loading...'}</p>
            </div>
          ) : shares.length === 0 ? (
            <div className="text-center py-8 text-comment">
              <ShareIcon className="w-12 h-12 mx-auto mb-3 opacity-50" />
              <p>{t('share.noShares') || 'No share links created yet'}</p>
            </div>
          ) : (
            <div className="space-y-3">
              {shares.map((share) => (
                <div key={share.id} className="p-4 bg-secondary rounded-lg">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        {getPermissionIcon(share.permissions)}
                        <span className="text-sm font-medium text-primary">
                          {getPermissionText(share.permissions)}
                        </span>
                        {share.password && (
                          <LockClosedIcon className="w-4 h-4 text-yellow-500" title="Password Protected" />
                        )}
                      </div>
                      
                      <div className="text-xs text-comment space-y-1">
                        <p>
                          {t('share.created') || 'Created'}: {formatDate(share.createdAt)}
                        </p>
                        {share.expiresAt && (
                          <p>
                            {t('share.expires') || 'Expires'}: {formatDate(share.expiresAt)}
                          </p>
                        )}
                        <div className="flex items-center space-x-4">
                          <span className="flex items-center">
                            <ChartBarIcon className="w-3 h-3 mr-1" />
                            {share.downloadCount} {t('share.downloads') || 'downloads'}
                          </span>
                          {share.maxDownloads && (
                            <span>/ {share.maxDownloads} max</span>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex space-x-2">
                      <Button
                        onClick={() => copyToClipboard(share.token)}
                        variant="secondary"
                        size="sm"
                        title={t('share.copy') || 'Copy Link'}
                      >
                        <ClipboardDocumentIcon className="w-4 h-4" />
                      </Button>
                      <Button
                        onClick={() => revokeShare(share.id)}
                        variant="danger"
                        size="sm"
                        title={t('share.revoke') || 'Revoke'}
                      >
                        <XMarkIcon className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </Modal>
  )
}

export default ShareDialog