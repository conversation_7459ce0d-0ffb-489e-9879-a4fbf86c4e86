import { apiService } from './api';
import { FileUpload, FileInfo, FileMetadata, Pagination, FileList } from '@cloud-storage/shared';

export interface ChunkedUploadInit {
  filename: string;
  fileSize: number;
  mimeType: string;
  totalChunks: number;
  folderId?: string;
  tags?: string[];
}

export interface UploadProgress {
  uploadId: string;
  filename: string;
  totalChunks: number;
  uploadedChunks: number;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'failed' | 'paused';
  createdAt: Date;
  updatedAt: Date;
}

export interface ChunkUploadResult {
  chunkIndex: number;
  uploaded: number;
  total: number;
  progress: number;
}

// File type validation configuration
export const ALLOWED_FILE_TYPES = {
  'image/jpeg': ['.jpg', '.jpeg'],
  'image/png': ['.png'],
  'image/gif': ['.gif'],
  'image/webp': ['.webp'],
  'text/plain': ['.txt'],
  'application/pdf': ['.pdf'],
  'application/msword': ['.doc'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
  'application/vnd.ms-excel': ['.xls'],
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
  'application/zip': ['.zip'],
  'application/x-rar-compressed': ['.rar'],
  'video/mp4': ['.mp4'],
  'video/avi': ['.avi'],
  'video/quicktime': ['.mov'],
  'audio/mpeg': ['.mp3'],
  'audio/wav': ['.wav']
};

export const MAX_FILE_SIZE = 500 * 1024 * 1024; // 500MB
export const CHUNK_SIZE = 5 * 1024 * 1024; // 5MB chunks

export class FileService {
  private static instance: FileService;
  private activeUploads = new Map<string, AbortController>();

  public static getInstance(): FileService {
    if (!FileService.instance) {
      FileService.instance = new FileService();
    }
    return FileService.instance;
  }

  // Validate file type and size
  validateFile(file: File): { valid: boolean; error?: string } {
    // Check file type
    if (!Object.keys(ALLOWED_FILE_TYPES).includes(file.type)) {
      return {
        valid: false,
        error: `File type ${file.type} is not allowed. Allowed types: ${Object.keys(ALLOWED_FILE_TYPES).join(', ')}`
      };
    }

    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      return {
        valid: false,
        error: `File size ${this.formatFileSize(file.size)} exceeds maximum allowed size of ${this.formatFileSize(MAX_FILE_SIZE)}`
      };
    }

    return { valid: true };
  }

  // Upload single file (small files)
  async uploadFile(
    file: File,
    folderId?: string,
    tags?: string[],
    onProgress?: (progress: number) => void
  ): Promise<FileInfo> {
    const validation = this.validateFile(file);
    if (!validation.valid) {
      throw new Error(validation.error);
    }

    const formData = new FormData();
    formData.append('file', file);
    if (folderId) formData.append('folderId', folderId);
    if (tags) formData.append('tags', JSON.stringify(tags));

    const abortController = new AbortController();
    const uploadId = `single-${Date.now()}`;
    this.activeUploads.set(uploadId, abortController);

    try {
      const response = await fetch(`${this.getApiBaseUrl()}/files/upload`, {
        method: 'POST',
        body: formData,
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        signal: abortController.signal
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Upload failed: ${response.statusText}`);
      }

      const result = await response.json();
      return result.data;
    } finally {
      this.activeUploads.delete(uploadId);
    }
  }

  // Upload multiple files
  async uploadFiles(
    files: File[],
    folderId?: string,
    tags?: string[],
    onProgress?: (fileIndex: number, progress: number) => void
  ): Promise<FileInfo[]> {
    // Validate all files first
    for (const file of files) {
      const validation = this.validateFile(file);
      if (!validation.valid) {
        throw new Error(`${file.name}: ${validation.error}`);
      }
    }

    const formData = new FormData();
    files.forEach(file => formData.append('files', file));
    if (folderId) formData.append('folderId', folderId);
    if (tags) formData.append('tags', JSON.stringify(tags));

    const abortController = new AbortController();
    const uploadId = `batch-${Date.now()}`;
    this.activeUploads.set(uploadId, abortController);

    try {
      const response = await fetch(`${this.getApiBaseUrl()}/files/upload/batch`, {
        method: 'POST',
        body: formData,
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        signal: abortController.signal
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Batch upload failed: ${response.statusText}`);
      }

      const result = await response.json();
      return result.data;
    } finally {
      this.activeUploads.delete(uploadId);
    }
  }

  // Initialize chunked upload for large files
  async initChunkedUpload(
    file: File,
    folderId?: string,
    tags?: string[]
  ): Promise<string> {
    const validation = this.validateFile(file);
    if (!validation.valid) {
      throw new Error(validation.error);
    }

    const totalChunks = Math.ceil(file.size / CHUNK_SIZE);
    
    const initData: ChunkedUploadInit = {
      filename: file.name,
      fileSize: file.size,
      mimeType: file.type,
      totalChunks,
      folderId,
      tags
    };

    const result = await apiService.post<{ uploadId: string }>('/files/upload/chunked/init', initData);
    return result.uploadId;
  }

  // Upload file chunk
  async uploadChunk(
    uploadId: string,
    file: File,
    chunkIndex: number,
    onProgress?: (progress: number) => void
  ): Promise<ChunkUploadResult> {
    const start = chunkIndex * CHUNK_SIZE;
    const end = Math.min(start + CHUNK_SIZE, file.size);
    const chunk = file.slice(start, end);

    const formData = new FormData();
    formData.append('chunk', chunk);
    formData.append('chunkIndex', chunkIndex.toString());

    const abortController = new AbortController();
    this.activeUploads.set(`${uploadId}-chunk-${chunkIndex}`, abortController);

    try {
      const response = await fetch(`${this.getApiBaseUrl()}/files/upload/chunked/${uploadId}/chunk`, {
        method: 'POST',
        body: formData,
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        signal: abortController.signal
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Chunk upload failed: ${response.statusText}`);
      }

      const result = await response.json();
      return result;
    } finally {
      this.activeUploads.delete(`${uploadId}-chunk-${chunkIndex}`);
    }
  }

  // Complete chunked upload
  async completeChunkedUpload(uploadId: string): Promise<FileInfo> {
    const result = await apiService.post<FileInfo>(`/files/upload/chunked/${uploadId}/complete`);
    return result;
  }

  // Get upload progress
  async getUploadProgress(uploadId: string): Promise<UploadProgress> {
    const result = await apiService.get<UploadProgress>(`/files/upload/chunked/${uploadId}/progress`);
    return result;
  }

  // Resumable upload with automatic retry
  async resumableUpload(
    file: File,
    folderId?: string,
    tags?: string[],
    onProgress?: (progress: number) => void,
    onChunkComplete?: (chunkIndex: number, totalChunks: number) => void
  ): Promise<FileInfo> {
    const validation = this.validateFile(file);
    if (!validation.valid) {
      throw new Error(validation.error);
    }

    // For small files, use regular upload
    if (file.size <= CHUNK_SIZE) {
      return this.uploadFile(file, folderId, tags, onProgress);
    }

    // Initialize chunked upload
    const uploadId = await this.initChunkedUpload(file, folderId, tags);
    const totalChunks = Math.ceil(file.size / CHUNK_SIZE);

    try {
      // Upload chunks with retry logic
      for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
        let retryCount = 0;
        const maxRetries = 3;

        while (retryCount < maxRetries) {
          try {
            const result = await this.uploadChunk(uploadId, file, chunkIndex);
            
            if (onProgress) {
              onProgress(result.progress);
            }
            
            if (onChunkComplete) {
              onChunkComplete(chunkIndex, totalChunks);
            }
            
            break; // Success, move to next chunk
          } catch (error) {
            retryCount++;
            if (retryCount >= maxRetries) {
              throw new Error(`Failed to upload chunk ${chunkIndex} after ${maxRetries} retries: ${error}`);
            }
            
            // Wait before retry (exponential backoff)
            await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000));
          }
        }
      }

      // Complete the upload
      return await this.completeChunkedUpload(uploadId);
    } catch (error) {
      // Clean up failed upload
      this.cancelUpload(uploadId);
      throw error;
    }
  }

  // Cancel upload
  cancelUpload(uploadId: string): void {
    // Cancel main upload
    const mainController = this.activeUploads.get(uploadId);
    if (mainController) {
      mainController.abort();
      this.activeUploads.delete(uploadId);
    }

    // Cancel all chunk uploads for this upload
    for (const [key, controller] of this.activeUploads.entries()) {
      if (key.startsWith(`${uploadId}-chunk-`)) {
        controller.abort();
        this.activeUploads.delete(key);
      }
    }
  }

  // Get file list
  async getFiles(folderId?: string, pagination?: Pagination): Promise<FileList> {
    const params = new URLSearchParams();
    if (folderId) params.append('folderId', folderId);
    if (pagination?.page) params.append('page', pagination.page.toString());
    if (pagination?.limit) params.append('limit', pagination.limit.toString());
    if (pagination?.sortBy) params.append('sortBy', pagination.sortBy);
    if (pagination?.sortOrder) params.append('sortOrder', pagination.sortOrder);
    if (pagination?.fileType) params.append('fileType', pagination.fileType);

    return apiService.get<FileList>(`/files/list?${params.toString()}`);
  }

  // Download file
  async downloadFile(fileId: string): Promise<Blob> {
    const response = await fetch(`${this.getApiBaseUrl()}/files/${fileId}/download`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('authToken')}`
      }
    });

    if (!response.ok) {
      throw new Error(`Download failed: ${response.statusText}`);
    }

    return response.blob();
  }

  // Get file preview (optimized for display)
  async getFilePreview(fileId: string, options: {
    quality?: 'low' | 'medium' | 'high';
    maxWidth?: number;
    maxHeight?: number;
  } = {}): Promise<Blob> {
    const params = new URLSearchParams();
    if (options.quality) params.append('quality', options.quality);
    if (options.maxWidth) params.append('maxWidth', options.maxWidth.toString());
    if (options.maxHeight) params.append('maxHeight', options.maxHeight.toString());

    const queryString = params.toString();
    const url = `${this.getApiBaseUrl()}/files/${fileId}/preview${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('authToken')}`
      }
    });

    if (!response.ok) {
      throw new Error(`Preview failed: ${response.statusText}`);
    }

    return response.blob();
  }

  // Get file info
  async getFileInfo(fileId: string): Promise<FileMetadata> {
    return apiService.get<FileMetadata>(`/files/${fileId}/info`);
  }

  // Get folder by ID
  async getFolderById(folderId: string): Promise<Folder> {
    return apiService.get<Folder>(`/files/folders/${folderId}`);
  }

  // Get folder info
  async getFolderInfo(folderId: string): Promise<Folder> {
    return apiService.get<Folder>(`/files/folders/${folderId}/info`);
  }

  // Delete file
  async deleteFile(fileId: string): Promise<void> {
    await apiService.delete(`/files/${fileId}`);
  }

  // Move file
  async moveFile(fileId: string, targetFolderId?: string): Promise<void> {
    await apiService.put(`/files/${fileId}/move`, { targetFolderId });
  }

  // Rename file
  async renameFile(fileId: string, newName: string): Promise<void> {
    await apiService.put(`/files/${fileId}/rename`, { name: newName });
  }

  // Create folder
  async createFolder(folderData: { name: string; parentId?: string }): Promise<any> {
    return apiService.post('/files/folders', folderData);
  }

  // Rename folder
  async renameFolder(folderId: string, newName: string): Promise<void> {
    await apiService.put(`/files/folders/${folderId}/rename`, { name: newName });
  }

  // Move folder
  async moveFolder(folderId: string, targetFolderId?: string): Promise<void> {
    await apiService.put(`/files/folders/${folderId}/move`, { targetFolderId });
  }

  // Delete folder
  async deleteFolder(folderId: string, permanent: boolean = false): Promise<void> {
    const params = permanent ? '?permanent=true' : '';
    await apiService.delete(`/files/folders/${folderId}${params}`);
  }

  // Duplicate file
  async duplicateFile(fileId: string, newName?: string): Promise<FileMetadata> {
    return apiService.post(`/files/${fileId}/duplicate`, { newName });
  }

  // Copy file to folder
  async copyFile(fileId: string, targetFolderId?: string): Promise<FileMetadata> {
    return apiService.post(`/files/${fileId}/copy`, { targetFolderId });
  }

  // Search files with advanced options
  async searchFiles(query: string, options: {
    fileType?: string;
    folderId?: string;
    tags?: string[];
    dateRange?: { from: Date; to: Date };
  } = {}): Promise<FileList> {
    const params = new URLSearchParams();
    params.append('q', query);
    if (options.fileType) params.append('fileType', options.fileType);
    if (options.folderId) params.append('folderId', options.folderId);
    if (options.tags) params.append('tags', options.tags.join(','));
    if (options.dateRange) {
      params.append('from', options.dateRange.from.toISOString());
      params.append('to', options.dateRange.to.toISOString());
    }

    return apiService.get<FileList>(`/files/search?${params.toString()}`);
  }

  // Move file to trash (soft delete)
  async moveFileToTrash(fileId: string): Promise<void> {
    await apiService.put(`/files/${fileId}/trash`);
  }

  // Get trash contents
  async getTrashContents(pagination?: Pagination): Promise<FileList> {
    const params = new URLSearchParams();
    if (pagination?.page) params.append('page', pagination.page.toString());
    if (pagination?.limit) params.append('limit', pagination.limit.toString());

    return apiService.get<FileList>(`/files/trash?${params.toString()}`);
  }

  // Restore file from trash
  async restoreFromTrash(fileId: string): Promise<void> {
    await apiService.put(`/files/${fileId}/restore`);
  }

  // Delete file or folder permanently (for trash)
  async deleteItemPermanently(itemId: string, itemType: 'file' | 'folder'): Promise<void> {
    if (itemType === 'folder') {
      await apiService.delete(`/files/folders/${itemId}?permanent=true`);
    } else {
      await apiService.delete(`/files/${itemId}`);
    }
  }

  // Empty trash
  async emptyTrash(): Promise<{ deletedCount: number }> {
    return apiService.delete('/files/trash/empty');
  }

  // Share file
  async shareFile(fileId: string, options: {
    permissions?: Array<{ type: 'read' | 'write' | 'delete'; granted: boolean }>;
    expiresAt?: Date;
    password?: string;
    maxDownloads?: number;
  }): Promise<any> {
    return apiService.post(`/files/${fileId}/share`, options);
  }

  // Get file shares
  async getFileShares(fileId: string): Promise<any[]> {
    return apiService.get(`/files/${fileId}/shares`);
  }

  // Update share link
  async updateShareLink(shareId: string, updates: any): Promise<void> {
    await apiService.put(`/files/shares/${shareId}`, updates);
  }

  // Revoke share link
  async revokeShareLink(shareId: string): Promise<void> {
    await apiService.delete(`/files/shares/${shareId}`);
  }

  // Get storage statistics
  async getStorageStats(): Promise<{
    user: {
      totalFiles: number;
      totalSize: number;
      storageQuota: number;
      usedPercentage: number;
    };
    system: any;
  }> {
    return apiService.get('/files/stats');
  }

  // Get comprehensive share statistics
  async getComprehensiveShareStats(): Promise<{
    totalShares: number;
    activeShares: number;
    totalDownloads: number;
    recentShares: Array<{
      id: string;
      fileName: string;
      createdAt: Date;
      downloadCount: number;
      isActive: boolean;
    }>;
    downloadsByDay: Array<{
      date: string;
      downloads: number;
    }>;
  }> {
    return apiService.get('/files/shares/stats/comprehensive');
  }

  // Get user shares
  async getUserShares(): Promise<Array<{
    id: string;
    fileId: string;
    fileName: string;
    fileSize: number;
    fileMimeType: string;
    token: string;
    permissions: Array<{ type: 'read' | 'write' | 'delete'; granted: boolean }>;
    downloadCount: number;
    maxDownloads?: number;
    createdAt: Date;
    expiresAt?: Date;
    password?: string;
  }>> {
    return apiService.get('/files/shares');
  }

  // Access public share
  async accessPublicShare(token: string, password?: string): Promise<{
    file: {
      id: string;
      name: string;
      size: number;
      mimeType: string;
      uploadedAt: Date;
      downloadCount: number;
      maxDownloads?: number;
      requiresPassword: boolean;
      isExpired: boolean;
    };
  }> {
    if (password) {
      return apiService.post(`/files/public/${token}`, { password });
    } else {
      return apiService.get(`/files/public/${token}`);
    }
  }

  // Download public share
  async downloadPublicShare(token: string, password?: string): Promise<Blob> {
    const response = await fetch(`${this.getApiBaseUrl()}/files/public/${token}/download`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ password })
    });

    if (!response.ok) {
      throw new Error(`Download failed: ${response.statusText}`);
    }

    return response.blob();
  }

  // Utility methods
  private getApiBaseUrl(): string {
    return (import.meta as any).env?.VITE_API_URL || 'http://localhost:3001/api';
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  getFileIcon(mimeType: string): string {
    if (mimeType.startsWith('image/')) return '🖼️';
    if (mimeType.startsWith('video/')) return '🎥';
    if (mimeType.startsWith('audio/')) return '🎵';
    if (mimeType.includes('pdf')) return '📄';
    if (mimeType.includes('word')) return '📝';
    if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return '📊';
    if (mimeType.includes('zip') || mimeType.includes('rar')) return '🗜️';
    return '📄';
  }
}

export const fileService = FileService.getInstance();