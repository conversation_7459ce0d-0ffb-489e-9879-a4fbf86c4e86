import React, { useState, useEffect } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { Eye, EyeOff, UserPlus, AlertCircle, CheckCircle } from 'lucide-react'
import { useAuthStore } from '../stores'
import { useI18n } from '../contexts/I18nContext'
import ThemeLanguageToggle from '../components/ThemeLanguageToggle'
import { validateRegistrationForm, validateField, getErrorMessage, extractFieldErrors, setTranslationFunction } from '../utils/validation'
import '../styles/auth.css'

const Register: React.FC = () => {
  const navigate = useNavigate()
  const { register, isLoading, error: authError, clearError } = useAuthStore()
  const { t } = useI18n()

  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
  })
  const [fieldErrors, setFieldErrors] = useState<Record<string, string[]>>({})
  const [touched, setTouched] = useState<Record<string, boolean>>({})
  const [isFormValid, setIsFormValid] = useState(false)
  const [registrationSuccess, setRegistrationSuccess] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Clear auth error when component mounts or form data changes
  useEffect(() => {
    if (authError) {
      clearError()
    }
  }, [formData, clearError])

  // Validate form whenever form data changes or language changes
  useEffect(() => {
    // Set the translation function for validation
    setTranslationFunction(t)

    // Basic validation to enable the button - just check if all fields have values
    const hasAllValues = 
      formData.username.trim() !== '' && 
      formData.email.trim() !== '' && 
      formData.password !== '' && 
      formData.confirmPassword !== '';
    
    setIsFormValid(hasAllValues);

    // Full validation for field-specific errors
    const newFieldErrors: Record<string, string[]> = {}

    // Validate each field individually to get specific errors
    const emailValidation = validateField('email', formData.email, formData)
    const usernameValidation = validateField('username', formData.username, formData)
    const passwordValidation = validateField('password', formData.password, formData)
    const confirmPasswordValidation = validateField('confirmPassword', formData.confirmPassword, formData)

    if (emailValidation.errors.length > 0) newFieldErrors.email = emailValidation.errors
    if (usernameValidation.errors.length > 0) newFieldErrors.username = usernameValidation.errors
    if (passwordValidation.errors.length > 0) newFieldErrors.password = passwordValidation.errors
    if (confirmPasswordValidation.errors.length > 0) newFieldErrors.confirmPassword = confirmPasswordValidation.errors

    setFieldErrors(newFieldErrors)
  }, [formData, t]) // Add t as a dependency to re-validate when language changes

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (isSubmitting) return // Prevent double submission

    // Mark all fields as touched
    setTouched({
      username: true,
      email: true,
      password: true,
      confirmPassword: true
    })

    // Basic validation - just check if all fields have values
    if (!formData.username.trim() || !formData.email.trim() || !formData.password || !formData.confirmPassword) {
      // Focus on first empty field
      if (!formData.username.trim()) {
        document.getElementById('username')?.focus();
      } else if (!formData.email.trim()) {
        document.getElementById('email')?.focus();
      } else if (!formData.password) {
        document.getElementById('password')?.focus();
      } else if (!formData.confirmPassword) {
        document.getElementById('confirmPassword')?.focus();
      }
      return;
    }

    setIsSubmitting(true)

    try {
      // Clear any previous errors
      clearError()

      const result = await register(formData.username.trim(), formData.email.trim(), formData.password)

      // Registration successful - show success message briefly before redirect
      if (result && result.success) {
        setRegistrationSuccess(true)

        // Track registration success event
        try {
          // Analytics tracking could be added here
          console.log('Registration successful event tracked')
        } catch (analyticsError) {
          console.error('Failed to track registration event:', analyticsError)
        }

        // Auto-login successful, redirect to dashboard after showing success message
        // Use a short delay to show the success message, then redirect
        setTimeout(() => {
          // Force navigation to dashboard by using window.location instead of navigate
          // This bypasses the PublicRoute redirect logic
          navigate('/dashboard')
        }, 2000) // 2 seconds to show success message
      }
    } catch (err: any) {
      console.error('Registration error:', err)

      // Handle field-specific validation errors
      const apiFieldErrors = extractFieldErrors(err)
      if (Object.keys(apiFieldErrors).length > 0) {
        setFieldErrors(prev => ({
          ...prev,
          ...apiFieldErrors
        }))

        // Focus on first error field
        const firstErrorField = Object.keys(apiFieldErrors)[0]
        if (firstErrorField) {
          const element = document.getElementById(firstErrorField)
          element?.focus()
        }
      }

      // Error is also handled by AuthContext and displayed via authError
      // Scroll to top to show error message
      window.scrollTo({ top: 0, behavior: 'smooth' })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const { name } = e.target
    setTouched(prev => ({
      ...prev,
      [name]: true
    }))
  }

  const getFieldError = (fieldName: string): string | null => {
    if (!touched[fieldName] || !fieldErrors[fieldName]) return null
    return fieldErrors[fieldName][0] // Return first error
  }

  const isFieldValid = (fieldName: string): boolean => {
    if (!touched[fieldName]) return true
    return !fieldErrors[fieldName] || fieldErrors[fieldName].length === 0
  }

  const getPasswordStrength = (password: string): { score: number; label: string; color: string } => {
    if (!password) return { score: 0, label: '', color: '' }

    let score = 0
    const checks = [
      password.length >= 8,
      /[A-Z]/.test(password),
      /[a-z]/.test(password),
      /\d/.test(password),
      /[!@#$%^&*(),.?":{}|<>]/.test(password)
    ]

    score = checks.filter(Boolean).length

    if (score <= 2) return { score, label: t('password.weak'), color: 'text-red-500' }
    if (score === 3) return { score, label: t('password.fair'), color: 'text-yellow-500' }
    if (score === 4) return { score, label: t('password.good'), color: 'text-blue-500' }
    return { score, label: t('password.strong'), color: 'text-green-500' }
  }

  const passwordStrength = getPasswordStrength(formData.password)

  return (
    <div className="min-h-screen bg-primary flex items-center justify-center px-4 relative">
      {/* 主题和语言切换按钮 */}
      <ThemeLanguageToggle className="absolute top-4 right-4" />

      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h1 className="text-3xl md:text-4xl font-bold text-primary mb-2">{t('app.title')}</h1>
          <h2 className="text-xl md:text-2xl font-semibold text-primary">{t('auth.registerSubtitle')}</h2>
          <p className="text-comment mt-2">{t('auth.startStoring')}</p>
        </div>

        <div className="bg-secondary p-6 rounded-lg shadow-lg border border-primary">
          <form className="space-y-6" onSubmit={handleSubmit}>
            {registrationSuccess && (
              <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg flex items-start space-x-2">
                <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                <div className="flex-1">
                  <p className="text-green-700 dark:text-green-300 text-sm font-medium">{t('success.registerSuccess')}</p>
                  <p className="text-green-600 dark:text-green-400 text-sm mt-1">{t('auth.autoLoginRedirect')}</p>
                </div>
              </div>
            )}

            {authError && !registrationSuccess && (
              <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg flex items-start space-x-2">
                <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
                <div className="flex-1">
                  <p className="text-red-700 dark:text-red-300 text-sm font-medium">{t('auth.registrationFailed')}</p>
                  <p className="text-red-600 dark:text-red-400 text-sm mt-1">{getErrorMessage(authError)}</p>
                  {authError && typeof authError === 'object' && 'code' in (authError as any) && (authError as any).code === 'NETWORK_ERROR' && (
                    <p className="text-red-600 dark:text-red-400 text-sm mt-1">
                      {t('error.checkConnection')}
                    </p>
                  )}
                </div>
              </div>
            )}

            <div className="space-y-5">
              <div>
                <label htmlFor="username" className="block text-sm font-medium text-primary mb-2">
                  {t('auth.username')}
                </label>
                <div className="relative">
                  <input
                    id="username"
                    name="username"
                    type="text"
                    required
                    placeholder={t('placeholder.chooseUsername')}
                    value={formData.username}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    className={`auth-input pr-10 ${touched.username
                      ? isFieldValid('username')
                        ? 'border-green-500 focus:border-green-500'
                        : 'border-red-500 focus:border-red-500'
                      : ''
                      }`}
                  />
                  {touched.username && (
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                      {isFieldValid('username') ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <AlertCircle className="h-5 w-5 text-red-500" />
                      )}
                    </div>
                  )}
                </div>
                {getFieldError('username') && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{getFieldError('username')}</p>
                )}
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-primary mb-2">
                  {t('auth.email')}
                </label>
                <div className="relative">
                  <input
                    id="email"
                    name="email"
                    type="email"
                    required
                    placeholder={t('placeholder.enterEmail')}
                    value={formData.email}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    className={`auth-input pr-10 ${touched.email
                      ? isFieldValid('email')
                        ? 'border-green-500 focus:border-green-500'
                        : 'border-red-500 focus:border-red-500'
                      : ''
                      }`}
                  />
                  {touched.email && (
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                      {isFieldValid('email') ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <AlertCircle className="h-5 w-5 text-red-500" />
                      )}
                    </div>
                  )}
                </div>
                {getFieldError('email') && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{getFieldError('email')}</p>
                )}
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-primary mb-2">
                  {t('auth.password')}
                </label>
                <div className="relative">
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    required
                    placeholder={t('auth.createPassword')}
                    value={formData.password}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    className={`auth-input pr-20 ${touched.password
                      ? isFieldValid('password')
                        ? 'border-green-500 focus:border-green-500'
                        : 'border-red-500 focus:border-red-500'
                      : ''
                      }`}
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center">
                    {touched.password && (
                      <div className="pr-2">
                        {isFieldValid('password') ? (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        ) : (
                          <AlertCircle className="h-5 w-5 text-red-500" />
                        )}
                      </div>
                    )}
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="pr-3 flex items-center text-comment hover:text-primary transition-colors"
                    >
                      {showPassword ? (
                        <EyeOff className="h-5 w-5" />
                      ) : (
                        <Eye className="h-5 w-5" />
                      )}
                    </button>
                  </div>
                </div>
                {getFieldError('password') && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{getFieldError('password')}</p>
                )}
                {formData.password && (
                  <div className="mt-2">
                    <div className="h-1 w-full bg-gray-200 rounded-full overflow-hidden">
                      <div 
                        className={`h-full ${
                          passwordStrength.score <= 2 ? 'bg-red-500' : 
                          passwordStrength.score === 3 ? 'bg-yellow-500' : 
                          passwordStrength.score === 4 ? 'bg-blue-500' : 'bg-green-500'
                        }`} 
                        style={{ width: `${passwordStrength.score * 20}%` }}
                      ></div>
                    </div>
                    <p className={`text-xs mt-1 ${passwordStrength.color}`}>
                      {passwordStrength.label}
                    </p>
                  </div>
                )}
              </div>

              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-primary mb-2">
                  {t('auth.confirmPassword')}
                </label>
                <div className="relative">
                  <input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    required
                    placeholder={t('auth.confirmYourPassword')}
                    value={formData.confirmPassword}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    className={`auth-input pr-20 ${touched.confirmPassword
                      ? isFieldValid('confirmPassword')
                        ? 'border-green-500 focus:border-green-500'
                        : 'border-red-500 focus:border-red-500'
                      : ''
                      }`}
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center">
                    {touched.confirmPassword && (
                      <div className="pr-2">
                        {isFieldValid('confirmPassword') ? (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        ) : (
                          <AlertCircle className="h-5 w-5 text-red-500" />
                        )}
                      </div>
                    )}
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="pr-3 flex items-center text-comment hover:text-primary transition-colors"
                    >
                      {showConfirmPassword ? (
                        <EyeOff className="h-5 w-5" />
                      ) : (
                        <Eye className="h-5 w-5" />
                      )}
                    </button>
                  </div>
                </div>
                {getFieldError('confirmPassword') && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{getFieldError('confirmPassword')}</p>
                )}
              </div>
            </div>

            <button
              type="submit"
              disabled={isLoading || isSubmitting || registrationSuccess}
              className="auth-button"
            >
              {isLoading || isSubmitting ? (
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
              ) : registrationSuccess ? (
                <CheckCircle className="w-5 h-5 mr-2" />
              ) : (
                <UserPlus className="w-5 h-5 mr-2" />
              )}
              <span>
                {isLoading || isSubmitting
                  ? t('auth.creatingAccount')
                  : registrationSuccess
                    ? t('success.registerSuccess')
                    : t('auth.createAccount')
                }
              </span>
            </button>

            <div className="text-center">
              <span className="text-comment">{t('auth.alreadyHaveAccount')} </span>
              <Link to="/login" className="auth-link">
                {t('auth.signIn')}
              </Link>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export default Register
