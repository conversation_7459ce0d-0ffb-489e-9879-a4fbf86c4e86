import { apiService } from './api'

export interface ImageBedInfo {
  id: string
  name: string
  isImageBed: boolean
  createdAt: string
  modifiedAt: string
}

export interface PublicImageInfo {
  id: string
  originalName: string
  publicUrl: string
  cdnUrl?: string
  thumbnails?: {
    small?: string
    medium?: string
    large?: string
  }
  accessCount: number
  createdAt: string
}

class ImageBedService {
  /**
   * Get or create the image bed folder for the current user
   */
  async getOrCreateImageBedFolder(): Promise<ImageBedInfo> {
    try {
      // First try to get existing image bed folder
      const response = await apiService.get<ImageBedInfo>('/image-bed/folder')
      return response
    } catch (error) {
      console.error('Error getting image bed folder:', error)
      throw error
    }
  }

  /**
   * Move an image file to the image bed
   */
  async moveImageToImageBed(fileId: string): Promise<PublicImageInfo> {
    const response = await apiService.post<PublicImageInfo>(`/image-bed/move/${fileId}`)
    return response
  }

  /**
   * Get public image information
   */
  async getPublicImageInfo(imageId: string): Promise<PublicImageInfo> {
    const response = await apiService.get<PublicImageInfo>(`/image-bed/public/${imageId}/info`)
    console.log('getPublicImageInfo', response)
    return response
  }

  /**
   * List all images in the image bed
   */
  async listImageBedImages(options?: {
    page?: number
    limit?: number
    sortBy?: 'name' | 'createdAt' | 'accessCount'
    sortOrder?: 'asc' | 'desc'
  }): Promise<{
    images: PublicImageInfo[]
    total: number
    page: number
    limit: number
  }> {
    const params = new URLSearchParams()
    if (options?.page) params.append('page', options.page.toString())
    if (options?.limit) params.append('limit', options.limit.toString())
    if (options?.sortBy) params.append('sortBy', options.sortBy)
    if (options?.sortOrder) params.append('sortOrder', options.sortOrder)

    const queryString = params.toString()
    const response = await apiService.get<{
      images: PublicImageInfo[]
      total: number
      page: number
      limit: number
    }>(`/image-bed/images${queryString ? `?${queryString}` : ''}`)

    return response
  }

  /**
   * Remove image from image bed (move back to regular folder)
   */
  async removeFromImageBed(imageId: string, targetFolderId?: string): Promise<void> {
    await apiService.post(`/image-bed/remove/${imageId}`, {
      targetFolderId
    })
  }

  /**
   * Generate or refresh public link for an image
   */
  async generatePublicLink(imageId: string): Promise<string> {
    const response = await apiService.post<{ publicUrl: string }>(`/image-bed/public-link/${imageId}`)
    return response.publicUrl
  }

  /**
   * Copy public link to clipboard
   */
  async copyPublicLink(imageId: string): Promise<string> {
    const publicUrl = await this.generatePublicLink(imageId)

    try {
      await navigator.clipboard.writeText(publicUrl)
      return publicUrl
    } catch (error) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea')
      textArea.value = publicUrl
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      return publicUrl
    }
  }

  /**
   * Check if a file is an image
   */
  isImageFile(mimeType: string): boolean {
    return mimeType.startsWith('image/')
  }

  /**
   * Upload image directly to image bed
   */
  async uploadToImageBed(formData: FormData): Promise<PublicImageInfo> {
    const response = await apiService.post<PublicImageInfo>('/image-bed/upload', formData)
    return response
  }

  /**
   * Check if a folder is the image bed folder
   */
  async isImageBedFolder(folderId: string): Promise<boolean> {
    try {
      const imageBedFolder = await this.getOrCreateImageBedFolder()
      console.log('Image bed folder:', imageBedFolder)
      return imageBedFolder.id === folderId
    } catch (error) {
      return false
    }
  }

  /**
   * Verify that an uploaded image is in the image bed folder
   */
  async verifyImageInImageBed(imageId: string): Promise<boolean> {
    try {
      const publicImageInfo = await this.getPublicImageInfo(imageId)
      // If we can get public image info, it means the image is in the image bed
      console.log('Image verified in image bed:', publicImageInfo)
      return !!publicImageInfo.publicUrl
    } catch (error) {
      console.error('Failed to verify image in image bed:', error)
      return false
    }
  }
}

export const imageBedService = new ImageBedService()
